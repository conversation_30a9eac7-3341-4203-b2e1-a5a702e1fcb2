import React, { useEffect, useContext, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { AuthContext } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";

const CallbackPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { dispatch } = useContext(AuthContext);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const callbackExecutedRef = useRef(false);

  console.log("callback")

  useEffect(() => {
    console.log("callback")
    const handleCallback = async () => {


      // Only proceed if this is the first execution
      // if (callbackExecutedRef.current) return;
      // callbackExecutedRef.current = true;

      try {
        // Get the 'code' and 'state' from URL query parameters
        const searchParams = new URLSearchParams(location.search);
        const code = searchParams.get("code");
        const data = searchParams.get("data");
        const state = searchParams.get("state");
        const token = searchParams.get("token");
        console.log(code,data,state,token)


        if(token){
          const sdk = new MkdSDK();
          const loginResponse = await sdk.magicLogin({token});
          if(!loginResponse.error){
            dispatch({
              type: "LOGIN",
              payload: {
                user: loginResponse.user_id,
                token: loginResponse.token,
                role: "member"
              }
            });
            showToast(globalDispatch, "Login successful!");

            // Check for community invitation in localStorage (set by social login)
            const communityInvitation = localStorage.getItem("pending_community_invitation");
            const invitationRef = localStorage.getItem("pending_invitation_ref");

            if (communityInvitation && invitationRef) {
              // Clear the stored invitation data
              localStorage.removeItem("pending_community_invitation");
              localStorage.removeItem("pending_invitation_ref");

              // Redirect to communities page with invitation
              navigate(`/member/communities?community_invitation=${communityInvitation}&ref=${invitationRef}`);
            } else {
              navigate("/member/dashboard");
            }
            return;
          }
          throw new Error(loginResponse.message || "Authentication failed");
        }else if(data){
          const dataObj = JSON.parse(data);
          console.log(dataObj,'dataObj');
          const {token, user_id} = dataObj;

          dispatch({
            type: "LOGIN",
            payload: {
              user: user_id,
              token: token,
              role: "member"
            }
          });

          showToast(globalDispatch, "Login successful!");

          // Check for community invitation in localStorage (set by social login)
          const communityInvitation = localStorage.getItem("pending_community_invitation");
          const invitationRef = localStorage.getItem("pending_invitation_ref");

          if (communityInvitation && invitationRef) {
            // Clear the stored invitation data
            localStorage.removeItem("pending_community_invitation");
            localStorage.removeItem("pending_invitation_ref");

            // Redirect to communities page with invitation
            navigate(`/member/communities?community_invitation=${communityInvitation}&ref=${invitationRef}`);
          } else {
            navigate("/member/dashboard");
          }
          return;
        }else if(state){
            // Call the API with the code
            const sdk = new MkdSDK();
            const response = await sdk.socialCallback({
              code,
              state
            });

            if (!response.error) {
              // Store auth data
              localStorage.setItem("token", response.token);
              localStorage.setItem("role", "member");
              console.log(response,'response');

              // Update auth context
              dispatch({
                type: "LOGIN",
                payload: {
                  user: response.user_id || response.id,
                  token: response.token,
                  role: "member"
                }
              });

              // Show success message
              showToast(globalDispatch, "Login successful!");

              // Check for community invitation in localStorage (set by social login)
              const communityInvitation = localStorage.getItem("pending_community_invitation");
              const invitationRef = localStorage.getItem("pending_invitation_ref");

              if (communityInvitation && invitationRef) {
                // Clear the stored invitation data
                localStorage.removeItem("pending_community_invitation");
                localStorage.removeItem("pending_invitation_ref");

                // Redirect to communities page with invitation
                navigate(`/member/communities?community_invitation=${communityInvitation}&ref=${invitationRef}`);
              } else {
                // Redirect to dashboard
                navigate("/member/dashboard");
              }
            } else {
              throw new Error(response.message || "Authentication failed");
            }
        }
      } catch (err) {
        console.error("Google auth error:", err);
        // Redirect to login page with error
        showToast(globalDispatch, err.message || "Authentication failed. Please try again.", 5000, "error");
        navigate("/member/login");
      }
    };

    handleCallback();
  }, [location, navigate, dispatch, globalDispatch]);

  return (
    <div className="w-full min-h-screen bg-black flex flex-col items-center justify-center">
      <div className="text-center flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#7dd87d] mb-4"></div>
        <h2 className="text-xl font-medium text-[#eaeaea]">Authenticating...</h2>
        <p className="mt-1 text-xs text-[#b5b5b5]">Please wait while we complete your sign-in</p>
      </div>
    </div>
  );
};

export default CallbackPage;