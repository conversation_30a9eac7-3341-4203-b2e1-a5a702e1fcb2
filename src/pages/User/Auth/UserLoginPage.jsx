import React, { useState, useContext, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { showToast } from "Context/Global";
import { Toast } from "Components/Toast";

const Logo = () => (
  <svg width="30" height="24" viewBox="0 0 30 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Frame">
    <path id="Vector" d="M15.1594 3.99375L10.6219 7.66875C9.86719 8.27812 9.72188 9.375 10.2938 10.1578C10.8984 10.9922 12.075 11.1562 12.8859 10.5234L17.5406 6.90469C17.8688 6.65156 18.3375 6.70781 18.5953 7.03594C18.8531 7.36406 18.7922 7.83281 18.4641 8.09062L17.4844 8.85L24 14.85V6H23.9672L23.7844 5.88281L20.3812 3.70312C19.6641 3.24375 18.825 3 17.9719 3C16.95 3 15.9562 3.35156 15.1594 3.99375ZM16.2281 9.825L13.8047 11.7094C12.3281 12.8625 10.1859 12.5625 9.07969 11.0437C8.03906 9.61406 8.30156 7.61719 9.675 6.50625L13.575 3.35156C13.0312 3.12187 12.4453 3.00469 11.85 3.00469C10.9688 3 10.1109 3.2625 9.375 3.75L6 6V16.5H7.32187L11.6063 20.4094C12.525 21.2484 13.9453 21.1828 14.7844 20.2641C15.0422 19.9781 15.2156 19.6453 15.3047 19.2984L16.1016 20.0297C17.0156 20.8687 18.4406 20.8078 19.2797 19.8937C19.4906 19.6641 19.6453 19.3969 19.7438 19.1203C20.6531 19.7297 21.8906 19.6031 22.6547 18.7687C23.4937 17.8547 23.4328 16.4297 22.5187 15.5906L16.2281 9.825ZM0.75 6C0.3375 6 0 6.3375 0 6.75V16.5C0 17.3297 0.670312 18 1.5 18H3C3.82969 18 4.5 17.3297 4.5 16.5V6H0.75ZM2.25 15C2.44891 15 2.63968 15.079 2.78033 15.2197C2.92098 15.3603 3 15.5511 3 15.75C3 15.9489 2.92098 16.1397 2.78033 16.2803C2.63968 16.421 2.44891 16.5 2.25 16.5C2.05109 16.5 1.86032 16.421 1.71967 16.2803C1.57902 16.1397 1.5 15.9489 1.5 15.75C1.5 15.5511 1.57902 15.3603 1.71967 15.2197C1.86032 15.079 2.05109 15 2.25 15ZM25.5 6V16.5C25.5 17.3297 26.1703 18 27 18H28.5C29.3297 18 30 17.3297 30 16.5V6.75C30 6.3375 29.6625 6 29.25 6H25.5ZM27 15.75C27 15.5511 27.079 15.3603 27.2197 15.2197C27.3603 15.079 27.5511 15 27.75 15C27.9489 15 28.1397 15.079 28.2803 15.2197C28.421 15.3603 28.5 15.5511 28.5 15.75C28.5 15.9489 28.421 16.1397 28.2803 16.2803C28.1397 16.421 27.9489 16.5 27.75 16.5C27.5511 16.5 27.3603 16.421 27.2197 16.2803C27.079 16.1397 27 15.9489 27 15.75Z" fill="#7DD87D"/>
    </g>
  </svg>
)


const UserLoginPage = () => {
  const schema = yup.object({
    email: yup.string().email("Invalid email").required("Email is required"),
    password: yup.string().required("Password is required"),
  });

  const { state: authState, dispatch } = useContext(AuthContext);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [invitationInfo, setInvitationInfo] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(schema)
  });

  // Check for community invitation parameters and handle authentication
  useEffect(() => {
    const handleCommunityInvitation = async () => {
      const params = new URLSearchParams(location.search);
      const ref = params.get("ref");
      const communityId = params.get("community");

      if (ref && communityId) {
        // Check if user is already logged in
        const token = localStorage.getItem("token");

        if (token && authState?.isAuthenticated) {
          // User is logged in, redirect to communities page with invitation
          navigate(`/member/communities?community_invitation=${communityId}&ref=${ref}`);
          return;
        }

        // User is not logged in, fetch community info to show invitation message
        try {
          const sdk = new MkdSDK();
          const response = await sdk.GetCommunityDetail(communityId);

          if (!response.error) {
            setInvitationInfo({
              community: response.model,
              ref: ref,
              communityId: communityId
            });
          }
        } catch (error) {
          console.error("Error fetching community details:", error);
        }
      }
    };

    handleCommunityInvitation();
  }, [location.search, authState?.isAuthenticated, navigate]);

  const onSubmit = async (data) => {
    try {
      setIsLoading(true);
      setError("");

      const sdk = new MkdSDK();
      const response = await sdk.login({
        email: data.email,
        password: data.password,
        role: "member"
      });

      console.log("Login response:", response); // Debug log

      if (!response.error) {
        // Store auth data
        localStorage.setItem("token", response.token);
        localStorage.setItem("role", "member");

        // Update auth context
        dispatch({
          type: "LOGIN",
          payload: {
            user: response.user_id,
            token: response.token,
            role: "member"
          }
        });

        // Show success message
        showToast(globalDispatch, "Login successful!");

        // Check if there's a community invitation to handle
        if (invitationInfo) {
          navigate(`/member/communities?community_invitation=${invitationInfo.communityId}&ref=${invitationInfo.ref}`);
        } else {
          // Redirect to dashboard
          navigate("/member/dashboard");
        }
      }
    } catch (err) {
      console.error("Login error:", err);
      // setError(err.message || "Login failed. Please check your credentials.");
      showToast(globalDispatch, err.message || "Login failed. Please check your credentials.", 5000, "error");
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithGoogle = async () => {
    const sdk = new MkdSDK();
    const params = new URLSearchParams(location.search);
    const ref = params.get("ref");
    const communityId = params.get("community");

    // Store community invitation data for after social login
    if (ref && communityId) {
      localStorage.setItem("pending_community_invitation", communityId);
      localStorage.setItem("pending_invitation_ref", ref);
    }

    await sdk.LoginWithGoogle({ referral_code: ref });
  };

  const loginWithMicrosoft = async () => {
    const sdk = new MkdSDK();
    const params = new URLSearchParams(location.search);
    const ref = params.get("ref");
    const communityId = params.get("community");

    // Store community invitation data for after social login
    if (ref && communityId) {
      localStorage.setItem("pending_community_invitation", communityId);
      localStorage.setItem("pending_invitation_ref", ref);
    }

    await sdk.LoginWithMicrosoft({ referral_code: ref });
  };
  const loginWithLinkedIn = async () =>{
    const sdk = new MkdSDK();
    const params = new URLSearchParams(location.search);
    const ref = params.get("ref");
    const communityId = params.get("community");

    // Store community invitation data for after social login
    if (ref && communityId) {
      localStorage.setItem("pending_community_invitation", communityId);
      localStorage.setItem("pending_invitation_ref", ref);
    }

    await sdk.LoginWithLinkedIn({ referral_code: ref });

  }
  const loginWithFacebook = async () =>{
    const sdk = new MkdSDK();
    const params = new URLSearchParams(location.search);
    const ref = params.get("ref");
    const communityId = params.get("community");

    // Store community invitation data for after social login
    if (ref && communityId) {
      localStorage.setItem("pending_community_invitation", communityId);
      localStorage.setItem("pending_invitation_ref", ref);
    }

    await sdk.LoginWithFacebook({ referral_code: ref });
  }

  return (
    <div className="w-full min-h-screen bg-[#1E1E1E] flex flex-col">
      {/* Header */}
      <header style={{ marginBottom: "20px" }} className="flex justify-between px-[5vw]  bg-[#161616] h-[62px] items-center py-3">
        <div className="flex items-center gap-2">
          <Logo />
          <span className="text-[16px] font-bold text-[#EAEAEA]">RainmakerOS</span>
        </div>
        <div className="flex items-center gap-[2rem]">
          <a href="/member/login" className="text-[16px] text-[#eaeaea] hover:text-[#7dd87d]">
            Home
          </a>
          {/* <a href="/contact" className="text-[16px] text-[#eaeaea] hover:text-[#7dd87d]">
            Contact Us
          </a>
          <a href="/about" className="text-[16px] text-[#eaeaea] hover:text-[#7dd87d]">
            About Us
          </a> */}
          <a href="/member/signup" className="text-[16px] text-[#eaeaea] hover:text-[#7dd87d]">
            Sign Up
          </a>
        </div>
      </header>

      {/* Login Form */}
      <div style={{ width: "400px" }} className="flex-1 flex flex-col items-center mx-auto px-4">
        <div className="w-full space-y-4">
          <div className="text-center mb-8">
            <h2 className="text-[30px] font-bold text-[#EAEAEA] mt-[1rem]">Welcome Back</h2>
            <p className="mt-1 text-[16px] text-[#B5B5B5]">Please enter your details to continue</p>

            {/* Community Invitation Message */}
            {invitationInfo && (
              <div className="mt-4 p-4 rounded-lg bg-[#2e7d32]/10 border border-[#2e7d32]/30">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <svg className="w-5 h-5 text-[#7dd87d]" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <span className="text-[#7dd87d] font-medium">Community Invitation</span>
                </div>
                <p className="text-[#eaeaea] text-sm">
                  You've been invited to join <span className="font-medium text-[#7dd87d]">{invitationInfo.community?.title?.value}</span>
                </p>
                <p className="text-[#b5b5b5] text-xs mt-1">
                  Please log in to accept the invitation
                </p>
              </div>
            )}
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-4">
              <div>
                <label className="block text-[16px] text-[#EAEAEA] mb-1">Email</label>
                <input
                  type="email"
                  {...register("email")}
                  className="w-full h-[48px] rounded border border-[#363636] bg-[#161616] px-3 text-[#ADAEBC] placeholder:text-[#666666] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]"
                  placeholder="Enter your email"
                />
                {errors.email && (
                  <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>
                )}
              </div>

              <div>
                <label className="block text-[16px] text-[#EAEAEA] mb-1 mt-[1rem]">Password</label>
                <input
                  type="password"
                  {...register("password")}
                  className="w-full h-[48px] rounded border border-[#363636] bg-[#161616] px-3 text-[#ADAEBC] placeholder:text-[#666666] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]"
                  placeholder="Enter your password"
                />
                {errors.password && (
                  <p className="mt-1 text-xs text-red-500">{errors.password.message}</p>
                )}
              </div>

              <div className="flex items-center justify-between mt-[1rem]">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="remember"
                    className="h-4 w-4 rounded border-[#363636] bg-[white] text-[#2e7d32] focus:ring-0"
                  />
                  <label htmlFor="remember" className="ml-2 text-[16px] text-[#B5B5B5]">
                    Remember me
                  </label>
                </div>
                <a href="/member/forgot" className="text-[16px] text-[#B5B5B5] hover:text-[#6bc76b]">
                  Forgot Password?
                </a>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full h-[48px] rounded bg-[#2E7D32] text-[16px] font-medium text-[#EAEAEA] hover:bg-[#266d2a] focus:outline-none disabled:opacity-50"
              >
                {isLoading ? "Signing in..." : "Continue"}
              </button>
            </div>
          </form>

          <div className="text-center text-[16px] text-[#B5B5B5] my-4 pt-[2rem]">Or continue with</div>

          <div className="grid grid-cols-2 gap-3">
            <button onClick={loginWithGoogle} className="flex items-center justify-center gap-2 rounded border border-[#7DD87D] h-[50px] py-1.5 hover:bg-[#1e1e1e]">
            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 16 16" fill="none">
  <g clip-path="url(#clip0_1_84)">
    <path d="M15.8281 8.18125C15.8281 12.6031 12.8 15.75 8.32812 15.75C4.04063 15.75 0.578125 12.2875 0.578125 8C0.578125 3.7125 4.04063 0.25 8.32812 0.25C10.4156 0.25 12.1719 1.01562 13.525 2.27813L11.4156 4.30625C8.65625 1.64375 3.525 3.64375 3.525 8C3.525 10.7031 5.68437 12.8938 8.32812 12.8938C11.3969 12.8938 12.5469 10.6938 12.7281 9.55313H8.32812V6.8875H15.7063C15.7781 7.28437 15.8281 7.66562 15.8281 8.18125Z" fill="#EAEAEA"/>
  </g>
  <defs>
    <clipPath id="clip0_1_84">
      <path d="M0.578125 0H15.8281V16H0.578125V0Z" fill="white"/>
    </clipPath>
  </defs>
</svg>
              <span className="text-[16px] text-[#EAEAEA]">Google</span>
            </button>
            <button onClick={loginWithFacebook} className="flex items-center justify-center gap-2 rounded border border-[#7DD87D] h-[50px] py-1.5 hover:bg-[#1e1e1e]">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 17 16" fill="none">
  <g clip-path="url(#clip0_1_87)">
    <path d="M16.6094 8C16.6094 3.71875 13.1406 0.25 8.85938 0.25C4.57812 0.25 1.10938 3.71875 1.10938 8C1.10938 11.8681 3.94344 15.0744 7.64844 15.6562V10.2403H5.67969V8H7.64844V6.2925C7.64844 4.35031 8.80469 3.2775 10.5756 3.2775C11.4237 3.2775 12.3106 3.42875 12.3106 3.42875V5.335H11.3331C10.3706 5.335 10.0703 5.9325 10.0703 6.54531V8H12.2197L11.8759 10.2403H10.0703V15.6562C13.7753 15.0744 16.6094 11.8681 16.6094 8Z" fill="#EAEAEA"/>
  </g>
  <defs>
    <clipPath id="clip0_1_87">
      <path d="M0.859375 0H16.8594V16H0.859375V0Z" fill="white"/>
    </clipPath>
  </defs>
</svg>
              <span className="text-[16px] text-[#eaeaea]">Facebook</span>
            </button>
            <button onClick={loginWithMicrosoft} className="flex items-center justify-center gap-2 rounded border border-[#7DD87D] h-[50px] py-1.5 hover:bg-[#1e1e1e]">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="16" viewBox="0 0 15 16" fill="none">
              <g clip-path="url(#clip0_1_90)">
                <path d="M0.546875 1H7.25313V7.70625H0.546875V1ZM7.84062 1H14.5469V7.70625H7.84062V1ZM0.546875 8.29375H7.25313V15H0.546875V8.29375ZM7.84062 8.29375H14.5469V15H7.84062V8.29375Z" fill="#EAEAEA"/>
              </g>
              <defs>
                <clipPath id="clip0_1_90">
                  <path d="M0.546875 0H14.5469V16H0.546875V0Z" fill="white"/>
                </clipPath>
              </defs>
            </svg>
              <span className="text-[16px] text-[#eaeaea]">Microsoft</span>
            </button>
            <button onClick={loginWithLinkedIn} className="flex items-center justify-center gap-2 rounded border border-[#7DD87D] h-[50px] py-1.5 hover:bg-[#1e1e1e]">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="16" viewBox="0 0 15 16" fill="none">
              <g clip-path="url(#clip0_1_93)">
                <path d="M13.75 1H1.74687C1.19687 1 0.75 1.45313 0.75 2.00938V13.9906C0.75 14.5469 1.19687 15 1.74687 15H13.75C14.3 15 14.75 14.5469 14.75 13.9906V2.00938C14.75 1.45313 14.3 1 13.75 1ZM4.98125 13H2.90625V6.31875H4.98438V13H4.98125ZM3.94375 5.40625C3.27812 5.40625 2.74063 4.86562 2.74063 4.20312C2.74063 3.54063 3.27812 3 3.94375 3C4.60625 3 5.14687 3.54063 5.14687 4.20312C5.14687 4.86875 4.60938 5.40625 3.94375 5.40625ZM12.7594 13H10.6844V9.75C10.6844 8.975 10.6687 7.97813 9.60625 7.97813C8.525 7.97813 8.35938 8.82188 8.35938 9.69375V13H6.28438V6.31875H8.275V7.23125H8.30312C8.58125 6.70625 9.25938 6.15312 10.2688 6.15312C12.3687 6.15312 12.7594 7.5375 12.7594 9.3375V13Z" fill="#EAEAEA"/>
              </g>
              <defs>
                <clipPath id="clip0_1_93">
                  <path d="M0.75 0H14.75V16H0.75V0Z" fill="white"/>
                </clipPath>
              </defs>
            </svg>
              <span className="ttext-[16px] text-[#eaeaea]">LinkedIn</span>
            </button>
          </div>

          <div className="text-center text-[16px] text-[#B5B5B5] pt-[1rem]">
            Or use a <a href="/member/magic-login-page" className="text-[#7dd87d] hover:text-[#6bc76b]">Magic Link</a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserLoginPage;
