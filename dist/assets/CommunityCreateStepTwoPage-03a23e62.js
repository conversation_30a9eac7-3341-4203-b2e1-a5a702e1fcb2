import{j as e}from"./@react-google-maps/api-211df1ae.js";import{d as c,u as d,r as l}from"./vendor-1c28ea83.js";import"./index-b3edd152.js";import"./pdf-lib-623decea.js";import"./react-toggle-58b0879a.js";/* empty css                 */import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./@uppy/dashboard-3a4b1704.js";const T=()=>{const a=c(),i=d(),{formData:s}=i.state||{},[t,o]=l.useState({...s,description:"",industry_description:""}),m=()=>{a("/member/communities/create/step3",{state:{formData:t}})},n=()=>{a("/member/communities/create",{state:{formData:s}})};return e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-4 md:p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:"Community Description"}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Step 2 of 3"})]}),e.jsx("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-[#eaeaea]",children:"Description"}),e.jsx("textarea",{value:t.description,onChange:r=>o({...t,description:r.target.value}),className:"mt-1 block w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-2.5 text-[#eaeaea]",rows:4,required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-[#eaeaea]",children:"Industry Description"}),e.jsx("textarea",{value:t.industry_description,onChange:r=>o({...t,industry_description:r.target.value}),className:"mt-1 block w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-2.5 text-[#eaeaea]",rows:4})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{onClick:n,className:"rounded-lg border border-[#363636] px-4 py-2 text-[#eaeaea]",children:"Back"}),e.jsx("button",{onClick:m,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea]",children:"Next Step"})]})]})})]})};export{T as default};
