import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as u,r as w,d as P,f as C}from"./vendor-1c28ea83.js";import{u as D}from"./react-hook-form-eec8b32f.js";import{M as T,A as L,G as z,s as h,t as _,S as $,o as R}from"./index-b3edd152.js";import{c as U,a as d,e as p}from"./yup-1b5612ec.js";import"./pdf-lib-623decea.js";import"./react-toggle-58b0879a.js";/* empty css                 */import{I as q}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./@uppy/dashboard-3a4b1704.js";let x=new T;const xe=n=>{const{dispatch:g}=u.useContext(L),E=U({email:d().required("Email is required"),password:d(),login_type:p().transform(s=>s===""?1:Number(s)),role_id:d().required("Role is required"),first_name:d().required("First name is required"),last_name:d(),phone:d(),industry_id:d(),address:d(),city:d(),state:d(),zip:d(),country:d(),payout_method:d(),status:p().transform(s=>s===""?1:Number(s)),verify:p().transform(s=>s===""?1:Number(s)),two_factor_authentication:p().transform(s=>s===""?0:Number(s)),company_id:p().transform(s=>s===""?0:Number(s))}).required(),{dispatch:c}=u.useContext(z),[v,f]=u.useState(!1),[F,b]=u.useState(!1),[m,j]=w.useState("account"),[M,A]=w.useState({}),y=P(),{register:t,handleSubmit:S,setError:V,setValue:i,formState:{errors:r}}=D({resolver:R(E)}),o=C(),k=s=>{if(!s)return{};try{return JSON.parse(s)}catch(a){return console.error("Error parsing user data:",a),{}}};u.useEffect(function(){(async function(){try{b(!0),x.setTable("user");const s=await x.callRestAPI({id:n.activeId?n.activeId:Number(o==null?void 0:o.id)},"GET");if(console.log("User data fetched:",s),!s.error){const a=k(s.model.data);A(a),i("email",s.model.email),i("password",""),i("login_type",s.model.login_type),i("role_id",s.model.role_id),i("status",s.model.status),i("verify",s.model.verify),i("two_factor_authentication",s.model.two_factor_authentication||0),i("company_id",s.model.company_id||0),i("first_name",a.first_name||""),i("last_name",a.last_name||""),i("phone",a.phone||""),i("industry_id",a.industry_id||""),i("address",a.address||""),i("city",a.city||""),i("state",a.state||""),i("zip",a.zip||""),i("country",a.country||""),i("payout_method",a.payout_method||""),b(!1)}}catch(s){b(!1),console.log("Error fetching user data:",s),h(c,"Error loading user data"),_(g,s.message)}})()},[]);const I=async s=>{f(!0);try{console.log("Form submission data:",s);const a={first_name:s.first_name||"",last_name:s.last_name||"",email:s.email,phone:s.phone||"",industry_id:s.industry_id||"",address:s.address||"",city:s.city||"",state:s.state||"",zip:s.zip||"",country:s.country||"",payout_method:s.payout_method||""},l={id:n.activeId?n.activeId:Number(o==null?void 0:o.id),email:s.email,role_id:s.role_id};s.password&&s.password.trim()!==""&&(l.password=s.password),l.login_type=s.login_type===""?1:Number(s.login_type),l.data=JSON.stringify(a),l.status=s.status===""?1:Number(s.status),l.verify=s.verify===""?1:Number(s.verify),l.two_factor_authentication=s.two_factor_authentication===""?0:Number(s.two_factor_authentication),l.company_id=s.company_id===""?0:Number(s.company_id),console.log("API payload:",l),x.setTable("user");const N=await x.callRestAPI(l,"PUT");console.log("API response:",N),N.error?h(c,N.message||"Failed to update user"):(h(c,"User updated successfully"),n.setSidebar?(y("/admin/user"),n.setSidebar(!1),c({type:"REFRESH_DATA",payload:{refreshData:!0}})):y(`/admin/view-user/${o==null?void 0:o.id}`)),f(!1)}catch(a){f(!1),console.log("Error",a),h(c,a.message||"An error occurred"),_(g,a.message)}};return u.useEffect(()=>{c({type:"SETPATH",payload:{path:"user"}})},[]),e.jsx("div",{className:"edit-user-page bg-[#1E1E1E] text-white min-h-screen",children:e.jsx("div",{className:"container mx-auto p-6",children:F?e.jsx("div",{className:"bg-[#161616] p-6 rounded",children:e.jsx($,{})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"header mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Edit User"}),e.jsxs("button",{onClick:()=>n.setSidebar?n.setSidebar(!1):y("/admin/user"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Cancel"]})]}),e.jsx("p",{className:"text-[#9ca3af] text-sm",children:"Edit user information"})]}),e.jsxs("div",{className:"content-wrapper bg-[#161616] rounded-lg border border-[#2d2d3d] overflow-hidden w-[80vw] mx-auto",children:[e.jsxs("div",{className:"tabs",children:[e.jsx("div",{className:`tab ${m==="account"?"active":""}`,onClick:()=>j("account"),children:"Account Information"}),e.jsx("div",{className:`tab ${m==="personal"?"active":""}`,onClick:()=>j("personal"),children:"Personal Information"}),e.jsx("div",{className:`tab ${m==="address"?"active":""}`,onClick:()=>j("address"),children:"Address Information"})]}),e.jsxs("form",{className:"p-6",onSubmit:S(I),children:[e.jsx("div",{className:`tab-content ${m==="account"?"active":""}`,children:e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-4",children:"Account Details"}),e.jsxs("div",{className:"form-grid",children:[e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",id:"email",...t("email"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.email&&e.jsx("p",{className:"error",children:r.email.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"password",children:"Password (leave empty to keep current)"}),e.jsx("input",{type:"password",id:"password",...t("password"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full",placeholder:"Enter new password or leave empty"}),r.password&&e.jsx("p",{className:"error",children:r.password.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"role_id",children:"Role"}),e.jsxs("select",{id:"role_id",...t("role_id"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full",children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"member",children:"Member"}),e.jsx("option",{value:"user",children:"User"}),e.jsx("option",{value:"editor",children:"Editor"}),e.jsx("option",{value:"viewer",children:"Viewer"})]}),r.role_id&&e.jsx("p",{className:"error",children:r.role_id.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"login_type",children:"Login Type"}),e.jsx("input",{type:"number",id:"login_type",...t("login_type"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.login_type&&e.jsx("p",{className:"error",children:r.login_type.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"status",children:"Status"}),e.jsxs("select",{id:"status",...t("status"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full",children:[e.jsx("option",{value:"1",children:"Active"}),e.jsx("option",{value:"0",children:"Inactive"})]}),r.status&&e.jsx("p",{className:"error",children:r.status.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"verify",children:"Verification"}),e.jsxs("select",{id:"verify",...t("verify"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full",children:[e.jsx("option",{value:"1",children:"Verified"}),e.jsx("option",{value:"0",children:"Not Verified"})]}),r.verify&&e.jsx("p",{className:"error",children:r.verify.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"two_factor_authentication",children:"Two-Factor Authentication"}),e.jsxs("select",{id:"two_factor_authentication",...t("two_factor_authentication"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full",children:[e.jsx("option",{value:"0",children:"Disabled"}),e.jsx("option",{value:"1",children:"Enabled"})]}),r.two_factor_authentication&&e.jsx("p",{className:"error",children:r.two_factor_authentication.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"company_id",children:"Company ID"}),e.jsx("input",{type:"number",id:"company_id",...t("company_id"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.company_id&&e.jsx("p",{className:"error",children:r.company_id.message})]})]})]})}),e.jsx("div",{className:`tab-content ${m==="personal"?"active":""}`,children:e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-4",children:"Personal Details"}),e.jsxs("div",{className:"form-grid",children:[e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"first_name",children:"First Name"}),e.jsx("input",{type:"text",id:"first_name",...t("first_name"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.first_name&&e.jsx("p",{className:"error",children:r.first_name.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"last_name",children:"Last Name"}),e.jsx("input",{type:"text",id:"last_name",...t("last_name"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.last_name&&e.jsx("p",{className:"error",children:r.last_name.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"phone",children:"Phone"}),e.jsx("input",{type:"text",id:"phone",...t("phone"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.phone&&e.jsx("p",{className:"error",children:r.phone.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"industry_id",children:"Industry"}),e.jsx("input",{type:"text",id:"industry_id",...t("industry_id"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.industry_id&&e.jsx("p",{className:"error",children:r.industry_id.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"payout_method",children:"Payout Method"}),e.jsx("input",{type:"text",id:"payout_method",...t("payout_method"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.payout_method&&e.jsx("p",{className:"error",children:r.payout_method.message})]})]})]})}),e.jsx("div",{className:`tab-content ${m==="address"?"active":""}`,children:e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-4",children:"Address Details"}),e.jsxs("div",{className:"form-grid",children:[e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"address",children:"Address"}),e.jsx("input",{type:"text",id:"address",...t("address"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.address&&e.jsx("p",{className:"error",children:r.address.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"city",children:"City"}),e.jsx("input",{type:"text",id:"city",...t("city"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.city&&e.jsx("p",{className:"error",children:r.city.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"state",children:"State/Province"}),e.jsx("input",{type:"text",id:"state",...t("state"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.state&&e.jsx("p",{className:"error",children:r.state.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"zip",children:"ZIP/Postal Code"}),e.jsx("input",{type:"text",id:"zip",...t("zip"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.zip&&e.jsx("p",{className:"error",children:r.zip.message})]}),e.jsxs("div",{className:"form-field",children:[e.jsx("label",{htmlFor:"country",children:"Country"}),e.jsx("input",{type:"text",id:"country",...t("country"),className:"bg-[#1a1a1a] border border-[#2d2d3d] rounded p-2 w-full"}),r.country&&e.jsx("p",{className:"error",children:r.country.message})]})]})]})}),e.jsx("div",{className:"actions-section mt-6 pt-6 border-t border-[#2d2d3d] flex justify-end",children:e.jsxs(q,{type:"submit",loading:v,disabled:v,className:"submit-button bg-[#22c55e] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Save Changes"]})})]})]})]})})})};export{xe as default};
