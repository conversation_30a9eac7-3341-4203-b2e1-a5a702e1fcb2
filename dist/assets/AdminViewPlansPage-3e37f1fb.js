import{j as s}from"./@react-google-maps/api-211df1ae.js";import{R as i,f as g,d as b}from"./vendor-1c28ea83.js";import{M as v,A as y,G as A,t as k,S as P}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let m=new v;const H=()=>{const{dispatch:x}=i.useContext(y);i.useContext(A);const[e,o]=i.useState({}),[h,r]=i.useState(!0),c=g(),l=b(),n=a=>a?new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",p=(a,t="monthly")=>{if(!a)return"N/A";const N=new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a),f=t.replace("ly","");return`${N}/${f}`};i.useEffect(function(){(async function(){try{r(!0),m.setTable("plan");const a=await m.callRestAPI({id:Number(c==null?void 0:c.id)},"GET");a.error||(o(a.model),r(!1))}catch(a){r(!1),console.log("error",a),k(x,a.message)}})()},[]);const j=a=>a===1?"status active":"status inactive",u=a=>a===1?s.jsx("span",{className:"popular-badge",children:"Popular"}):null,d=a=>{try{return!a||a==="[]"?[]:JSON.parse(a)}catch(t){return console.error("Error parsing features:",t),[]}};return s.jsx("div",{className:"view-plan-page bg-[#1E1E1E] text-white min-h-screen",children:s.jsx("div",{className:"container mx-auto p-6",children:h?s.jsx("div",{className:"bg-[#161616] p-6 rounded",children:s.jsx(P,{})}):s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"header mb-6",children:[s.jsxs("div",{className:"flex justify-between items-center mb-4",children:[s.jsx("h1",{className:"text-2xl font-semibold",children:"Plan Details"}),s.jsxs("button",{onClick:()=>l("/admin/plans"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to List"]})]}),s.jsx("p",{className:"text-[#9ca3af] text-sm",children:"View and manage plan details"})]}),s.jsxs("div",{className:"content-wrapper bg-[#161616] rounded-lg border border-[#2d2d3d] overflow-hidden",children:[s.jsx("div",{className:"header-section p-6 border-b border-[#2d2d3d]",children:s.jsxs("div",{className:"flex justify-between items-start",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"text-xl font-semibold mb-2",children:e==null?void 0:e.name}),s.jsxs("div",{className:"flex items-center gap-4 text-sm text-[#9ca3af]",children:[s.jsxs("div",{children:["Plan ID: #",e==null?void 0:e.id]}),s.jsxs("div",{children:["Created: ",n(e==null?void 0:e.created_at)]})]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("span",{className:j(e==null?void 0:e.is_active),children:(e==null?void 0:e.is_active)===1?"Active":"Inactive"}),u(e==null?void 0:e.is_popular)]})]})}),s.jsx("div",{className:"price-section p-6 border-b border-[#2d2d3d] bg-[#1a1a1a]",children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-[#9ca3af] text-sm mb-1",children:"Price"}),s.jsx("div",{className:"price",children:p(e==null?void 0:e.price,e==null?void 0:e.billing_cycle)})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-[#9ca3af] text-sm mb-1",children:"Billing Cycle"}),s.jsx("div",{className:"capitalize",children:(e==null?void 0:e.billing_cycle)||"Monthly"})]})]})}),s.jsxs("div",{className:"details-grid p-6 grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"left-column space-y-6",children:[s.jsxs("div",{className:"detail-section",children:[s.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Plan Information"}),s.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Plan ID"}),s.jsx("div",{children:(e==null?void 0:e.id)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Name"}),s.jsx("div",{children:(e==null?void 0:e.name)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Description"}),s.jsx("div",{children:(e==null?void 0:e.description)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Status"}),s.jsx("div",{children:(e==null?void 0:e.is_active)===1?"Active":"Inactive"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Popular"}),s.jsx("div",{children:(e==null?void 0:e.is_popular)===1?"Yes":"No"})]})]})]}),d(e==null?void 0:e.features).length>0&&s.jsxs("div",{className:"detail-section",children:[s.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Features"}),s.jsx("div",{className:"bg-[#1a1a1a] rounded-lg p-4",children:s.jsx("ul",{className:"features-list",children:d(e==null?void 0:e.features).map((a,t)=>s.jsxs("li",{className:"feature-item",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-green-500 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),a]},t))})})]})]}),s.jsxs("div",{className:"right-column space-y-6",children:[s.jsxs("div",{className:"detail-section",children:[s.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Trial Information"}),s.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Trial Available"}),s.jsx("div",{children:(e==null?void 0:e.is_trial_available)===1?"Yes":"No"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Trial Days"}),s.jsx("div",{children:(e==null?void 0:e.trial_days)||"N/A"})]})]})]}),s.jsxs("div",{className:"detail-section",children:[s.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Stripe Information"}),s.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Stripe Product ID"}),s.jsx("div",{children:(e==null?void 0:e.stripe_product_id)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Stripe Price ID"}),s.jsx("div",{children:(e==null?void 0:e.stripe_price_id)||"N/A"})]})]})]})]})]}),s.jsxs("div",{className:"dates-section p-6 border-t border-[#2d2d3d]",children:[s.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Dates"}),s.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Created At"}),s.jsx("div",{children:n(e==null?void 0:e.created_at)})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Updated At"}),s.jsx("div",{children:n(e==null?void 0:e.updated_at)})]})]})]}),s.jsx("div",{className:"actions-section p-6 border-t border-[#2d2d3d] flex justify-end gap-4",children:s.jsxs("button",{onClick:()=>l("/admin/plans"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to List"]})})]})]})})})};export{H as default};
