import{j as e}from"./@react-google-maps/api-211df1ae.js";import{f as u,d as g,r as s}from"./vendor-1c28ea83.js";import{M as b,S as h,T as j}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const z=()=>{var o;const{id:i}=u(),m=g(),[t,d]=s.useState(null),[c,p]=s.useState(!0),[r,n]=s.useState("");s.useEffect(()=>{x()},[i]);const x=async()=>{try{const a=await new b().GetMeetingDetails(i);a.error?n(a.message):d(a.data)}catch(l){n(l.message)}finally{p(!1)}};return c?e.jsx("div",{className:"p-4",children:e.jsx(h,{className:"h-64 w-full rounded-lg"})}):t?e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-4 md:p-6",children:[r&&e.jsx(j,{message:r,type:"error"}),e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsx("button",{onClick:()=>m("/member/meetings"),className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:"← Back to Meetings"}),e.jsx("button",{onClick:()=>window.open(t.join_url.value,"_blank"),className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea]",children:"Join Meeting"})]}),e.jsxs("div",{className:"rounded-lg bg-[#242424] p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-[#eaeaea]",children:t.title.value}),e.jsx("p",{className:"mt-2 text-[#b5b5b5]",children:(o=t.description)==null?void 0:o.value})]}),e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 font-medium text-[#eaeaea]",children:"Date & Time"}),e.jsx("p",{className:"text-[#b5b5b5]",children:new Date(t.date.value).toLocaleString()})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 font-medium text-[#eaeaea]",children:"Status"}),e.jsx("span",{className:`rounded-full px-3 py-1 text-sm ${t.status.value==="scheduled"?"bg-[#2e7d32]/20 text-[#7dd87d]":"bg-[#363636] text-[#b5b5b5]"}`,children:t.status.value})]})]})]})]}):e.jsx("div",{className:"p-4 text-[#eaeaea]",children:"Meeting not found"})};export{z as default};
