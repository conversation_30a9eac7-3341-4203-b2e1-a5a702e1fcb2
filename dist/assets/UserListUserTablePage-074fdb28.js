import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as a,r as E,d as M}from"./vendor-1c28ea83.js";import{M as b,A as j,G as y,a0 as C,L as i,E as A,S as D,a3 as T}from"./index-b3edd152.js";import{M as k}from"./index-db36e1ef.js";import{d as v,M as h}from"./index-23846e03.js";import{M as f}from"./index-0cdf3a8c.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";new b;const R=[{header:"Row",accessor:"row"},{header:"Action",accessor:""},{header:"Id",accessor:"id",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Company",accessor:"user_id",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Role",accessor:"role",isSorted:!0,isSortedDesc:!1,mappingExist:!0,mappings:{user:{name:"Employee"}}},{header:"First Name",accessor:"first_name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Last Name",accessor:"last_name",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Email",accessor:"email",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Verified",accessor:"verify",isSorted:!0,isSortedDesc:!1,mappingExist:!0,mappings:{0:{name:"false"},1:{name:"true"}}},{header:"Photo",accessor:"photo",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!0,isSortedDesc:!1,mappingExist:!0,mappings:{0:{name:"Inactive"},1:{name:"Active"},2:{name:"Suspend"}}}],ae=()=>{a.useContext(j);const{dispatch:g}=a.useContext(y),l=E.useRef(null),[d,x]=a.useState(!1),[n,S]=a.useState(!1),[c,w]=a.useState([]),{profile:s}=C(),m=M(),p=(o,r)=>{switch(o){case"add":m(`/member/add-employee/${s==null?void 0:s.id}`);break;case"edit":m(`/member/edit-employee/${r[0]}`);break}},t=(o,r,u=[])=>{switch(console.log("ids >>",u),o){case"add":x(r);break;case"edit":S(r),w(u);break}};return a.useEffect(()=>{g({type:"SETPATH",payload:{path:"employees"}})},[]),e.jsxs(e.Fragment,{children:[s!=null&&s.id?e.jsx(i,{children:e.jsx("div",{className:"grid h-full max-h-full min-h-full w-full min-w-full max-w-full grid-rows-1 rounded bg-white p-5 shadow",children:e.jsx(k,{defaultColumns:R,filterDisplays:[v.COLUMNS],tableRole:"user",table:"user",tableTitle:"Employees",columnModel:"employee",join:["division"],actionId:"id",defaultFilter:["role,cs,user",`dealmaker_user.user_id,in,${s==null?void 0:s.id}`],actions:{view:{show:!1,action:null,multiple:!0},select:{show:!1,action:null,multiple:!0},edit:{show:!0,locations:["buttons"],action:o=>p("edit",o),multiple:!1,children:"Edit",icon:e.jsx(i,{children:e.jsx(A,{className:"h-[.955rem] w-[.955rem]"})})},delete:{show:!0,action:null,multiple:!0},add:{show:!0,action:()=>p("add",!0),multiple:!0,showChildren:!0,children:"Add"},export:{show:!0,action:null,multiple:!0,showText:!1,className:"!p-0 !bg-white !border-0"}},actionPostion:["buttons"],refreshRef:l})})}):e.jsx(D,{count:7,counts:[2,2,3,2,1]}),e.jsx(i,{children:e.jsx(f,{isModalActive:d,closeModalFn:()=>t("add",!1,[]),customMinWidthInTw:"!w-full ",showHeader:!0,title:"Add Campaign",headerContent:e.jsx(h,{onToggleModal:()=>t("add",!1,[])}),children:d&&e.jsx(i,{children:e.jsx(T,{onSuccess:()=>{l.current.click(),t("add",!1,[])}})})})}),e.jsx(i,{children:e.jsx(f,{isModalActive:n,closeModalFn:()=>t("edit",!1,[]),customMinWidthInTw:`${c.length>1?"!w-full":"!w-1/3"}`,showHeader:!0,title:"Edit Campaign",headerContent:e.jsx(h,{onToggleModal:()=>t("edit",!1,[])}),children:n&&e.jsx(i,{children:e.jsx(EditCampaigns,{id:c[0],onSuccess:()=>{l.current.click(),t("edit",!1,[])}})})})})]})};export{ae as default};
