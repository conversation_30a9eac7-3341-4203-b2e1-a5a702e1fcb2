import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as a,d as K}from"./vendor-1c28ea83.js";import{M as q,G as J,A as Q,o as U,t as W,a6 as l}from"./index-b3edd152.js";import{u as X}from"./react-hook-form-eec8b32f.js";import{c as Y,a as d}from"./yup-1b5612ec.js";import{P as Z}from"./index-7ba88dde.js";import{A as ee}from"./index-e2604cb4.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let te=new q;const S=[{header:"Row",accessor:"row"},{header:"Stripe Id",accessor:"stripe_id"},{header:"Product",accessor:"product_name"},{header:"Nickname",accessor:"name"},{header:"Type",accessor:"type",mapping:{one_time:"One Time",recurring:"Recurring",lifetime:"Lifetime"}},{header:"Price",accessor:"amount"},{header:"Trial",accessor:"trial_days"},{header:"Status",accessor:"status",mapping:{0:"Archived",1:"Active"}},{header:"Action",accessor:""}],_e=()=>{var y,w,j,N;const{dispatch:k}=a.useContext(J),{dispatch:P}=a.useContext(Q);a.useState("");const[_,A]=a.useState([]),[n,f]=a.useState(10),[b,C]=a.useState(0),[ae,R]=a.useState(0),[u,T]=a.useState(0),[E,D]=a.useState(!1),[z,I]=a.useState(!1),O=K(),V=Y({stripe_id:d(),name:d(),status:d(),product_name:d(),amount:d(),type:d()}),{register:i,handleSubmit:B,formState:{errors:m}}=X({resolver:U(V)}),F=[{key:"",value:"All"},{key:0,value:"Archived"},{key:1,value:"Active"}],G=[{key:"",value:"All"},{key:"one_time",value:"One time"},{key:"recurring",value:"Recurring"}];function L(t){(async function(){f(t),await c(1,t)})()}function M(){(async function(){await c(u-1>1?u-1:1,n)})()}function $(){(async function(){await c(u+1<=b?u+1:1,n)})()}async function c(t,r,o){try{const s=await te.getStripePrices({page:t,limit:r},o),{list:p,total:x,limit:h,num_pages:v,page:g}=s;A(p),f(+h),C(+v),T(+g),R(+x),D(+g>1),I(+g+1<=+v)}catch(s){console.log("ERROR",s),W(P,s.message)}}const H=t=>{const r=l(t.stripe_id),o=l(t.product_name),s=l(t.name),p=l(t.amount),x=l(t.type),h=l(t.status);c(1,n,{stripe_id:r,product_name:o,name:s,amount:p,type:x,status:h})};return a.useEffect(()=>{k({type:"SETPATH",payload:{path:"prices"}}),async function(){await c(1,n)}()},[]),e.jsxs(e.Fragment,{children:[e.jsxs("form",{className:"mb-10 rounded bg-white p-5 shadow",onSubmit:B(H),children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Search"}),e.jsxs("div",{className:"filter-form-holder mt-10 flex flex-wrap",children:[e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Stripe Id"}),e.jsx("input",{type:"text",placeholder:"Stripe Id",...i("stripe_id"),className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(y=m.stripe_id)==null?void 0:y.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Product"}),e.jsx("input",{type:"text",placeholder:"Product Name",...i("product_name"),className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(w=m.product_name)==null?void 0:w.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...i("name"),className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(j=m.name)==null?void 0:j.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Amount"}),e.jsx("input",{type:"number",placeholder:"Amount",...i("amount"),className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(N=m.amount)==null?void 0:N.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...i("type"),children:G.map(t=>e.jsx("option",{value:t.key,defaultValue:"",children:t.value},t.key))}),e.jsx("p",{className:"text-xs italic text-red-500"})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...i("status"),children:F.map(t=>e.jsx("option",{value:t.key,defaultValue:"",children:t.value},t.key))}),e.jsx("p",{className:"text-xs italic text-red-500"})]})]}),e.jsxs("div",{className:"search-buttons pl-2",children:[e.jsx("button",{type:"submit",className:"mr-2 inline-block rounded bg-blue-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:"Search"}),e.jsx("button",{type:"reset",onClick:()=>c(1,n),className:"inline-block rounded bg-gray-800 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-gray-900 hover:shadow-lg focus:bg-gray-900 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-gray-900 active:shadow-lg",children:"Reset"})]})]}),e.jsxs("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:[e.jsxs("div",{className:"mb-3 flex w-full justify-between text-center  ",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Prices "}),e.jsx(ee,{link:"/admin/add-price"})]}),e.jsx("div",{className:"overflow-x-auto border-b border-gray-200 shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:S.map((t,r)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},r))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:_.map((t,r)=>e.jsx("tr",{children:S.map((o,s)=>o.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"mx-1 inline-block rounded-full bg-green-500 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-green-600 hover:shadow-lg focus:bg-green-600 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-green-700 active:shadow-lg",onClick:()=>{O("/admin/edit-price/"+t.id,{state:t})},children:[" ","Edit"]})},s):o.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:o.mapping[t[o.accessor]]},s):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[o.accessor]},s))},r))})]})})]}),e.jsx(Z,{currentPage:u,pageCount:b,pageSize:n,canPreviousPage:E,canNextPage:z,updatePageSize:L,previousPage:M,nextPage:$})]})};export{_e as default};
