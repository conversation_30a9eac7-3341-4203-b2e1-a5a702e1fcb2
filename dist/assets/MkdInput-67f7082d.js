import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as E}from"./vendor-1c28ea83.js";import{S as M,a as P}from"./index-b3edd152.js";import{_ as q}from"./react-toggle-58b0879a.js";/* empty css                 */const B=({type:o="text",page:y,cols:I="30",rows:z="50",name:n,label:x,errors:t=null,register:c=()=>({}),className:u,placeholder:d,options:f=[],mapping:m=null,disabled:r=!1,value:g=null,onChange:h,loading:S=!1,required:i=!1,customField:p=!1,showErrorMessage:_=!0})=>{var b,j,w,$,k,N,v;const a=E.useId();return e.jsx(e.Fragment,{children:e.jsxs("div",{className:`relative grow ${y==="list"?"w-full pl-2 pr-2 md:w-1/2":""}`,children:[["radio","checkbox","color","toggle"].includes(o)?null:e.jsx(e.Fragment,{children:x&&e.jsxs("label",{className:"mb-2 block cursor-pointer text-[.875rem] font-bold",htmlFor:a,children:[x,i&&e.jsx("sup",{className:"z-[99999] text-[.825rem] text-red-600",children:"*"})]})}),S?e.jsx(M,{count:1,counts:[2],className:"!h-[3rem] !max-h-[3rem] !min-h-[3rem] !gap-0 overflow-hidden rounded-[.625rem] !bg-[#ebebeb] !p-0"}):o==="textarea"?e.jsx(e.Fragment,{children:e.jsx("textarea",{className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 font-inter leading-tight text-black shadow focus:outline-none ${u} ${t&&t[n]&&((b=t[n])!=null&&b.message)?"!border-red-500":"border-gray-200"} ${r?"appearance-none bg-gray-200":""}`,disabled:r,id:a,cols:I,name:n,placeholder:d,rows:z,...c(n,{...i&&p?{required:!0}:null})})}):["radio","checkbox","color","toggle"].includes(o)?e.jsxs("div",{className:"flex h-[1.875rem] items-center gap-2 pb-1 pt-3",children:[["toggle"].includes(o)?e.jsx(q,{className:`toggle_class  ${u}`,disabled:r,icons:!1,...h?{onChange:h}:null,...g?{checked:g}:null}):e.jsx("input",{disabled:r,type:o,id:a,name:n,...g?{value:g}:null,placeholder:d,...c(n,{...i&&p?{required:!0}:null}),className:`focus:shadow-outline !h-4 !w-4 cursor-pointer appearance-none rounded border font-inter leading-tight text-primary shadow focus:outline-none focus:ring-0 ${u} ${t&&t[n]&&((j=t[n])!=null&&j.message)?"!border-red-500":"border-gray-200"} ${o==="color"?"min-h-[3.125rem] min-w-[6.25rem]":""} ${r?"appearance-none bg-gray-200":""}`}),e.jsx("label",{className:"mb-2 block h-full cursor-pointer font-inter text-[.9375rem] font-bold capitalize text-black",htmlFor:a,children:x})]}):o==="dropdown"||o==="select"?e.jsxs("select",{type:o,id:a,disabled:r,placeholder:d,...c(n,{...i&&p?{required:!0}:null}),className:`focus:shadow-outline h-[3rem] w-full appearance-none rounded-[.625rem] border p-[.625rem] px-3 py-2 font-inter leading-tight text-black shadow focus:outline-none focus:ring-0  ${u} ${t&&t[n]&&((w=t[n])!=null&&w.message)?"!border-red-500":"border-gray-200"}  ${r?"appearance-none bg-gray-200":""}`,children:[e.jsx("option",{}),f.map((s,l)=>e.jsx("option",{value:s,children:s},l+1))]}):o==="mapping"?e.jsx(e.Fragment,{children:m?e.jsxs("select",{id:a,disabled:r,...g?{value:g}:null,placeholder:d,...c(n,{...i&&p?{required:!0}:null}),className:`focus:shadow-outline h-[3rem] w-full  rounded-[.625rem] border p-[.625rem] px-3 py-2 font-inter leading-tight text-black shadow focus:outline-none focus:ring-0  ${u} ${t&&t[n]&&(($=t[n])!=null&&$.message)?"!border-red-500":"border-gray-200"} ${r?"appearance-none bg-gray-200":""}`,children:[e.jsx("option",{}),f.map((s,l)=>e.jsx("option",{value:s,children:m[s]},l+1))]}):"Please Pass the mapping e.g {key:value}"}):["number","decimal"].includes(o)?e.jsx("input",{type:o,id:a,disabled:r,placeholder:d,...c(n,{...i&&p?{required:!0}:null}),step:"0.01",min:"0.00",onInput:s=>{let l=s.target.value;l.startsWith(".")&&(l=`0${l}`),/^\d+(\.\d{0,2})?$/.test(l)||(l=l.slice(0,-1)),s.target.value=l},className:`focus:shadow-outline h-[3rem] w-full appearance-none rounded-[.625rem] border p-[.625rem] px-3 py-2 font-inter leading-tight text-black shadow focus:outline-none focus:ring-0 ${u} ${t&&t[n]&&((k=t[n])!=null&&k.message)?"!border-red-500":"border-gray-200"} ${r?"appearance-none bg-gray-200":""}`}):e.jsx("input",{type:o,id:a,disabled:r,placeholder:d,...c(n,{...i&&p?{required:!0}:null}),...o==="number"?{step:"0.01"}:null,min:o==="number"?"0.00":void 0,className:`focus:shadow-outline h-[3rem] w-full appearance-none rounded-[.625rem] border p-[.625rem] px-3 py-2 font-inter leading-tight text-black shadow focus:outline-none focus:ring-0 ${u} ${t&&t[n]&&((N=t[n])!=null&&N.message)?"!border-red-500":"border-gray-200"} ${r?"appearance-none bg-gray-200":""}`}),_&&t&&t[n]&&e.jsx("p",{className:"text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500",children:P((v=t[n])==null?void 0:v.message,{casetype:"capitalize",separator:" "})})]})})};export{B as M};
