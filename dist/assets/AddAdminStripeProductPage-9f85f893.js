import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as i,d as S}from"./vendor-1c28ea83.js";import{u as k}from"./react-hook-form-eec8b32f.js";import{G as x,o as E,M as A,s as h,t as P}from"./index-b3edd152.js";import{c as D,a as b}from"./yup-1b5612ec.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const Z=()=>{var l,m,d,c;const f=D({name:b().required(),description:b().nullable()}).required(),{dispatch:s}=i.useContext(x),{dispatch:g}=i.useContext(x),y=S(),{register:n,handleSubmit:j,setError:N,formState:{errors:o}}=k({resolver:E(f)}),w=async p=>{let v=new A;try{const t=await v.addStripeProduct({name:p.name,description:p.description});if(!t.error)h(s,"Added"),y("/admin/products");else if(t.validation){const u=Object.keys(t.validation);for(let r=0;r<u.length;r++){const a=u[r];console.log(a),N(a,{type:"manual",message:t.validation[a]})}}}catch(t){console.log("Error",t),h(s,t.message),P(s,t.message)}};return i.useEffect(()=>{g({type:"SETPATH",payload:{path:"products"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add a Product"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:j(w),children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...n("name"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(l=o.name)!=null&&l.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(m=o.name)==null?void 0:m.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),e.jsx("input",{type:"text",placeholder:"Description",...n("description"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(d=o.description)!=null&&d.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(c=o.description)==null?void 0:c.message})]}),e.jsx("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{Z as default};
