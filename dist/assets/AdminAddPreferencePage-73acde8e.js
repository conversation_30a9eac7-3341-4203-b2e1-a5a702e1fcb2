import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as i,d as b}from"./vendor-1c28ea83.js";import{u as g}from"./react-hook-form-eec8b32f.js";import{G as _,A as y,o as S,M as j,s as A,t as N}from"./index-b3edd152.js";import{c as P,a as s}from"./yup-1b5612ec.js";import{M as m}from"./MkdInput-67f7082d.js";import{I as E}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const ae=({setSidebar:d})=>{const{dispatch:n}=i.useContext(_),c=P({first_name:s(),last_name:s(),phone:s(),photo:s(),user_id:s().required()}).required(),{dispatch:u}=i.useContext(y),[l,p]=i.useState(!1),h=b(),{register:t,handleSubmit:f,setError:w,formState:{errors:a}}=g({resolver:S(c)}),x=async r=>{p(!0);try{let o=new j;o.setTable("preference"),(await o.callRestAPI({first_name:r.first_name,last_name:r.last_name,phone:r.phone,photo:r.photo,user_id:r.user_id},"POST")).error||(A(n,"Added"),h("/admin/preference"),d(!1),n({type:"REFRESH_DATA",payload:{refreshData:!0}})),p(!1)}catch(o){p(!1),console.log("Error",o),N(u,o.message)}};return i.useEffect(()=>{n({type:"SETPATH",payload:{path:"preference"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Preference"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:f(x),children:[e.jsx(m,{type:"text",page:"add",name:"first_name",errors:a,label:"First_name",placeholder:"First_name",register:t,className:""}),e.jsx(m,{type:"text",page:"add",name:"last_name",errors:a,label:"Last_name",placeholder:"Last_name",register:t,className:""}),e.jsx(m,{type:"text",page:"add",name:"phone",errors:a,label:"Phone",placeholder:"Phone",register:t,className:""}),e.jsx(m,{type:"text",page:"add",name:"photo",errors:a,label:"Photo",placeholder:"Photo",register:t,className:""}),e.jsx(m,{type:"text",page:"add",name:"user_id",errors:a,label:"User_id",placeholder:"User_id",register:t,className:""}),e.jsx(E,{type:"submit",loading:l,disabled:l,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ae as default};
