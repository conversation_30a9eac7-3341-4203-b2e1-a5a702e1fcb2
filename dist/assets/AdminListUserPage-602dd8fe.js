import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as r,d as E,r as j}from"./vendor-1c28ea83.js";import{M as w,A as S,G as A,L as x,E as y,c as N,d as C,e as D}from"./index-b3edd152.js";import{M as _}from"./index-0cdf3a8c.js";import{M as k}from"./index-db36e1ef.js";import{A as F}from"./index.esm-2d1feecf.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";new w;const I=[{header:"Full Name",accessor:"data",dataField:"first_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:(s,n)=>{var o,m;const c=((o=n.data)==null?void 0:o.first_name)||"",u=((m=n.data)==null?void 0:m.last_name)||"",p=`${c.charAt(0)}${u.charAt(0)}`.toUpperCase(),f=`${c} ${u}`,h=["#4F46E5","#22C55E","#EF4444","#F59E0B","#3B82F6"],g=n.id%h.length;return`
        <div class="user-info">
          <div class="user-avatar" style="background-color: ${h[g]}">
            ${p}
          </div>
          <div>${f}</div>
        </div>
      `}},{header:"Email",accessor:"email",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Role",accessor:"role_id",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{admin:"Admin",member:"Member",user:"User",editor:"Editor",viewer:"Viewer"},selected_column:!0,cellRenderer:s=>s?`${typeof s=="string"?s.charAt(0).toUpperCase()+s.slice(1):s}`:"N/A"},{header:"Status",accessor:"verify",isSorted:!1,isSortedDesc:!1,mappingExist:!0,mappings:{0:"Inactive",1:"Active"},selected_column:!0,cellRenderer:s=>s===1||s==="1"?'<span class="status-badge active">Active</span>':'<span class="status-badge inactive">Inactive</span>'}],ie=()=>{r.useContext(S),r.useContext(A);const s=E(),[n,c]=r.useState(!1),[u,p]=r.useState(!1),[f,h]=r.useState();r.useState("");const g=j.useRef(null);r.useState({total:125,active:98,inactive:27});const[d,o]=r.useState({data:[{id:53,email:"<EMAIL>",data:{first_name:"Kevin",last_name:"Pettie"},role_id:"member",verify:1,login_type:1,status:1},{id:52,email:"<EMAIL>",data:{first_name:"Tyrone",last_name:""},role_id:"member",verify:1,login_type:1,status:1},{id:51,email:"fyghjnkn@jsnkam",data:{first_name:"Zainee",last_name:""},role_id:"member",verify:1,login_type:1,status:1},{id:50,email:"<EMAIL>",data:{first_name:"Idera",last_name:""},role_id:"member",verify:1,login_type:1,status:1},{id:49,email:"<EMAIL>",data:{first_name:"Umer",last_name:""},role_id:"admin",verify:1,login_type:1,status:1},{id:48,email:"<EMAIL>",data:{first_name:"-",last_name:""},role_id:"user",verify:1,login_type:1,status:1},{id:47,email:"<EMAIL>",data:{first_name:"not tester",last_name:""},role_id:"member",verify:0,login_type:1,status:0}],loading:!1,page:1,limit:10,pages:13,total:125}),m=async(t,i,a=[])=>{switch(t){case"add":c(i);break;case"edit":p(i),h(a[0]);break;case"delete":await v(a);break}},v=async t=>{try{console.log("Deleting user with ID:",t[0]),o(l=>({...l,loading:!0}));const i=new w;i.setTable("user");const a=await i.callRestAPI({id:t[0]},"DELETE");console.log("Delete result:",a),a!=null&&a.error?o(l=>({...l,loading:!1})):o(l=>({...l,data:l.data.filter(b=>b.id!==t[0]),loading:!1}))}catch(i){console.error("Error deleting user:",i),o(a=>({...a,loading:!1}))}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"users-dashboard bg-[#1E1E1E]",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"header",children:[e.jsx("h1",{children:"Users Dashboard"}),e.jsx("p",{children:"Manage and review all user accounts"})]}),e.jsx("div",{className:"search-add",children:e.jsxs("button",{onClick:()=>m("add",!0),className:"add-user-btn ml-auto",children:[e.jsx("span",{children:"+"})," Add New User"]})}),e.jsx("div",{className:"table-container",children:e.jsx(x,{children:e.jsx(k,{pageUser:!0,columns:I,tableRole:"admin",table:"user",actionId:"id",searchField:"email",actions:{view:{show:!0,action:t=>{console.log("View user with ID:",t[0]),s(`/admin/view-user/${t[0]}`)},multiple:!1,locations:["buttons"],showChildren:!1,children:"View",icon:e.jsx(F,{className:"text-blue-500"})},edit:{show:!0,multiple:!1,action:t=>m("edit",!0,t),locations:["buttons"],showChildren:!1,children:"Edit",icon:e.jsx(y,{stroke:"#4CAF50"})},delete:{show:!0,action:v,multiple:!1,locations:["buttons"],showChildren:!1,children:"Delete",icon:e.jsx(N,{fill:"#E53E3E"})},select:{show:!1,action:null,multiple:!1},add:{show:!0,action:()=>m("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPosition:["buttons"],refreshRef:g,allowSortColumns:!1,externalData:{use:!0,data:d.data,loading:d.loading,page:d.page,limit:d.limit,pages:d.pages,total:d.total,fetch:(t,i,a)=>{console.log("Fetch called with page:",t,"limit:",i,"filter:",a),setTimeout(()=>{o(l=>({...l,loading:!1}))},500)},search:(t,i,a,l)=>{console.log("Search called with:",t,l),setTimeout(()=>{o(b=>({...b,loading:!1}))},500)}}})})}),e.jsxs("div",{className:"pagination",children:[e.jsx("div",{className:"pagination-info",children:"Showing 1 to 7 of 125 entries"}),e.jsxs("div",{className:"pagination-buttons",children:[e.jsx("button",{className:"pagination-button prev",children:"Previous"}),e.jsx("button",{className:"pagination-button next",children:"Next"})]})]})]})}),e.jsx(x,{children:e.jsx(_,{isModalActive:n,closeModalFn:()=>c(!1),children:e.jsx(C,{setSidebar:c})})}),u&&e.jsx(x,{children:e.jsx(_,{isModalActive:u,closeModalFn:()=>p(!1),children:e.jsx(D,{activeId:f,setSidebar:p})})})]})};export{ie as default};
