import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as a,d as S,f as j}from"./vendor-1c28ea83.js";import{u as A}from"./react-hook-form-eec8b32f.js";import{M as I,A as N,G as w,t as y,S as T,o as R,s as P}from"./index-b3edd152.js";import{c as k,a as p}from"./yup-1b5612ec.js";import{M as f}from"./MkdInput-67f7082d.js";import{I as q}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let r=new I;const le=i=>{const{dispatch:v}=a.useContext(N),x=k({user_id:p().required(),event_details:p().required(),event_type:p().required()}).required(),{dispatch:o}=a.useContext(w),[_,d]=a.useState(!1),[h,l]=a.useState(!1),g=S(),{register:n,handleSubmit:b,setError:C,setValue:m,formState:{errors:c}}=A({resolver:R(x)}),s=j();a.useEffect(function(){(async function(){try{l(!0),r.setTable("activity_feed");const e=await r.callRestAPI({id:i.activeId?i.activeId:Number(s==null?void 0:s.id)},"GET");e.error||(m("user_id",e.model.user_id),m("event_details",e.model.event_details),m("event_type",e.model.event_type),l(!1))}catch(e){l(!1),console.log("error",e),y(v,e.message)}})()},[]);const E=async e=>{d(!0);try{r.setTable("activity_feed"),(await r.callRestAPI({id:i.activeId?i.activeId:Number(s==null?void 0:s.id),user_id:e.user_id,event_details:e.event_details,event_type:e.event_type},"PUT")).error||(P(o,"Updated"),g("/admin/activity_feed"),i.setSidebar(!1),o({type:"REFRESH_DATA",payload:{refreshData:!0}})),d(!1)}catch(u){d(!1),console.log("Error",u),y(v,u.message)}};return a.useEffect(()=>{o({type:"SETPATH",payload:{path:"activity_feed"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Activity_feed"}),h?t.jsx(T,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:b(E),children:[t.jsx(f,{type:"text",page:"edit",name:"user_id",errors:c,label:"User_id",placeholder:"User_id",register:n,className:""}),t.jsx(f,{type:"text",page:"edit",name:"event_details",errors:c,label:"Event_details",placeholder:"Event_details",register:n,className:""}),t.jsx(f,{type:"text",page:"edit",name:"event_type",errors:c,label:"Event_type",placeholder:"Event_type",register:n,className:""}),t.jsx(q,{type:"submit",loading:_,disabled:_,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{le as default};
