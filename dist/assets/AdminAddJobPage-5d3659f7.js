import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as m,d as g}from"./vendor-1c28ea83.js";import{u as h}from"./react-hook-form-eec8b32f.js";import{G as j,A as y,o as S,M as A,s as v,t as k}from"./index-b3edd152.js";import{c as N,a as o}from"./yup-1b5612ec.js";import{M as i}from"./MkdInput-67f7082d.js";import{I as T}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const se=({setSidebar:d})=>{const{dispatch:l}=m.useContext(j),u=N({task:o().required(),arguments:o(),time_interval:o(),retries:o(),status:o()}).required(),{dispatch:c}=m.useContext(y),[p,n]=m.useState(!1),x=g(),{register:t,handleSubmit:b,setError:E,formState:{errors:s}}=h({resolver:S(u)}),f=async a=>{n(!0);try{let r=new A;r.setTable("job"),(await r.callRestAPI({task:a.task,arguments:a.arguments,time_interval:a.time_interval,retries:a.retries,status:a.status},"POST")).error||(v(l,"Added"),x("/admin/job"),d(!1),l({type:"REFRESH_DATA",payload:{refreshData:!0}})),n(!1)}catch(r){n(!1),console.log("Error",r),k(c,r.message)}};return m.useEffect(()=>{l({type:"SETPATH",payload:{path:"job"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Job"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:b(f),children:[e.jsx(i,{type:"text",page:"add",name:"task",errors:s,label:"Task",placeholder:"Task",register:t,className:""}),e.jsx(i,{type:"text",page:"add",name:"arguments",errors:s,label:"Arguments",placeholder:"Arguments",register:t,className:""}),e.jsx(i,{type:"text",page:"add",name:"time_interval",errors:s,label:"Time_interval",placeholder:"Time_interval",register:t,className:""}),e.jsx(i,{type:"text",page:"add",name:"retries",errors:s,label:"Retries",placeholder:"Retries",register:t,className:""}),e.jsx(i,{type:"text",page:"add",name:"status",errors:s,label:"Status",placeholder:"Status",register:t,className:""}),e.jsx(T,{type:"submit",loading:p,disabled:p,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{se as default};
