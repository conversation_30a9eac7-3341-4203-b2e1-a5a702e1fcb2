import{j as s}from"./@react-google-maps/api-211df1ae.js";import{R as i,f as n}from"./vendor-1c28ea83.js";import{M as o,A as x,G as f,t as p,S as h}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let m=new o;const F=()=>{const{dispatch:l}=i.useContext(x);i.useContext(f);const[e,d]=i.useState({}),[c,a]=i.useState(!0),r=n();return i.useEffect(function(){(async function(){try{a(!0),m.setTable("activity_feed");const t=await m.callRestAPI({id:Number(r==null?void 0:r.id),join:""},"GET");t.error||(d(t.model),a(!1))}catch(t){a(!1),console.log("error",t),p(l,t.message)}})()},[]),s.jsx("div",{className:"shadow-md rounded mx-auto p-5",children:c?s.jsx(h,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Activity_feed"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Updated_at"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.updated_at})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"User_id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.user_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Event_details"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.event_details})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Event_type"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.event_type})]})})]})})};export{F as default};
