import{j as e}from"./@react-google-maps/api-211df1ae.js";import{d as q,u as $,r as l}from"./vendor-1c28ea83.js";import{M as u,T as ee}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const te=()=>e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M17.5 9.375L11.25 15.625C10.2083 16.6667 8.89583 17.1875 7.3125 17.1875C5.72917 17.1875 4.41667 16.6667 3.375 15.625C2.33333 14.5833 1.8125 13.2708 1.8125 11.6875C1.8125 10.1042 2.33333 8.79167 3.375 7.75L10.9375 0.1875C11.6458 -0.520833 12.5104 -0.875 13.5312 -0.875C14.5521 -0.875 15.4167 -0.520833 16.125 0.1875C16.8333 0.895833 17.1875 1.76042 17.1875 2.78125C17.1875 3.80208 16.8333 4.66667 16.125 5.375L8.5625 12.9375C8.20833 13.2917 7.78125 13.4688 7.28125 13.4688C6.78125 13.4688 6.35417 13.2917 6 12.9375C5.64583 12.5833 5.46875 12.1562 5.46875 11.6562C5.46875 11.1562 5.64583 10.7292 6 10.375L12.8125 3.5625",stroke:"#B5B5B5","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),we=()=>{var T,S,H,M,L,A;const j=q(),o=$(),[v,c]=l.useState(""),[P,h]=l.useState(!1),[f,I]=l.useState([]),[R,N]=l.useState([]),[E,_]=l.useState(!1),[x,D]=l.useState(((T=o.state)==null?void 0:T.referralType)||""),[ae,y]=l.useState(((S=o.state)==null?void 0:S.communityId)||""),[s,w]=l.useState({}),[g,C]=l.useState(!1),[G,Z]=l.useState(""),[B,k]=l.useState(""),[W,Y]=l.useState([]),[r,b]=l.useState({title:"",type:"",industry:"",description:"",know_client:"",deal_size:"",referral_fee:"",payment_method:"",referral_type:((H=o.state)==null?void 0:H.referralType)||"",community:((M=o.state)==null?void 0:M.communityId)||"",direct_person:"",additional_notes:"",requirements:"",description_image:"",expiration_date:""});l.useEffect(()=>{U(),J()},[]),l.useEffect(()=>{var t,a;(t=o.state)!=null&&t.communityId&&((a=o.state)==null?void 0:a.referralType)==="community referral"&&(D("community referral"),y(o.state.communityId),b(i=>({...i,referral_type:"community referral",community:o.state.communityId})))},[o.state]);const Q=new u().callRawAPI("/v1/api/dealmaker/user/recommendations",{},"GET");console.log(Q);const U=async()=>{try{const a=await new u().GetJoinedCommunities();a.error?c(a.message):I(a.list||[])}catch(t){c(t.message||"Failed to load communities")}},J=async()=>{try{const a=await new u().callRawAPI("/v1/api/dealmaker/industries",{},"GET");!a.error&&a.data?Y(a.data):c(a.message||"Failed to load industries")}catch(t){c(t.message||"Failed to load industries")}};l.useEffect(()=>{f.length===0&&I([{id:{value:1},title:{value:"Rain Maker LLC"}}])},[f]);const F=async t=>{try{const i=await new u().GetCommunityUsers(t);if(!i.error){const p=(i.list||[]).filter(n=>{var d;return((d=n.role)==null?void 0:d.value)!=="admin"});N(p)}}catch(a){console.error("Failed to fetch users:",a),c(a.message||"Failed to fetch users")}},K=t=>{const a=t.target.value;y(a),r.community=a,x==="direct referral"&&(_(!0),F(a),_(!1))},X=t=>{const a=t.target.value;D(a),y(""),N([]),b(i=>({...i,referral_type:a,community:"",direct_person:"",requirements:""}))},O=async t=>{t.preventDefault(),h(!0),c("");const a={title:"Title",type:"Type of Opportunity",description:"Description",deal_size:"Deal Size",referral_fee:"Referral Fee",payment_method:"Payment Method",referral_type:"Referral Type"};(r.referral_type==="community referral"||r.referral_type==="direct referral")&&(a.community="Community"),r.referral_type==="direct referral"&&(a.direct_person="Direct Person");const i={};let p=!1;for(const[n,d]of Object.entries(a))r[n]||(i[n]=`${d} is required`,p=!0);if(r.expiration_date){const n=new Date(r.expiration_date),d=new Date;d.setHours(0,0,0,0),n<d&&(i.expiration_date="Expiration date cannot be in the past",p=!0)}if(w(i),p){h(!1);return}try{const n=new u,d={...r,industry_id:r.industry?parseInt(r.industry,10):null,industry:void 0},z=await n.CreateReferral(d);z.error?c(z.message):j("/member/referrals",{state:{activeTab:"my-referrals"}})}catch(n){c(n.message||"Failed to create referral")}finally{h(!1)}},m=(t,a)=>{b(i=>({...i,[t]:a})),s[t]&&w(i=>({...i,[t]:""}))},V=async t=>{const a=t.target.files[0];if(a)try{C(!0);const i=new u,p=new FormData;p.append("file",a);const n=await i.uploadImage(p);n.url&&(b(d=>({...d,description_image:n.url})),Z(URL.createObjectURL(a)),k(a.name))}catch(i){console.error("Error uploading attachment:",i),c("Failed to upload attachment")}finally{C(!1)}};return e.jsxs("div",{style:{width:"600px",margin:"20px auto"},className:"min-h-screen bg-[#161616] p-6",children:[e.jsx("style",{children:`
          input[type="date"]::-webkit-calendar-picker-indicator {
            filter: invert(1);
            cursor: pointer;
          }
          input[type="date"]::-webkit-calendar-picker-indicator:hover {
            filter: invert(1) brightness(1.2);
          }
        `}),e.jsxs("div",{className:"mx-auto max-w-3xl",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold text-[#eaeaea]",children:"Add Opportunity"}),e.jsxs("form",{onSubmit:O,className:"space-y-5",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Title"}),e.jsx("input",{type:"text",value:r.title,onChange:t=>m("title",t.target.value),placeholder:"Enter referral title",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]"}),s.title&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.title})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Type of Opportunity"}),e.jsxs("select",{value:r.type,onChange:t=>m("type",t.target.value),className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select type"}),e.jsx("option",{value:"looking_for_service",children:"Looking for Service"}),e.jsx("option",{value:"looking_for_product",children:"Looking for Product"}),e.jsx("option",{value:"looking_for_buyer",children:"Looking for Buyer"}),e.jsx("option",{value:"looking_for_investor",children:"Looking for Investor"}),e.jsx("option",{value:"looking_for_partner",children:"Looking for Partner"})]}),s.type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.type})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Industry"}),e.jsxs("select",{value:r.industry,onChange:t=>m("industry",t.target.value),className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select industry"}),W.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),s.industry&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.industry})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Description"}),e.jsxs("div",{className:"relative",children:[e.jsx("textarea",{value:r.description,onChange:t=>m("description",t.target.value),placeholder:"Write your text here...",rows:4,className:"w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea] placeholder-[#666]"}),e.jsxs("div",{className:"absolute bottom-4 right-4 flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"file",onChange:V,className:"hidden",id:"attachment-input",accept:"image/*,.pdf,.doc,.docx",disabled:g}),e.jsx("button",{type:"button",onClick:()=>document.getElementById("attachment-input").click(),className:`text-[#b5b5b5] hover:text-[#eaeaea] ${g?"opacity-50 cursor-not-allowed":""}`,disabled:g,children:e.jsx(te,{})}),g&&e.jsxs("div",{className:"absolute -top-1 -right-1 h-2 w-2",children:[e.jsx("div",{className:"animate-ping absolute h-full w-full rounded-full bg-[#7dd87d] opacity-75"}),e.jsx("div",{className:"rounded-full h-full w-full bg-[#7dd87d]"})]})]}),e.jsx("button",{type:"button",className:"text-[#b5b5b5] hover:text-[#eaeaea]",onClick:()=>{}})]})]}),G&&e.jsxs("div",{className:"mt-2 flex items-center gap-2 rounded-lg border border-[#363636] bg-[#1a1a1a] p-2",children:[e.jsx("div",{className:"flex-1 truncate text-sm text-[#eaeaea]",children:B}),e.jsx("button",{type:"button",onClick:()=>{b(t=>({...t,description_image:""})),Z(""),k("")},className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),s.description&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.description})]}),e.jsx("div",{className:"grid grid-cols-1 gap-4",children:e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Estimated Deal Size"}),e.jsx("input",{type:"text",value:r.deal_size,onChange:t=>m("deal_size",t.target.value),placeholder:"e.g., $500",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]"}),s.deal_size&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.deal_size})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Referral Fee (%)"}),e.jsx("input",{type:"text",value:r.referral_fee,onChange:t=>m("referral_fee",t.target.value),placeholder:"Enter percentage",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]"}),s.referral_fee&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.referral_fee})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Payment Method"}),e.jsxs("select",{value:r.payment_method,onChange:t=>m("payment_method",t.target.value),className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select method"}),e.jsx("option",{value:"bank",children:"Bank Transfer"}),e.jsx("option",{value:"bank",children:"Bank Card"})]}),s.payment_method&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.payment_method})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Referral Type"}),e.jsxs("select",{value:r.referral_type,onChange:X,disabled:((L=o.state)==null?void 0:L.referralType)==="community referral",className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select type"}),e.jsx("option",{value:"open referral",children:"Open Referral"}),e.jsx("option",{value:"community referral",children:"Community Referral"}),e.jsx("option",{value:"direct referral",children:"Direct Referral"})]}),s.referral_type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.referral_type})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx("div",{children:(x==="community referral"||x==="direct referral")&&e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Select Community"}),e.jsxs("select",{value:r.community,onChange:t=>K(t),disabled:(A=o.state)==null?void 0:A.communityId,className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea] placeholder-[#666]",required:!0,children:[e.jsx("option",{value:"",disabled:!0,children:"Select community"}),f.map(t=>e.jsx("option",{value:t.id.value,children:t.title.value},t.id.value))]})]})}),e.jsx("div",{children:x==="direct referral"&&e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Select Direct Person"}),E?e.jsx("div",{className:"h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]",children:e.jsx("div",{className:"flex items-center justify-center",children:e.jsx("div",{style:{marginTop:"8px"},className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-[#eaeaea]"})})}):e.jsxs("select",{value:r.direct_person,onChange:t=>b(a=>({...a,direct_person:t.target.value})),className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea] placeholder-[#666]",children:[e.jsx("option",{value:"",children:"Select person"}),R.map(t=>e.jsx("option",{value:t.id.value,children:t.name.value},t.id.value))]})]})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Expiration Date"}),e.jsx("input",{type:"date",value:r.expiration_date,onChange:t=>m("expiration_date",t.target.value),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",min:new Date().toISOString().split("T")[0]}),e.jsx("p",{className:"mt-1 text-xs text-[#666]",children:"The opportunity will expire on this date. Leave blank for no expiration."}),s.expiration_date&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:s.expiration_date})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Additional Notes"}),e.jsx("textarea",{value:r.additional_notes,onChange:t=>b(a=>({...a,additional_notes:t.target.value})),placeholder:"Write any additional notes here...",rows:4,className:"w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea] placeholder-[#666]"})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{type:"button",onClick:()=>j("/member/referrals",{state:{activeTab:"my-referrals"}}),className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#363636]",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:P,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#2e7d32]/90",children:"Submit Referral"})]})]}),v&&e.jsx(ee,{message:v})]})]})};export{we as default};
