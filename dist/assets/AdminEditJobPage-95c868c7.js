import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as a,d as k,f as E}from"./vendor-1c28ea83.js";import{u as N}from"./react-hook-form-eec8b32f.js";import{M as T,A,G as I,t as b,S as w,o as R,s as P}from"./index-b3edd152.js";import{c as _,a as m}from"./yup-1b5612ec.js";import{M as n}from"./MkdInput-67f7082d.js";import{I as C}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let d=new T;const me=r=>{const{dispatch:g}=a.useContext(A),h=_({task:m().required(),arguments:m(),time_interval:m(),retries:m(),status:m()}).required(),{dispatch:u}=a.useContext(I),[x,c]=a.useState(!1),[v,p]=a.useState(!1),j=k(),{register:o,handleSubmit:y,setError:D,setValue:i,formState:{errors:l}}=N({resolver:R(h)}),s=E();a.useEffect(function(){(async function(){try{p(!0),d.setTable("job");const e=await d.callRestAPI({id:r.activeId?r.activeId:Number(s==null?void 0:s.id)},"GET");e.error||(i("task",e.model.task),i("arguments",e.model.arguments),i("time_interval",e.model.time_interval),i("retries",e.model.retries),i("status",e.model.status),p(!1))}catch(e){p(!1),console.log("error",e),b(g,e.message)}})()},[]);const S=async e=>{c(!0);try{d.setTable("job"),(await d.callRestAPI({id:r.activeId?r.activeId:Number(s==null?void 0:s.id),task:e.task,arguments:e.arguments,time_interval:e.time_interval,retries:e.retries,status:e.status},"PUT")).error||(P(u,"Updated"),j("/admin/job"),r.setSidebar(!1),u({type:"REFRESH_DATA",payload:{refreshData:!0}})),c(!1)}catch(f){c(!1),console.log("Error",f),b(g,f.message)}};return a.useEffect(()=>{u({type:"SETPATH",payload:{path:"job"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Job"}),v?t.jsx(w,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:y(S),children:[t.jsx(n,{type:"text",page:"edit",name:"task",errors:l,label:"Task",placeholder:"Task",register:o,className:""}),t.jsx(n,{type:"text",page:"edit",name:"arguments",errors:l,label:"Arguments",placeholder:"Arguments",register:o,className:""}),t.jsx(n,{type:"text",page:"edit",name:"time_interval",errors:l,label:"Time_interval",placeholder:"Time_interval",register:o,className:""}),t.jsx(n,{type:"text",page:"edit",name:"retries",errors:l,label:"Retries",placeholder:"Retries",register:o,className:""}),t.jsx(n,{type:"text",page:"edit",name:"status",errors:l,label:"Status",placeholder:"Status",register:o,className:""}),t.jsx(C,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{me as default};
