import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as a,d as g}from"./vendor-1c28ea83.js";import{u as b}from"./react-hook-form-eec8b32f.js";import{G as S,A as y,o as A,M as N,s as j,t as E}from"./index-b3edd152.js";import{c as w,a as n}from"./yup-1b5612ec.js";import{M as p}from"./MkdInput-67f7082d.js";import{I}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const at=({setSidebar:d})=>{const{dispatch:s}=a.useContext(S),c=w({status:n().required(),name:n().required(),description:n()}).required(),{dispatch:u}=a.useContext(y),[l,o]=a.useState(!1),x=g(),{register:r,handleSubmit:f,setError:D,formState:{errors:i}}=b({resolver:A(c)}),h=async m=>{o(!0);try{let e=new N;e.setTable("integration"),(await e.callRestAPI({status:m.status,name:m.name,description:m.description},"POST")).error||(j(s,"Added"),x("/admin/integration"),d(!1),s({type:"REFRESH_DATA",payload:{refreshData:!0}})),o(!1)}catch(e){o(!1),console.log("Error",e),E(u,e.message)}};return a.useEffect(()=>{s({type:"SETPATH",payload:{path:"integration"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Integration"}),t.jsxs("form",{className:"w-full max-w-lg",onSubmit:f(h),children:[t.jsx(p,{type:"text",page:"add",name:"status",errors:i,label:"Status",placeholder:"Status",register:r,className:""}),t.jsx(p,{type:"text",page:"add",name:"name",errors:i,label:"Name",placeholder:"Name",register:r,className:""}),t.jsx(p,{type:"text",page:"add",name:"description",errors:i,label:"Description",placeholder:"Description",register:r,className:""}),t.jsx(I,{type:"submit",loading:l,disabled:l,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{at as default};
