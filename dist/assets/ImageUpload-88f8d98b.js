import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as s,R as M}from"./vendor-1c28ea83.js";import{M as F,A as R,am as S,L as y}from"./index-b3edd152.js";import{A as u}from"./index-e2604cb4.js";import{M as E}from"./index-2d6e6aa7.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";function U({title:c,titleId:d,...x},m){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:m,"aria-labelledby":d},x),c?s.createElement("title",{id:d},c):null,s.createElement("path",{fillRule:"evenodd",d:"M10.5 3.75a6 6 0 0 0-5.98 6.496A5.25 5.25 0 0 0 6.75 20.25H18a4.5 4.5 0 0 0 2.206-8.423 3.75 3.75 0 0 0-4.133-4.303A6.001 6.001 0 0 0 10.5 3.75Zm2.03 5.47a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 1 0 1.06 1.06l1.72-1.72v4.94a.75.75 0 0 0 1.5 0v-4.94l1.72 1.72a.75.75 0 1 0 1.06-1.06l-3-3Z",clipRule:"evenodd"}))}const z=s.forwardRef(U),B=z;new F;const D="Image",H=({register:c,errors:d,setValue:x,image:m,name:t,setFileObj:h,fileObj:i,multiple:o=!1,previewImage:I,label:p=D})=>{var j,N;M.useContext(R);const r=s.useRef(null),[A,Z]=s.useState(!1),[w,f]=s.useState(!1),[P,L]=s.useState(""),v=(l=null)=>{let a=i;if(o){let n=a[t];return n.splice(l,1),a[t]=[...n],h({...a})}delete a[t],h({...a})},g=l=>{L(l),f(!0)};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"relative w-fit",children:[!o&&e.jsx("div",{className:"top-[-12%] z-[9] m-auto mb-5",children:e.jsx("label",{children:p})}),e.jsx("div",{className:" w-fit items-start ",children:e.jsxs("div",{className:"flex flex-col gap-5 lg:flex-row",children:[e.jsx("input",{hidden:!0,disabled:A,ref:r,type:"file",id:t,name:t,accept:".jpg,.jpeg,.png",multiple:!1,style:{display:"none"},onChange:l=>{I(t,l.target,o),r.current.value=""}}),o?null:e.jsx("div",{className:"h-fit w-full",children:i[t]||m?e.jsxs("div",{className:"flex items-center gap-5 self-start",children:[e.jsx("fieldset",{className:"relative w-fit min-w-[9.375rem] max-w-[9.375rem]",children:e.jsx("img",{onClick:()=>{var l;return g(((l=i[t])==null?void 0:l.tempURL)||m)},className:"h-[9.375rem] min-w-[9.375rem] max-w-[9.375rem] cursor-pointer rounded-[1.25rem] border-2 border-black ",src:((j=i[t])==null?void 0:j.tempURL)||m,alt:""})}),e.jsxs("div",{className:"flex w-fit",children:[e.jsx(u,{className:"w-full !border-0 !bg-transparent !px-5 !text-[1.25rem] font-bold !text-[#000]",type:"button",showPlus:!1,onClick:()=>{var l;return(l=r==null?void 0:r.current)==null?void 0:l.click()},children:"Change"}),e.jsx(u,{className:"w-full !border-0 !bg-transparent !px-5 !text-[1.25rem] font-bold !text-sub-500",type:"button",onClick:()=>v(),showPlus:!1,children:"Remove"})]})]}):e.jsx("div",{className:"flex h-[9.375rem] min-h-[9.375rem] min-w-[9.375rem] max-w-[9.375rem]  items-center justify-center  rounded-md px-3 py-2 shadow-sm",children:e.jsx("button",{type:"button",onClick:()=>{var l;return(l=r==null?void 0:r.current)==null?void 0:l.click()},className:"relative h-full min-h-full w-full min-w-full",children:e.jsx(B,{className:"text-primary"})})})}),o?e.jsxs("div",{className:"flex w-[full] flex-wrap gap-5 md:w-auto",children:[e.jsx("div",{className:"flex max-h-[9.375rem] min-h-[9.375rem] min-w-[9.375rem] max-w-[9.375rem] items-center justify-center border-2",children:e.jsxs(u,{type:"button",className:"relative h-full min-h-full w-full min-w-full",onClick:()=>{var l;return(l=r==null?void 0:r.current)==null?void 0:l.click()},children:["+ Add ",p]})}),i[t]&&i[t].length?(N=i[t])==null?void 0:N.map((l,a)=>{var n,b,C;return e.jsxs("div",{className:"min-w-[9.375rem] max-w-[9.375rem]",children:[e.jsxs("fieldset",{className:"relative w-fit",children:[e.jsx("img",{onClick:()=>{var k;return g((k=l.tempFile)==null?void 0:k.url)},className:"h-[9.375rem] min-w-[9.375rem] max-w-[9.375rem] cursor-pointer rounded-md",src:(n=l.tempFile)==null?void 0:n.url,alt:""}),e.jsx("div",{onClick:()=>v(a),className:"absolute -right-[.3125rem] -top-[.3125rem] m-auto flex h-[1.875rem] w-[1.875rem] cursor-pointer items-center justify-center rounded-[50%] bg-red-600 text-xs text-white",children:e.jsx(S,{className:"h-3 w-3",fill:"white"})})]}),e.jsx("div",{title:(b=l.tempFile)==null?void 0:b.name,className:"text-truncate min-h-[3.125rem] min-w-full max-w-full overflow-hidden truncate text-ellipsis px-2 text-center text-black",children:(C=l.tempFile)==null?void 0:C.name})]},a)}):null]}):null]})})]}),e.jsx(y,{children:e.jsx(E,{isOpen:w,modalCloseClick:()=>f(!1),title:"Image Preview",modalHeader:!0,classes:{modalDialog:"max-h-[90%] h-fit min-h-fit overflow-clip !w-full md:!w-[29.0625rem]",modal:"h-full",modalContent:"h-full w-full flex items-center"},children:w&&e.jsx(y,{children:e.jsx("img",{className:"max-h-auto min-h-auto h-auto w-full",src:P,alt:"preview"})})})})]})},de=s.memo(H);export{de as default};
