import{j as e}from"./@react-google-maps/api-211df1ae.js";import{f as _,d as R,r as a}from"./vendor-1c28ea83.js";import{G as k,s as n}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const Z=()=>{_(),R();const{dispatch:d}=a.useContext(k),[c,x]=a.useState(!1),[h,i]=a.useState(null),[o,r]=a.useState("joined"),m={avatar:{value:"https://via.placeholder.com/40"},title:{value:"Manaknight Community"}},l=[{id:{value:1},user_avatar:{value:"https://via.placeholder.com/40"},user_first_name:{value:"John"},user_last_name:{value:"Doe"},created_at:{value:new Date(Date.now()-72e5).toISOString()},title:{value:"My client is looking for a Driver for his company"},description:{value:"We are seeking an experienced driver for a full-time position with our client's logistics company. The ideal candidate should have..."},requirements:["Valid driver's license with clean record","Minimum 3 years of professional driving experience","Excellent communication skills"]},{id:{value:2},user_avatar:{value:"https://via.placeholder.com/40"},user_first_name:{value:"Sarah"},user_last_name:{value:"Smith"},created_at:{value:new Date(Date.now()-18e6).toISOString()},title:{value:"Software Developer Position Available"},description:{value:"Looking for a skilled software developer to join our team. The position involves working on exciting projects..."},requirements:["3+ years experience with React and modern JavaScript","Experience with backend technologies","Strong problem-solving skills"]}],p=[{id:{value:1},title:{value:"Business Network Pro"}},{id:{value:2},title:{value:"Tech Innovators"}},{id:{value:3},title:{value:"Marketing Experts"}}],s=t=>{n(d,"Refer functionality to be implemented",3e3,"info")},w=t=>{n(d,"Chat functionality to be implemented",3e3,"info")},y=t=>{i(t),x(!0)},C=t=>{const u={year:"numeric",month:"short",day:"numeric"};return new Date(t).toLocaleDateString(void 0,u)},S=()=>e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"#7dd87d",className:"mr-2",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"})}),e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:m.title.value})]})}),e.jsxs("button",{className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea] flex items-center gap-2",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Create Post"]})]}),M=()=>e.jsx("div",{className:"mb-6 border-b border-[#363636]",children:e.jsxs("nav",{className:"flex -mb-px",children:[e.jsx("button",{onClick:()=>r("joined"),className:`px-4 py-2 font-medium text-sm border-b-2 ${o==="joined"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea] hover:border-[#444]"}`,children:"Joined Communities"}),e.jsx("button",{onClick:()=>r("join"),className:`px-4 py-2 font-medium text-sm border-b-2 ${o==="join"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea] hover:border-[#444]"}`,children:"Join a Community"}),e.jsx("button",{onClick:()=>r("create"),className:`px-4 py-2 font-medium text-sm border-b-2 ${o==="create"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea] hover:border-[#444]"}`,children:"Create a Community"})]})});return e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-6",children:[S(),M(),e.jsx("div",{className:"space-y-6",children:l.map(t=>{var u,v,b,f,j,g,N;return e.jsx("div",{className:"bg-black rounded p-4 space-y-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-[#363636] text-[#eaeaea] text-sm",children:(v=(u=t.user_first_name)==null?void 0:u.value)==null?void 0:v.charAt(0)}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-semibold text-[#eaeaea]",children:(b=t.title)==null?void 0:b.value}),e.jsxs("p",{className:"text-xs text-[#b5b5b5]",children:["Posted by ",(f=t.user_first_name)==null?void 0:f.value," ",(j=t.user_last_name)==null?void 0:j.value," • ",C((g=t.created_at)==null?void 0:g.value)]})]}),e.jsx("div",{children:e.jsx("button",{className:"text-[#b5b5b5]",children:e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})})})})]}),e.jsx("div",{className:"mt-3",children:e.jsx("p",{className:"text-[#eaeaea] mt-2",children:(N=t.description)==null?void 0:N.value})}),e.jsxs("div",{className:"mt-4 flex gap-2",children:[e.jsxs("button",{onClick:()=>s(t.id.value),className:"rounded-lg bg-[#2E7D32] px-4 py-2 text-sm text-[#eaeaea] flex items-center gap-2",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Refer"]}),e.jsxs("button",{onClick:()=>w(t.id.value),className:"rounded-lg bg-[#2E7D32] px-4 py-2 text-sm text-[#eaeaea] flex items-center gap-2",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),"Chat"]}),e.jsxs("button",{onClick:()=>y(t.id.value),className:"rounded-lg bg-[#2E7D32] px-4 py-2 text-sm text-[#eaeaea] flex items-center gap-2",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Repost"]})]})]})]})},t.id.value)})}),e.jsx(D,{isOpen:c,onClose:t=>{x(!1),i(null)},referralId:h,communities:p})]})},D=({isOpen:d,onClose:c,referralId:x,communities:h})=>{if(!d)return null;const[i,o]=a.useState(""),[r,m]=a.useState(!1),{dispatch:l}=a.useContext(k),p=async()=>{if(!i){n(l,"Please select a community",3e3,"error");return}try{m(!0),await new Promise(s=>setTimeout(s,1e3)),n(l,"Referral reposted successfully",3e3,"success"),c(!0)}catch(s){console.error("Error reposting:",s),n(l,"Failed to repost",5e3,"error")}finally{m(!1)}};return e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-[#242424] rounded-lg p-6 max-w-md w-full",children:[e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea] mb-4",children:"Repost to Community"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-[#b5b5b5] mb-2",children:"Select Community"}),e.jsxs("select",{className:"w-full bg-[#363636] text-[#eaeaea] rounded p-2",value:i,onChange:s=>o(s.target.value),children:[e.jsx("option",{value:"",children:"Select a community"}),h.map(s=>e.jsx("option",{value:s.id.value,children:s.title.value},s.id.value))]})]}),e.jsxs("div",{className:"flex justify-end gap-3 mt-6",children:[e.jsx("button",{onClick:()=>c(!1),className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{onClick:p,disabled:r,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:r?"Reposting...":"Repost"})]})]})})};export{Z as default};
