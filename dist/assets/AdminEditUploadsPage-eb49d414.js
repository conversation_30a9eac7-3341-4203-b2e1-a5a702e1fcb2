import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as i,d as N,f as S}from"./vendor-1c28ea83.js";import{u as I}from"./react-hook-form-eec8b32f.js";import{M as T,A as v,G as A,t as g,S as U,o as R,s as C}from"./index-b3edd152.js";import{c as P,a as l}from"./yup-1b5612ec.js";import{M as d}from"./MkdInput-67f7082d.js";import{I as k}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let p=new T;const de=m=>{const{dispatch:f}=i.useContext(v),y=P({url:l().required(),caption:l(),user_id:l(),width:l(),height:l(),type:l().required()}).required(),{dispatch:n}=i.useContext(A),[x,c]=i.useState(!1),[b,u]=i.useState(!1),w=N(),{register:a,handleSubmit:j,setError:D,setValue:s,formState:{errors:o}}=I({resolver:R(y)}),r=S();i.useEffect(function(){(async function(){try{u(!0),p.setTable("uploads");const e=await p.callRestAPI({id:m.activeId?m.activeId:Number(r==null?void 0:r.id)},"GET");e.error||(s("url",e.model.url),s("caption",e.model.caption),s("user_id",e.model.user_id),s("width",e.model.width),s("height",e.model.height),s("type",e.model.type),u(!1))}catch(e){u(!1),console.log("error",e),g(f,e.message)}})()},[]);const E=async e=>{c(!0);try{p.setTable("uploads"),(await p.callRestAPI({id:m.activeId?m.activeId:Number(r==null?void 0:r.id),url:e.url,caption:e.caption,user_id:e.user_id,width:e.width,height:e.height,type:e.type},"PUT")).error||(C(n,"Updated"),w("/admin/uploads"),m.setSidebar(!1),n({type:"REFRESH_DATA",payload:{refreshData:!0}})),c(!1)}catch(h){c(!1),console.log("Error",h),g(f,h.message)}};return i.useEffect(()=>{n({type:"SETPATH",payload:{path:"uploads"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Uploads"}),b?t.jsx(U,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:j(E),children:[t.jsx(d,{type:"text",page:"edit",name:"url",errors:o,label:"Url",placeholder:"Url",register:a,className:""}),t.jsx(d,{type:"text",page:"edit",name:"caption",errors:o,label:"Caption",placeholder:"Caption",register:a,className:""}),t.jsx(d,{type:"text",page:"edit",name:"user_id",errors:o,label:"User_id",placeholder:"User_id",register:a,className:""}),t.jsx(d,{type:"text",page:"edit",name:"width",errors:o,label:"Width",placeholder:"Width",register:a,className:""}),t.jsx(d,{type:"text",page:"edit",name:"height",errors:o,label:"Height",placeholder:"Height",register:a,className:""}),t.jsx(d,{type:"text",page:"edit",name:"type",errors:o,label:"Type",placeholder:"Type",register:a,className:""}),t.jsx(k,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{de as default};
