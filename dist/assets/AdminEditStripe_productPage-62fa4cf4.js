import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as l,d as N,f as E}from"./vendor-1c28ea83.js";import{u as I}from"./react-hook-form-eec8b32f.js";import{M as v,A as w,G as A,t as b,S as P,o as T,s as R}from"./index-b3edd152.js";import{c as C,a as o}from"./yup-1b5612ec.js";import{M as i}from"./MkdInput-67f7082d.js";import{I as k}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let p=new v;const le=c=>{const{dispatch:_}=l.useContext(w),h=C({create_at:o(),update_at:o(),name:o(),product_id:o(),stripe_id:o(),object:o(),status:o()}).required(),{dispatch:m}=l.useContext(A),[f,u]=l.useState(!1),[g,n]=l.useState(!1),j=N(),{register:a,handleSubmit:S,setError:D,setValue:s,formState:{errors:r}}=I({resolver:T(h)}),d=E();l.useEffect(function(){(async function(){try{n(!0),p.setTable("stripe_product");const e=await p.callRestAPI({id:c.activeId?c.activeId:Number(d==null?void 0:d.id)},"GET");e.error||(s("create_at",e.model.create_at),s("update_at",e.model.update_at),s("name",e.model.name),s("product_id",e.model.product_id),s("stripe_id",e.model.stripe_id),s("object",e.model.object),s("status",e.model.status),n(!1))}catch(e){n(!1),console.log("error",e),b(_,e.message)}})()},[]);const y=async e=>{u(!0);try{p.setTable("stripe_product"),(await p.callRestAPI({id:c.activeId?c.activeId:Number(d==null?void 0:d.id),create_at:e.create_at,update_at:e.update_at,name:e.name,product_id:e.product_id,stripe_id:e.stripe_id,object:e.object,status:e.status},"PUT")).error||(R(m,"Updated"),j("/admin/stripe_product"),c.setSidebar(!1),m({type:"REFRESH_DATA",payload:{refreshData:!0}})),u(!1)}catch(x){u(!1),console.log("Error",x),b(_,x.message)}};return l.useEffect(()=>{m({type:"SETPATH",payload:{path:"stripe_product"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Stripe_product"}),g?t.jsx(P,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:S(y),children:[t.jsx(i,{type:"text",page:"edit",name:"create_at",errors:r,label:"Create_at",placeholder:"Create_at",register:a,className:""}),t.jsx(i,{type:"text",page:"edit",name:"update_at",errors:r,label:"Update_at",placeholder:"Update_at",register:a,className:""}),t.jsx(i,{type:"text",page:"edit",name:"name",errors:r,label:"Name",placeholder:"Name",register:a,className:""}),t.jsx(i,{type:"text",page:"edit",name:"product_id",errors:r,label:"Product_id",placeholder:"Product_id",register:a,className:""}),t.jsx(i,{type:"text",page:"edit",name:"stripe_id",errors:r,label:"Stripe_id",placeholder:"Stripe_id",register:a,className:""}),t.jsx(i,{type:"text",page:"edit",name:"object",errors:r,label:"Object",placeholder:"Object",register:a,className:""}),t.jsx(i,{type:"text",page:"edit",name:"status",errors:r,label:"Status",placeholder:"Status",register:a,className:""}),t.jsx(k,{type:"submit",loading:f,disabled:f,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{le as default};
