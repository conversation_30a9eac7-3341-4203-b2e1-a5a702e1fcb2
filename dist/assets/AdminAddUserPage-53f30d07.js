import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as o,d as y,R as w}from"./vendor-1c28ea83.js";import{u as N}from"./react-hook-form-eec8b32f.js";import{A,G as C,o as v,M as E,s as p}from"./index-b3edd152.js";import{c as _,a,b as k,d as B}from"./yup-1b5612ec.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const ee=({setSidebar:n})=>{const u=_({first_name:a().required("First name is required"),last_name:a().required("Last name is required"),email:a().email("Invalid email").required("Email is required"),password:a().min(8,"Password must be at least 8 characters").required("Password is required"),confirm_password:a().oneOf([k("password")],"Passwords must match").required("Please confirm your password"),user_type:B().min(1,"Please select a role").max(1,"Please select only one role").required("Please select a role"),industry:a().required("Please select an industry")}),[D,h]=o.useState(""),[x,m]=o.useState(!1);o.useContext(A);const{dispatch:l}=o.useContext(C),b=y(),{register:r,handleSubmit:f,setValue:j,watch:i,formState:{errors:s}}=N({resolver:v(u),defaultValues:{user_type:[]}}),c=t=>{j("user_type",[t],{shouldValidate:!0})},g=async t=>{console.log("Form data:",t);try{m(!0),h(""),(await new E().register({first_name:t.first_name,last_name:t.last_name,email:t.email,password:t.password,confirm_password:t.confirm_password,user_type:t.user_type,industry:t.industry})).error||(p(l,"User added successfully"),b("/admin/user"),n&&n(!1),l({type:"REFRESH_DATA",payload:{refreshData:!0}}))}catch(d){console.error("Registration error:",d),p(l,d.message||"Failed to add user. Please try again.",5e3,"error")}finally{m(!1)}};return w.useEffect(()=>{l({type:"SETPATH",payload:{path:"user"}})},[]),e.jsx("div",{className:"w-full bg-[#1E1E1E] text-white min-h-screen",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-white",children:"Add New User"}),e.jsx("p",{className:"text-[#B5B5B5] mt-1",children:"Create a new user account"})]}),e.jsxs("form",{onSubmit:f(g),className:"space-y-4 bg-[#161616] p-6 rounded-lg border border-[#2d2d3d] w-[80vw] mx-auto",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#EAEAEA] mb-1",children:"First Name"}),e.jsx("input",{type:"text",...r("first_name"),className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#333] bg-[#1a1a1a] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#444] focus:outline-none",placeholder:"Enter first name"}),s.first_name&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:s.first_name.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Last Name"}),e.jsx("input",{type:"text",...r("last_name"),className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#333] bg-[#1a1a1a] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#444] focus:outline-none",placeholder:"Enter last name"}),s.last_name&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:s.last_name.message})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Email"}),e.jsx("input",{type:"email",...r("email"),autoComplete:"off",className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#333] bg-[#1a1a1a] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#444] focus:outline-none",placeholder:"Enter email"}),s.email&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:s.email.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Password"}),e.jsx("input",{type:"password",...r("password"),autoComplete:"new-password",className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#333] bg-[#1a1a1a] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#444] focus:outline-none",placeholder:"Create password"}),s.password&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:s.password.message})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-2",children:"User Role:"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",...r("user_type"),value:"opportunity",onChange:t=>c("opportunity"),checked:i("user_type").includes("opportunity"),className:"h-4 w-4 rounded bg-[#1a1a1a] text-[#4CAF50] focus:ring-0 border-[#333]"}),e.jsx("span",{className:"ml-2 text-[16px] text-[#ADAEBC]",children:"Looking for Opportunity"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",...r("user_type"),value:"referrals",onChange:t=>c("referrals"),checked:i("user_type").includes("referrals"),className:"h-4 w-4 rounded bg-[#1a1a1a] text-[#4CAF50] focus:ring-0 border-[#333]"}),e.jsx("span",{className:"ml-2 text-[16px] text-[#ADAEBC]",children:"Has Referrals for Others"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",...r("user_type"),value:"both",onChange:t=>c("both"),checked:i("user_type").includes("both"),className:"h-4 w-4 rounded bg-[#1a1a1a] text-[#4CAF50] focus:ring-0 border-[#333]"}),e.jsx("span",{className:"ml-2 text-[16px] text-[#ADAEBC]",children:"Looking for Both"})]})]}),s.user_type&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:s.user_type.message})]}),e.jsxs("div",{className:"flex flex-col space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Confirm Password"}),e.jsx("input",{type:"password",...r("confirm_password"),autoComplete:"new-password",className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#333] bg-[#1a1a1a] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#444] focus:outline-none",placeholder:"Confirm password"}),s.confirm_password&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:s.confirm_password.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Industry"}),e.jsxs("select",{...r("industry"),className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#333] bg-[#1a1a1a] px-3 text-[16px] text-[#ADAEBC] focus:border-[#444] focus:outline-none appearance-none",children:[e.jsx("option",{value:"",children:"Select industry"}),e.jsx("option",{value:"technology",children:"Technology"}),e.jsx("option",{value:"finance",children:"Finance"}),e.jsx("option",{value:"healthcare",children:"Healthcare"}),e.jsx("option",{value:"retail",children:"Retail"}),e.jsx("option",{value:"manufacturing",children:"Manufacturing"}),e.jsx("option",{value:"consulting",children:"Consulting"}),e.jsx("option",{value:"education",children:"Education"}),e.jsx("option",{value:"real_estate",children:"Real Estate"}),e.jsx("option",{value:"other",children:"Other"})]}),s.industry&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:s.industry.message})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-4 pt-6 border-t border-[#2d2d3d] mt-6",children:[e.jsx("button",{type:"button",onClick:()=>n(!1),className:"px-4 py-2 bg-[#161616] border border-[#333] text-white rounded hover:bg-[#252538]",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:x,className:"px-4 py-2 bg-[#4CAF50] text-white rounded hover:bg-[#3d9f40] flex items-center justify-center",children:x?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Creating User..."]}):"Create User"})]})]})]})})};export{ee as default};
