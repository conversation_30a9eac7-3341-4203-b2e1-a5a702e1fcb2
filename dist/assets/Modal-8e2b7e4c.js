import{j as t}from"./@react-google-maps/api-211df1ae.js";import{r as m}from"./vendor-1c28ea83.js";import{M as u}from"./index.esm-1ac45320.js";import{a as s}from"./index-b3edd152.js";import"./react-icons-5238c8a8.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const x=({children:a,title:r,modalCloseClick:n,modalHeader:p,classes:o={modal:"h-full",modalDialog:"h-[90%]",modalContent:""},page:d="",isOpen:i,disableCancel:f=!1})=>{const c=m.useRef(null);return m.useEffect(()=>{const l=document.querySelectorAll("body, .scrollable-container");return i?l.forEach(e=>{e.style.overflow="hidden"}):l.forEach(e=>{e.style.overflow="auto"}),()=>{l.forEach(e=>{e.style.overflow="auto"})}},[i]),t.jsx("div",{ref:c,style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},className:`fixed bottom-0 left-0 right-0 top-0 flex w-full scale-0 items-center justify-center bg-[#00000099] p-5 backdrop-blur-sm transition-all ${i?"scale-100":"scale-0"} ${o==null?void 0:o.modal}`,children:t.jsxs("div",{className:`${d==="ManagePermissionAddRole"?"w-fit":"w-[80%]"} relative overflow-auto rounded-lg bg-[#1e1e1e] pb-5 shadow ${o==null?void 0:o.modalDialog}`,children:[p&&t.jsxs("div",{className:"sticky inset-x-0 top-0 !z-50 m-auto flex w-full justify-between border-b bg-[#1e1e1e] px-5 py-4",children:[t.jsx("div",{className:"text-center text-white font-inter text-[1.125rem] font-bold capitalize leading-[1.5rem] tracking-[-1.5%]",children:["string"].includes(typeof r)?s(r,{casetype:"capitalize",separator:" "}):r}),f?null:t.jsx("button",{type:"button",className:"modal-close cursor-pointer",onClick:n,children:t.jsx(u,{className:"text-xl text-white"})})]}),t.jsx("div",{className:`-z-10 mt-4 px-5 ${o==null?void 0:o.modalContent}`,children:a})]})})},H=m.memo(x);export{H as Modal};
