import{j as s}from"./@react-google-maps/api-211df1ae.js";import{R as a,f as o}from"./vendor-1c28ea83.js";import{M as n,A as x,G as p,t as f,S as h}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let m=new n;const K=()=>{const{dispatch:l}=a.useContext(x);a.useContext(p);const[e,d]=a.useState({}),[c,r]=a.useState(!0),i=o();return a.useEffect(function(){(async function(){try{r(!0),m.setTable("industry");const t=await m.callRestAPI({id:Number(i==null?void 0:i.id),join:""},"GET");t.error||(d(t.model),r(!1))}catch(t){r(!1),console.log("error",t),f(l,t.message)}})()},[]),s.jsx("div",{className:"shadow-md rounded mx-auto p-5",children:c?s.jsx(h,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Industry"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Updated_at"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.updated_at})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Created_at"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.created_at})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.name})]})})]})})};export{K as default};
