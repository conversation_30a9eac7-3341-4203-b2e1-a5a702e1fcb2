import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as _,d as D,r as s}from"./vendor-1c28ea83.js";import{M as P,A as R,S as U,t as F}from"./index-b3edd152.js";/* empty css                              */import{A as V}from"./index.esm-2d1feecf.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let T=new P;const he=()=>{const{dispatch:h}=_.useContext(R),f=D(),[i,j]=s.useState(""),[g,n]=s.useState([]),[N,c]=s.useState(!1),[a,y]=s.useState(1),[l]=s.useState(10),v=e=>{f(`/admin/view-commission/${e}`)},w=async()=>{try{c(!0);const e=await T.callRawAPI("/v1/api/dealmaker/super_admin/commissions",{},"GET");console.log("API Response:",e),e&&!e.error?Array.isArray(e)?n(e):e.list&&Array.isArray(e.list)?n(e.list):(console.error("Unexpected API response format:",e),n([])):(console.error("Error fetching commissions:",(e==null?void 0:e.error)||"Unknown error"),n([])),c(!1)}catch(e){console.error("Error fetching commissions:",e),c(!1),n([]),F(h,e.message)}},o=g.filter(e=>{if(!i)return!0;if(!e)return!1;const r=i.toLowerCase(),m=e.id?e.id.toString().includes(r):!1,k=e.user_id?e.user_id.toString().includes(r):!1,I=e.reference_id?e.reference_id.toString().includes(r):!1,M=e.type?e.type.toLowerCase().includes(r):!1,L=e.amount?e.amount.toString().includes(r):!1,$=e.status?e.status.toLowerCase().includes(r):!1;return m||k||I||M||L||$}),d=a*l,u=d-l,p=o.slice(u,d),x=e=>y(e),A=e=>{try{if(!e)return"N/A";const r=new Date(e);return isNaN(r.getTime())?"Invalid Date":`${r.toLocaleString("default",{month:"short"})} ${r.getDate()}, ${r.getFullYear()}`}catch(r){return console.error("Error formatting date:",r),"Error"}},C=e=>{try{if(!e)return"$0.00";const r=parseFloat(e);return isNaN(r)?"$0.00":`$${r.toFixed(2)}`}catch(r){return console.error("Error formatting currency:",r),"$0.00"}},S=e=>{try{return e?{referral_completion:"Referral Completion",create_community:"Buyer",subscription:"Subscription",referral:"Introduction"}[e]||e:"Unknown"}catch(r){return console.error("Error getting type display:",r),"Unknown"}},b=e=>{try{return e?`${String(e).padStart(3,"0")}`:"N/A"}catch(r){return console.error("Error formatting user ID:",r),"N/A"}},E=e=>{try{return e?e.toLowerCase()==="available"?"Complete":e.charAt(0).toUpperCase()+e.slice(1).toLowerCase():"Pending"}catch(r){return console.error("Error formatting status:",r),"Pending"}};return s.useEffect(()=>{w()},[h]),t.jsx("div",{className:"opportunities-dashboard bg-[#1E1E1E]",children:t.jsxs("div",{className:"container",children:[t.jsxs("div",{className:"header",children:[t.jsx("h1",{children:"Commissions"}),t.jsx("p",{children:"View all commission payouts and transactions"})]}),t.jsx("div",{className:"search-add",children:t.jsxs("div",{className:"search-container",children:[t.jsx("div",{className:"search-icon",children:t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),t.jsx("input",{type:"text",placeholder:"Search commissions...",value:i,onChange:e=>j(e.target.value),className:"search-input"})]})}),t.jsx("div",{className:"table-container",children:N?t.jsx("div",{className:"p-6",children:t.jsx(U,{})}):t.jsxs("table",{children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:"ID"}),t.jsx("th",{children:"User ID"}),t.jsx("th",{children:"Reference ID"}),t.jsx("th",{children:"Type"}),t.jsx("th",{children:"Amount"}),t.jsx("th",{children:"Status"}),t.jsx("th",{children:"Date"}),t.jsx("th",{children:"Actions"})]})}),t.jsx("tbody",{children:p.length>0?p.map(e=>t.jsxs("tr",{children:[t.jsx("td",{children:e.id||"N/A"}),t.jsx("td",{children:e.user_id?b(e.user_id):"N/A"}),t.jsx("td",{children:e.reference_id||"N/A"}),t.jsx("td",{children:e.type?S(e.type):"N/A"}),t.jsx("td",{children:e.amount?C(e.amount):"N/A"}),t.jsx("td",{children:t.jsx("span",{className:"status paid",children:E(e.status)})}),t.jsx("td",{children:e.created_at?A(e.created_at):"N/A"}),t.jsx("td",{children:t.jsx("div",{className:"action-buttons",children:t.jsx("button",{title:"View",onClick:()=>v(e.id),className:"view-button",disabled:!e.id,children:t.jsx(V,{className:"text-blue-500"})})})})]},e.id||Math.random())):t.jsx("tr",{children:t.jsx("td",{colSpan:"8",className:"text-center py-4",children:"No commissions found"})})})]})}),t.jsxs("div",{className:"pagination",children:[t.jsx("div",{className:"pagination-info",children:o.length>0?`Showing ${u+1} to ${Math.min(d,o.length)} of ${o.length} entries`:"No entries to show"}),t.jsxs("div",{className:"pagination-buttons",children:[t.jsx("button",{className:"pagination-button prev",onClick:()=>x(a-1),disabled:a===1,children:"Previous"}),t.jsx("button",{className:"pagination-button next",onClick:()=>x(a+1),disabled:a>=Math.ceil(o.length/l),children:"Next"})]})]})]})})};export{he as default};
