import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as n,f as g,d as v}from"./vendor-1c28ea83.js";import{M as b,A as y,G as _,t as k,S as D}from"./index-b3edd152.js";/* empty css                             */import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let C=new b;const X=()=>{var j;const{dispatch:x}=n.useContext(y);n.useContext(_);const[s,p]=n.useState({}),[a,u]=n.useState(null),[N,l]=n.useState(!0),r=g(),m=v(),d=t=>{try{if(!t)return"N/A";const i=new Date(t);return isNaN(i.getTime())?"Invalid Date":i.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch(i){return console.error("Error formatting date:",i),"Error"}},o=(t,i="USD")=>{try{if(!t)return"N/A";const c=parseFloat(t);return isNaN(c)?"$0.00":new Intl.NumberFormat("en-US",{style:"currency",currency:i}).format(c)}catch(c){return console.error("Error formatting currency:",c),"$0.00"}};n.useEffect(function(){(async function(){try{l(!0),console.log("Fetching commissions and filtering for ID:",r==null?void 0:r.id);const t=await C.callRawAPI("/v1/api/dealmaker/super_admin/commissions",{},"GET");if(console.log("API Response:",t),t&&!t.error&&t.list&&Array.isArray(t.list)){const i=t.list.find(c=>c.id===Number(r==null?void 0:r.id));if(i){if(p(i),i.data)try{const c=JSON.parse(i.data);u(c),console.log("Parsed Stripe data:",c)}catch(c){console.error("Error parsing Stripe data:",c)}console.log("Found commission:",i)}else console.error("Commission not found with ID:",r==null?void 0:r.id)}else console.error("Error fetching commissions:",(t==null?void 0:t.error)||"Unknown error");l(!1)}catch(t){console.error("Error fetching commissions:",t),l(!1),k(x,t.message)}})()},[r==null?void 0:r.id,x]);const f=t=>{try{if(!t)return"status paid";switch(t.toLowerCase()){case"available":return"status paid";case"pending":return"status pending";case"failed":return"status failed";case"refunded":return"status refunded";default:return"status paid"}}catch(i){return console.error("Error getting status class:",i),"status paid"}},h=t=>{try{return t?{referral_completion:"Referral Completion",create_community:"Buyer",subscription:"Subscription",referral:"Introduction"}[t]||t:"Unknown"}catch(i){return console.error("Error getting type display:",i),"Unknown"}};return e.jsx("div",{className:"view-payment-page bg-[#1E1E1E] text-white min-h-screen",children:e.jsx("div",{className:"container mx-auto p-6",children:N?e.jsx("div",{className:"bg-[#161616] p-6 rounded",children:e.jsxs("div",{className:"flex flex-col items-center justify-center py-6",children:[e.jsx(D,{}),e.jsx("p",{className:"text-[#9ca3af] mt-4",children:"Loading commission data..."})]})}):!s||Object.keys(s).length===0?e.jsx("div",{className:"bg-[#161616] p-6 rounded",children:e.jsxs("div",{className:"flex flex-col items-center justify-center py-12",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"64",height:"64",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"text-red-500 mb-4",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),e.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Commission Not Found"}),e.jsxs("p",{className:"text-[#9ca3af] text-center mb-6",children:["The commission with ID ",r==null?void 0:r.id," could not be found."]}),e.jsxs("button",{onClick:()=>m("/admin/commission"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Commission List"]})]})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"header mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Commission Details"}),e.jsxs("button",{onClick:()=>m("/admin/commission"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to List"]})]}),e.jsx("p",{className:"text-[#9ca3af] text-sm",children:"View and manage commission details"})]}),e.jsxs("div",{className:"content-wrapper bg-[#161616] rounded-lg border border-[#2d2d3d] overflow-hidden",children:[e.jsx("div",{className:"header-section p-6 border-b border-[#2d2d3d]",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-xl font-semibold mb-2",children:["Commission #",s==null?void 0:s.id]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-[#9ca3af]",children:[e.jsxs("div",{children:["Transaction ID: ",(s==null?void 0:s.transaction_id)||(s==null?void 0:s.stripe_invoice_id)||`ch_${Math.floor(Math.random()*1e10)}`]}),e.jsxs("div",{children:["Date: ",d(s==null?void 0:s.created_at)]})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:f(s==null?void 0:s.status),children:(s==null?void 0:s.status)||"Available"}),e.jsx("span",{className:"type-badge bg-[#252538] text-white px-3 py-1 rounded-full text-xs",children:h(s==null?void 0:s.type)})]})]})}),e.jsx("div",{className:"amount-section p-6 border-b border-[#2d2d3d] bg-[#1a1a1a]",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-[#9ca3af] text-sm mb-1",children:"Amount"}),e.jsx("div",{className:"amount",children:o(s==null?void 0:s.amount,s==null?void 0:s.currency)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-[#9ca3af] text-sm mb-1",children:"Deal Size"}),e.jsx("div",{children:o(s==null?void 0:s.deal_size,s==null?void 0:s.currency)})]})]})}),e.jsxs("div",{className:"details-grid p-6 grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"left-column space-y-6",children:[e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Commission Information"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Commission ID"}),e.jsx("div",{children:(s==null?void 0:s.id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Type"}),e.jsx("div",{children:h(s==null?void 0:s.type)})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Status"}),e.jsx("div",{children:(s==null?void 0:s.status)||"Available"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Currency"}),e.jsx("div",{children:(s==null?void 0:s.currency)||"USD"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Payment Method"}),e.jsx("div",{children:(s==null?void 0:s.payment_method)||"Credit Card"})]})]})]}),e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Stripe Information"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Stripe Invoice ID"}),e.jsx("div",{children:(s==null?void 0:s.stripe_invoice_id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Stripe Subscription ID"}),e.jsx("div",{children:(s==null?void 0:s.stripe_subscription_id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Transaction ID"}),e.jsx("div",{children:(s==null?void 0:s.transaction_id)||"N/A"})]}),a&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Payment Status"}),e.jsx("div",{children:a.status||"N/A"})]}),a.receipt_url&&e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Receipt URL"}),e.jsx("div",{children:e.jsx("a",{href:a.receipt_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:underline",children:"View Receipt"})})]}),((j=a.payment_method_details)==null?void 0:j.card)&&e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Card Details"}),e.jsxs("div",{children:[a.payment_method_details.card.brand," **** ",a.payment_method_details.card.last4,"(Expires: ",a.payment_method_details.card.exp_month,"/",a.payment_method_details.card.exp_year,")"]})]})]})]})]})]}),e.jsxs("div",{className:"right-column space-y-6",children:[e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"User Information"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"User ID"}),e.jsx("div",{children:s!=null&&s.user_id?`${String(s.user_id).padStart(3,"0")}`:"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Reference ID"}),e.jsx("div",{children:(s==null?void 0:s.reference_id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Receiver ID"}),e.jsx("div",{children:(s==null?void 0:s.receiver_id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Pay"}),e.jsx("div",{children:o(s==null?void 0:s.pay,s==null?void 0:s.currency)})]})]})]}),a&&e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Payment Details"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[a.description&&e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Description"}),e.jsx("div",{children:a.description})]}),a.receipt_email&&e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Receipt Email"}),e.jsx("div",{children:a.receipt_email})]}),a.created&&e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Stripe Created Date"}),e.jsx("div",{children:d(new Date(a.created*1e3))})]}),a.outcome&&e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Payment Outcome"}),e.jsx("div",{children:a.outcome.seller_message||a.outcome.type||"N/A"})]})]})]})]})]}),e.jsxs("div",{className:"dates-section p-6 border-t border-[#2d2d3d]",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Dates"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Created At"}),e.jsx("div",{children:d(s==null?void 0:s.created_at)})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Updated At"}),e.jsx("div",{children:d(s==null?void 0:s.updated_at)})]})]})]}),e.jsx("div",{className:"actions-section p-6 border-t border-[#2d2d3d] flex justify-end gap-4",children:e.jsxs("button",{onClick:()=>m("/admin/commission"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to List"]})})]})]})})})};export{X as default};
