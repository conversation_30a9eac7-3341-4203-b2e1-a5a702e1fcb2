import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as x,r as u,u as v,L as m,k as p}from"./vendor-1c28ea83.js";import{G as N,A as b,aa as y,L as j}from"./index-b3edd152.js";import{B as L}from"./index-f11ac994.js";import"./pdf-lib-623decea.js";import{P as g}from"./index-5e89d896.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const s={LINK:"link",ACTION:"action",DROPDOWN:"dropdown"},w=[{to:"/admin/items",text:"View Items",type:s.LINK,value:"items"},{to:"/admin/assembly",text:"View Assembly",type:s.LINK,value:"assembly"}],K=[{to:"/admin/pending_orders",text:"Pending Orders",type:s.LINK,value:"pending orders"},{to:"/admin/cancelled_orders",text:"Cancelled Orders",type:s.LINK,value:"cancelled orders"},{to:"/admin/completed-orders",text:"Completed Orders",type:s.LINK,value:"completed orders"}],k=[{to:"/admin/inventory",text:"View Inventory",type:s.LINK,value:"inventory"},{to:"/admin/purge_requests",text:"Purge Requests",type:s.LINK,value:"purge requests"},{to:"/admin/hold_requests",text:"Hold Requests",type:s.LINK,value:"hold requests"},{to:"/admin/cycle_counts",text:"Cylce Count Requests",type:s.LINK,value:"cylce count requests"},{to:"/admin/transfer_inventory",text:"Transfer Inventory",type:s.LINK,value:"transfer"}],i=[{to:"/admin/users",text:"User Management",type:s.LINK,value:"users"},{to:"/admin/company",text:"Companies",type:s.LINK,value:"company"},{text:"Address Book",value:"address_book",to:"/admin/address_book",type:s.LINK},{to:"/admin/warehouses",text:"Warehouses",type:s.LINK,value:"warehouses"},{to:"/admin/warehouse_locations",text:"Locations",type:s.LINK,value:"warehouse location"},{to:"/admin/location_types",text:"Location Types",type:s.LINK,value:"location_type"},{to:"/admin/freight_accounts",text:"Freight Accounts",type:s.LINK,value:"freight_accounts"},{to:"/admin/packaging_materials",text:"Packaging Materials",type:s.LINK,value:"packaging_materials"},{to:"/admin/rate_card",text:"Rate Cards",type:s.LINK,value:"rate cards"}],_=[{to:"/admin/dashboard",text:"Dashboard",type:s.LINK,value:"dashboard"},{to:"/admin/orders?view=pending",text:"Orders",type:s.LINK,dropdownItems:K,value:"orders"},{to:"/admin/receipts",text:"Receipts",type:s.LINK,value:"receipts"},{to:"/admin/inventory",text:"Inventory",type:s.LINK,dropdownItems:k,value:"inventory"},{to:"/admin/items",text:"Items",type:s.LINK,dropdownItems:w,value:"items"},{to:"/admin/reports",text:"Reports",type:s.LINK,value:"reports"},{to:"/admin/installation",text:"Installation",type:s.LINK,value:"installation"},{to:"/admin/products",text:"Products",type:s.LINK,value:"products"},{to:"/admin/exhibitor_request?view=submitted",text:"Exhibitor Requests",type:s.LINK,value:"exhibitor request"}],ee=()=>{const{state:d,dispatch:n}=x.useContext(N);x.useContext(b);const[F,c]=u.useState(""),{isOpen:C,showBackButton:h,path:a}=d,o=v();return u.useEffect(()=>{const e=o.pathname.split("/");e[1]!=="user"&&e[1]!=="admin"?c(e[1]):c(e[2])},[o]),t.jsxs("div",{className:"sticky inset-x-0 top-0 z-20 m-auto flex h-fit max-h-fit min-h-[5.4375rem] w-full min-w-full max-w-full flex-col items-center justify-between bg-black px-6  pt-2 md:min-w-[auto] md:max-w-[auto]",children:[t.jsxs("div",{className:"flex w-full min-w-full max-w-full justify-between gap-10",children:[t.jsxs("div",{className:"flex items-center gap-3",children:[h&&t.jsx(L,{}),t.jsx("h1",{className:"text-xl capitalize",children:t.jsx(y,{})})]}),t.jsx(j,{children:t.jsx(g,{})})]}),t.jsx("div",{className:"non_print_section scrollbar-hide w-full max-w-full overflow-x-auto md:overflow-x-clip",children:t.jsxs("ul",{className:"flex w-fit justify-start text-sm",children:[_.map(e=>{var r;switch(e.type){case s.LINK:return t.jsx("li",{className:`flex !w-fit !min-w-fit !max-w-fit items-center justify-center p-[0.75rem] px-2  ${a===e.value?"border-b-2 border-b-third text-white":"text-white-600"}`,children:t.jsx(p,{to:e.to,className:`${a===e.value?"active-nav":""} !w-fit`,children:t.jsx("div",{className:"flex !w-fit items-center gap-3",children:t.jsx("span",{className:"!w-fit",children:e.text})})})},e.value);case s.DROPDOWN:return t.jsx(t.Fragment,{children:t.jsxs("li",{className:` relative flex cursor-pointer list-none items-center justify-center px-2 ${a===e.value?"border-b-2 border-b-third text-white":"text[#ffffff99"}`,children:[t.jsxs("button",{className:"peer flex h-fit cursor-pointer items-center gap-2 text-[1rem] ",children:[e.text,t.jsx("span",{className:"text-xs",children:"▽"})]}),t.jsx("ul",{className:"absolute top-[80%] z-20 hidden min-w-[12.5rem] rounded-lg border border-[#a8a8a8] bg-white p-2 text-sm text-white shadow-md hover:block focus:block peer-focus:block peer-focus-visible:block",children:e!=null&&e.dropdownItems&&((r=e.dropdownItems)!=null&&r.length)?e.dropdownItems.map((l,f)=>t.jsx("li",{className:"!w-fit",children:t.jsx(m,{className:`hover:text[#262626] flex !w-fit cursor-pointer items-center rounded-md px-4 py-3 text-black hover:bg-[#F4F4F4] ${a===l.value?"active-nav":""}`,to:l==null?void 0:l.to,children:t.jsx("span",{children:l.text})})},f)):null})]})})}}),i&&(i!=null&&i.length)?i.map((e,r)=>{switch(e==null?void 0:e.type){case s.LINK:return t.jsx("li",{className:`flex !w-fit  !min-w-fit !max-w-fit items-center justify-center p-[0.75rem] px-2 md:hidden  ${a===e.value?"border-b-2 border-b-third text-white":"text-white-600"}`,children:t.jsx(p,{to:e.to,className:`${a===e.value?"active-nav":""} !w-fit`,children:t.jsx("div",{className:"flex !w-fit items-center gap-3",children:t.jsx("span",{className:"!w-fit",children:e.text})})})},e.value);case s.ACTION:return t.jsx("li",{className:`flex !w-fit  !min-w-fit !max-w-fit items-center justify-center p-[0.75rem] px-2 md:hidden  ${a===e.value?"border-b-2 border-b-third text-white":"text-white-600"}`,children:t.jsx("button",{type:"button",onClick:()=>(e==null?void 0:e.action)&&e.action(n),className:`${a===e.value?"active-nav":""} !w-fit`,children:t.jsx("div",{className:"flex !w-fit items-center gap-3",children:t.jsx("span",{className:"!w-fit",children:e.text})})})},e.value)}}):null,t.jsxs("div",{className:" relative hidden cursor-pointer items-center md:flex",children:[t.jsxs("button",{className:"peer ml-3 flex cursor-pointer items-center text-sm text-white-600 ",children:["More",t.jsx("span",{className:"ml-2 text-xs",children:"▽"})]}),t.jsx("ul",{className:"absolute top-[80%] z-20 hidden min-w-[12.5rem] rounded-lg border border-[#a8a8a8] bg-white p-2 text-sm text-white shadow-md hover:block focus:block peer-focus:block peer-focus-visible:block  md:right-0",children:i&&(i!=null&&i.length)?i.map((e,r)=>{switch(e.type){case s.LINK:return t.jsx("li",{className:"!w-fit !min-w-full",children:t.jsx(m,{className:`flex !w-full cursor-pointer items-center rounded-md px-4 py-3 text-black hover:bg-[#F4F4F4] hover:text-[#262626] ${a===e.value?"bg-[#F4F4F4]":""}`,to:e==null?void 0:e.to,children:t.jsx("span",{children:e.text})})},r);case s.ACTION:return t.jsx("li",{className:"!w-fit !min-w-full",children:t.jsx("button",{type:"button",onClick:()=>(e==null?void 0:e.action)&&e.action(n),className:`flex !w-full cursor-pointer items-center rounded-md px-4 py-3 text-black hover:bg-[#F4F4F4] hover:text-[#262626] ${a===e.value?"bg-[#F4F4F4]":""}`,children:t.jsx("span",{children:e.text})})},r)}}):null})]})]})})]})};export{ee as default};
