import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as a,d as cs,u as ms,R as Ae}from"./vendor-1c28ea83.js";import{G as xe,M as L,s as f,T as us,S as Ie}from"./index-b3edd152.js";import"./@craftjs/core-a5d68af1.js";import{d as xs}from"./@uppy/aws-s3-c23f5c86.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const hs="/assets/img-1001f23f.svg",bs=({referralId:m,onClose:C})=>{var te;const{dispatch:i}=a.useContext(xe),[T,D]=a.useState(!1),[V,B]=a.useState(""),[p,E]=a.useState({first_name:{value:""},last_name:{value:""},email:{value:""},online_accounts:{value:[]},recommendation:{value:[{first_name:"",last_name:"",email:"",user_id:"12"},{first_name:"",last_name:"",email:"",user_id:"12"},{first_name:"",last_name:"",email:"",user_id:""},{first_name:"",last_name:"",email:"",user_id:""},{first_name:"",last_name:"",email:"",user_id:""}]}}),se=()=>{E(o=>({...o,online_accounts:{value:[...o.online_accounts.value,{platform:"",username:"",url:""}]}}))},Q=async o=>{o.preventDefault(),D(!0),B("");const h=p.recommendation.value.filter(v=>v.first_name&&v.last_name&&v.email);if(h.length===0){B("Please fill in at least one recommendation"),D(!1);return}try{const v=new L,g={first_name:{value:p.first_name.value},last_name:{value:p.last_name.value},email:{value:p.email.value},online_accounts:{value:p.online_accounts.value.map(R=>({platform:{value:R.platform},username:{value:R.username},url:{value:R.url},user_id:{value:R.user_id||""}}))},recommendation:{value:h.map(R=>({first_name:{value:R.first_name},last_name:{value:R.last_name},email:{value:R.email},user_id:{value:R.user_id||""}}))}},A=await v.callRawAPI(`/v1/api/dealmaker/user/referral/${m}/recommend`,g,"POST");A.error?B(A.message):(f(i,"Referral recommendation submitted successfully!",5e3,"success"),C())}catch(v){B(v.message||"Failed to submit referral recommendation")}finally{D(!1)}};return e.jsx("div",{className:"p-6",children:e.jsxs("form",{onSubmit:Q,className:"space-y-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:"Your Name *"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name"}),e.jsx("input",{type:"text",value:p.first_name.value,onChange:o=>E(h=>({...h,first_name:{value:o.target.value}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name"}),e.jsx("input",{type:"text",value:p.last_name.value,onChange:o=>E(h=>({...h,last_name:{value:o.target.value}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Email *"}),e.jsx("input",{type:"email",value:p.email.value,onChange:o=>E(h=>({...h,email:{value:o.target.value}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Add your website or social links"}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("input",{type:"url",value:((te=p.online_accounts.value[0])==null?void 0:te.url)||"",onChange:o=>E(h=>({...h,online_accounts:{value:[{platform:"",username:"",url:o.target.value,user_id:""},...h.online_accounts.value.slice(1)]}})),placeholder:"https://",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"}),e.jsx("button",{type:"button",onClick:se,className:"flex h-10 items-center justify-center rounded-lg border border-[#363636] bg-[#161616] px-4 text-sm text-[#eaeaea] hover:bg-[#242424]",children:"+ Add More"})]}),p.online_accounts.value.slice(1).map((o,h)=>e.jsx("div",{className:"mt-2",children:e.jsx("input",{type:"url",value:o.url,onChange:v=>{const g=[...p.online_accounts.value];g[h+1]={platform:"",username:"",url:v.target.value,user_id:""},E(A=>({...A,online_accounts:{value:g}}))},placeholder:"https://",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})},h)),e.jsx("div",{className:"mt-2 h-2 w-full rounded-full bg-[#363636]",children:e.jsx("div",{className:"h-full rounded-full bg-[#2e7d32]",style:{width:"20%"}})})]})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:"Recommendation #1 *"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name"}),e.jsx("input",{type:"text",value:p.recommendation.value[0].first_name,onChange:o=>E(h=>({...h,recommendation:{value:h.recommendation.value.map((v,g)=>g===0?{...v,first_name:o.target.value}:v)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name"}),e.jsx("input",{type:"text",value:p.recommendation.value[0].last_name,onChange:o=>E(h=>({...h,recommendation:{value:h.recommendation.value.map((v,g)=>g===0?{...v,last_name:o.target.value}:v)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Email"}),e.jsx("input",{type:"email",value:p.recommendation.value[0].email,onChange:o=>E(h=>({...h,recommendation:{value:h.recommendation.value.map((v,g)=>g===0?{...v,email:o.target.value}:v)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]})]}),[1,2,3,4].map(o=>e.jsxs("div",{children:[e.jsxs("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:["Recommendation #",o+1]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name"}),e.jsx("input",{type:"text",value:p.recommendation.value[o].first_name,onChange:h=>E(v=>({...v,recommendation:{value:v.recommendation.value.map((g,A)=>A===o?{...g,first_name:h.target.value}:g)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name"}),e.jsx("input",{type:"text",value:p.recommendation.value[o].last_name,onChange:h=>E(v=>({...v,recommendation:{value:v.recommendation.value.map((g,A)=>A===o?{...g,last_name:h.target.value}:g)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:["Recommendation #",o+1," Email"]}),e.jsx("input",{type:"email",value:p.recommendation.value[o].email,onChange:h=>E(v=>({...v,recommendation:{value:v.recommendation.value.map((g,A)=>A===o?{...g,email:h.target.value}:g)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]})]},o)),e.jsxs("div",{style:{padding:"20px"},className:"mt-6 rounded-lg bg-[#242424] text-sm text-[#7dd87d]",children:[e.jsx("p",{className:"mb-2 font-medium",children:"Please note:"}),e.jsx("p",{className:"text-xs text-white",children:"RainmakerOS is not for everyone. This is a vetted premium network designed to connect you with givers, matchers and other business rainmakers."})]}),V&&e.jsx("p",{className:"text-sm text-red-500",children:V}),e.jsx("div",{className:"flex justify-center mt-6",children:e.jsx("button",{type:"submit",disabled:T,className:"rounded-lg bg-[#2e7d32] px-6 py-3 text-sm text-white hover:bg-[#1b5e20] disabled:opacity-50",children:"Submit Referral"})})]})})},vs=m=>m.split(" ").map(C=>C[0]).join("").toUpperCase().slice(0,2),Se=({user:m})=>{var C;return(C=m.photo)!=null&&C.value?e.jsx("img",{src:m.photo.value,alt:m.name.value,className:"object-cover w-8 h-8 rounded-full"}):e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white",children:vs(m.name.value)})},ps=({communityId:m,onClose:C,onSuccess:i})=>{const{dispatch:T}=a.useContext(xe),[D,V]=a.useState(""),[B,p]=a.useState([]),[E,se]=a.useState(!1),[Q,te]=a.useState(!1),[o,h]=a.useState([]),[v,g]=a.useState(!1),A=a.useCallback(xs(async b=>{if(b.trim().length<2){p([]);return}try{se(!0);const k=await new L().SearchUsers(b);k.error||p(k.list||[])}catch($){console.error("Failed to search users:",$)}finally{se(!1)}},300),[]),R=b=>{const $=b.target.value;V($),te(!0),A($)},ae=b=>{o.some($=>$.id.value===b.id.value)||h([...o,b]),V(""),p([]),te(!1)},re=b=>{h(o.filter($=>$.id.value!==b))},oe=async()=>{if(o.length===0){f(T,"Please select at least one user to invite",5e3,"error");return}try{g(!0);const b=new L,$=o.map(k=>b.callRawAPI(`/v1/api/dealmaker/user/community/${m}/add`,{email:k.email.value,user_id:{value:k.id.value}},"POST"));await Promise.all($),f(T,`Successfully sent ${o.length} invitation${o.length>1?"s":""}`,5e3,"success"),h([]),i&&i(),C&&C()}catch(b){console.error("Failed to send invites:",b),f(T,b.message||"Failed to send invites",5e3,"error")}finally{g(!1)}};return e.jsxs("div",{className:"p-4 bg-[#242424] rounded-lg",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-[#eaeaea]",children:"Invite New Members"}),e.jsx("button",{onClick:C,className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:e.jsx("svg",{className:"w-4 h-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),e.jsx("p",{className:"text-[#b5b5b5] mb-4",children:"Search for users to invite to this community. They will receive an email with instructions to login and join."}),o.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-sm text-[#b5b5b5] mb-2",children:"Selected Users:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:o.map(b=>e.jsxs("div",{className:"inline-flex items-center gap-1 rounded bg-[#7dd87d] px-2 py-1 text-sm text-[#1e1e1e]",children:[e.jsx("span",{children:b.name.value}),e.jsx("button",{onClick:()=>re(b.id.value),className:"text-[#1e1e1e] hover:text-red-800 ml-1",children:"×"})]},b.id.value))})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search for users by name or email",value:D,onChange:R,className:"w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2 text-sm text-[#eaeaea] focus:border-[#7dd87d] focus:outline-none"}),Q&&D.trim()!==""&&e.jsx("div",{className:"absolute z-10 mt-1 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] py-2 shadow-lg max-h-60 overflow-y-auto",children:E?e.jsx("div",{className:"px-4 py-2 text-[#b5b5b5]",children:"Searching..."}):B.length>0?B.map(b=>{var $;return e.jsx("div",{onClick:()=>ae(b),className:"cursor-pointer px-4 py-2 text-[#eaeaea] hover:bg-[#363636]",children:e.jsxs("div",{className:"flex gap-2 items-center",children:[($=b.avatar)!=null&&$.value?e.jsx("img",{src:b.avatar.value,alt:"",className:"w-6 h-6 rounded-full"}):e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-[#7dd87d] text-sm text-[#1e1e1e]",children:b.name.value.charAt(0)}),e.jsxs("div",{children:[e.jsx("div",{children:b.name.value}),e.jsx("div",{className:"text-sm text-[#b5b5b5]",children:b.email.value})]})]})},b.id.value)}):e.jsx("div",{className:"px-4 py-2 text-[#b5b5b5]",children:"No users found"})})]}),e.jsx("div",{className:"mt-6 flex justify-end",children:e.jsx("button",{onClick:oe,disabled:v||o.length===0,className:"rounded-lg bg-[#7dd87d] px-4 py-2 text-sm text-[#1e1e1e] hover:bg-[#7dd87d]/90 disabled:opacity-50",children:v?"Sending...":"Send Invites"})})]})},gs=({text:m="",maxWords:C=100})=>{const[i,T]=a.useState(!1);if(!m)return e.jsx("p",{className:"whitespace-pre-line text-[#b5b5b5]",children:"No content available"});const D=m.split(/\s+/),V=D.length>C,B=i?m:D.slice(0,C).join(" ")+(V?"...":"");return e.jsx("div",{children:e.jsxs("p",{className:"whitespace-pre-line text-[#b5b5b5]",children:[B,V&&e.jsx("button",{onClick:()=>T(!i),className:"ml-2 text-[#7dd87d] hover:underline",children:i?"Read Less":"Read More"})]})})},fs={Technology:e.jsx("svg",{className:"h-8 w-8 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"})}),Business:e.jsx("svg",{className:"h-8 w-8 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),Finance:e.jsx("svg",{className:"h-8 w-8 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),Healthcare:e.jsx("svg",{className:"h-8 w-8 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})}),Education:e.jsx("svg",{className:"h-8 w-8 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})})},js=()=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"42",height:"33",viewBox:"0 0 42 33",fill:"none",children:e.jsx("path",{d:"M22.4766 5.79688C23.2781 5.30469 23.8125 4.41172 23.8125 3.40625C23.8125 1.85234 22.5539 0.59375 21 0.59375C19.4461 0.59375 18.1875 1.85234 18.1875 3.40625C18.1875 4.41875 18.7219 5.30469 19.5234 5.79688L15.4945 13.8547C14.8547 15.1344 13.1953 15.5 12.0773 14.607L5.8125 9.59375C6.16406 9.12266 6.375 8.53906 6.375 7.90625C6.375 6.35234 5.11641 5.09375 3.5625 5.09375C2.00859 5.09375 0.75 6.35234 0.75 7.90625C0.75 9.46016 2.00859 10.7188 3.5625 10.7188C3.57656 10.7188 3.59766 10.7188 3.61172 10.7188L6.825 28.3953C7.21172 30.5328 9.075 32.0938 11.2547 32.0938H30.7453C32.918 32.0938 34.7812 30.5398 35.175 28.3953L38.3883 10.7188C38.4023 10.7188 38.4234 10.7188 38.4375 10.7188C39.9914 10.7188 41.25 9.46016 41.25 7.90625C41.25 6.35234 39.9914 5.09375 38.4375 5.09375C36.8836 5.09375 35.625 6.35234 35.625 7.90625C35.625 8.53906 35.8359 9.12266 36.1875 9.59375L29.9227 14.607C28.8047 15.5 27.1453 15.1344 26.5055 13.8547L22.4766 5.79688Z",fill:"#7DD87D"})}),de=m=>{const C=new Date(m),i=C.toLocaleString("default",{month:"short"}),T=C.getDate(),D=C.getFullYear();return`${i} ${T}, ${D}`},Ns=({isOpen:m,onClose:C,post:i})=>{var X,he,ce,be,me,ve,pe,ge;const[T,D]=a.useState("details"),[V,B]=a.useState([]),[p,E]=a.useState([]),[se,Q]=a.useState(!1),[te,o]=a.useState(!1),[h,v]=a.useState(""),[g,A]=a.useState({content:"",due_date:""}),{dispatch:R}=a.useContext(xe);a.useEffect(()=>{m&&i&&(ae(),re())},[m,i]);const ae=async()=>{try{const P=await new L().callRawAPI(`/v1/api/dealmaker/user/notes?referral_id=${i.id.value}`,{},"GET");if(!P.error){const I=(P.list||[]).sort((G,J)=>new Date(J.created_at)-new Date(G.created_at));B(I)}}catch(u){console.error("Failed to load notes:",u)}},re=async()=>{try{const P=await new L().callRawAPI(`/v1/api/dealmaker/user/tasks?referral_id=${i.id.value}`,{},"GET");if(!P.error){const I=(P.list||[]).sort((G,J)=>new Date(G.due_date)-new Date(J.due_date));E(I)}}catch(u){console.error("Failed to load tasks:",u)}},oe=async()=>{var u,P,I,G,J;if(h.trim())try{const le=await new L().callRawAPI("/v1/api/dealmaker/user/notes",{content:{value:h},referral_id:{value:(u=i==null?void 0:i.id)==null?void 0:u.value}},"POST");if(le.error)throw new Error(le.message||"Failed to add note");{const we=new Date().toISOString(),ue={id:((I=(P=le.model)==null?void 0:P.id)==null?void 0:I.value)||Date.now(),description:h,created_at:((J=(G=le.model)==null?void 0:G.created_at)==null?void 0:J.value)||we};B(ke=>[ue,...ke]),v(""),Q(!1),ae(),f(R,"Note added successfully!",5e3,"success")}}catch(ne){console.error("Error adding note:",ne),f(R,ne.message||"Failed to add note",5e3,"error")}},b=async u=>{try{const I=await new L().callRawAPI(`/v1/api/dealmaker/user/notes/${u}`,{},"DELETE");if(!I.error)B(G=>G.filter(J=>J.id!==u)),f(R,"Note deleted successfully!",5e3,"success");else throw new Error(I.message||"Failed to delete note")}catch(P){console.error("Error deleting note:",P),f(R,P.message||"Failed to delete note",5e3,"error")}},$=async()=>{var u,P;if(!(!g.content.trim()||!g.due_date))try{const G=await new L().callRawAPI("/v1/api/dealmaker/user/tasks",{content:{value:g.content},due_date:{value:g.due_date},referral_id:{value:(u=i==null?void 0:i.id)==null?void 0:u.value}},"POST");if(G.error)throw new Error(G.message||"Failed to add task");{const J={id:((P=G.model)==null?void 0:P.id)||Date.now(),description:g.content,due_date:g.due_date,created_at:new Date().toISOString(),title:`Task added on ${new Date().toLocaleDateString()}`};E(ne=>[...ne,J]),A({content:"",due_date:""}),o(!1),re(),f(R,"Task added successfully!",5e3,"success")}}catch(I){console.error("Error adding task:",I),f(R,I.message||"Failed to add task",5e3,"error")}},k=async u=>{try{const I=await new L().callRawAPI(`/v1/api/dealmaker/user/tasks/${u}`,{},"DELETE");if(!I.error)E(G=>G.filter(J=>J.id!==u)),f(R,"Task deleted successfully!",5e3,"success");else throw new Error(I.message||"Failed to delete task")}catch(P){console.error("Error deleting task:",P),f(R,P.message||"Failed to delete task",5e3,"error")}};return!m||!i?null:e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-full items-center justify-center p-4",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:C,"aria-hidden":"true"}),e.jsx("div",{className:"relative z-50 w-full max-w-3xl rounded-lg bg-[#161616] shadow-xl",children:e.jsxs("div",{className:"flex flex-col h-[85vh]",children:[e.jsxs("div",{className:"sticky top-0 z-50 flex justify-between items-center bg-[#161616] p-6 border-b border-[#363636]",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx(Se,{user:i.creator}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-[#eaeaea]",children:i.title.value}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[i.posted_by.value," -"," ",de(i.created_at.value)]})]})]}),e.jsx("button",{onClick:C,className:"text-[#b5b5b5] hover:text-[#eaeaea] p-2",children:e.jsx("svg",{className:"w-5 h-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),e.jsx("div",{className:"border-b border-[#363636] px-6",children:e.jsxs("div",{className:"flex gap-4",children:[e.jsx("button",{onClick:()=>D("details"),className:`border-b-2 py-2 px-1 text-sm ${T==="details"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Details"}),e.jsx("button",{onClick:()=>D("notes"),className:`border-b-2 py-2 px-1 text-sm ${T==="notes"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Notes"}),e.jsx("button",{onClick:()=>D("tasks"),className:`border-b-2 py-2 px-1 text-sm ${T==="tasks"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Tasks"})]})}),e.jsxs("div",{className:"flex-1 overflow-y-auto p-6",children:[e.jsx("div",{className:`h-full ${T==="details"?"block":"hidden"}`,children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Type"}),e.jsx("p",{className:"text-[#b5b5b5]",children:((X=i==null?void 0:i.type)==null?void 0:X.value)||"N/A"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Description"}),e.jsx(gs,{text:(he=i==null?void 0:i.description)==null?void 0:he.value,maxWords:1e3})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Deal Size"}),e.jsxs("p",{className:"text-[#b5b5b5]",children:["$",((ce=i==null?void 0:i.deal_size)==null?void 0:ce.value)||"N/A"]})]}),((be=i==null?void 0:i.expiration_date)==null?void 0:be.value)&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Expiration Date"}),e.jsx("p",{className:"text-[#b5b5b5]",children:de(i.expiration_date.value)})]}),((me=i==null?void 0:i.description_image)==null?void 0:me.value)&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Attached Image"}),e.jsx("img",{src:i.description_image.value,alt:"Description",className:"max-h-96 rounded-lg object-contain"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Status"}),e.jsx("span",{className:"rounded-full bg-[#2e7d3233] px-3 py-1 text-sm text-[#7dd87d]",children:((ve=i==null?void 0:i.status)==null?void 0:ve.value)||"Unknown"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Referral Type"}),e.jsx("p",{className:"text-[#b5b5b5]",children:((pe=i==null?void 0:i.referral_type)==null?void 0:pe.value)||"N/A"})]}),((ge=i==null?void 0:i.recommendations)==null?void 0:ge.value)&&i.recommendations.value.length>0&&e.jsxs("div",{children:[e.jsxs("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:["Recommendations (",i.recommendations.value.length,")"]}),e.jsx("div",{className:"space-y-2",children:i.recommendations.value.map((u,P)=>{var I,G;return e.jsx("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:e.jsx("p",{className:"text-[#eaeaea]",children:(G=(I=u.user)==null?void 0:I.name)==null?void 0:G.value})},P)})})]})]})}),e.jsx("div",{className:`h-full ${T==="notes"?"block":"hidden"}`,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h4",{className:"text-lg font-medium text-[#eaeaea]",children:"Notes"}),e.jsxs("button",{onClick:()=>Q(!0),className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})}),"Add Note"]})]}),se&&e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:[e.jsx("textarea",{value:h,onChange:u=>v(u.target.value),placeholder:"Write your note...",className:"mb-4 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-[#eaeaea]",rows:3}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx("button",{onClick:()=>Q(!1),className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{onClick:oe,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Add Note"})]})]}),e.jsx("div",{className:"space-y-2",children:V.map(u=>e.jsx("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#eaeaea]",children:u.description}),e.jsx("p",{className:"mt-2 text-sm text-[#b5b5b5]",children:de(u.created_at)})]}),e.jsx("button",{onClick:()=>b(u.id),className:"text-[#b5b5b5] hover:text-[#dc3545]",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})},u.id))})]})}),e.jsx("div",{className:`h-full ${T==="tasks"?"block":"hidden"}`,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h4",{className:"text-lg font-medium text-[#eaeaea]",children:"Tasks"}),e.jsxs("button",{onClick:()=>o(!0),className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})}),"Add Task"]})]}),te&&e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Task Title"}),e.jsx("input",{type:"text",value:g.content,onChange:u=>A({...g,content:u.target.value}),placeholder:"Enter task title...",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Due Date"}),e.jsx("input",{type:"date",value:g.due_date,onChange:u=>A({...g,due_date:u.target.value}),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx("button",{onClick:()=>o(!1),className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{onClick:$,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Add Task"})]})]}),e.jsx("div",{className:"space-y-2",children:p.length===0?e.jsx("p",{className:"text-center text-[#b5b5b5] py-4",children:"No tasks yet. Create your first task!"}):p.map(u=>e.jsx("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#eaeaea] font-medium",children:u.description}),e.jsxs("p",{className:"mt-1 text-sm text-[#b5b5b5]",children:["Due: ",de(u.due_date)]})]}),e.jsx("button",{onClick:()=>k(u.id),className:"text-[#b5b5b5] hover:text-[#dc3545]",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})},u.id))})]})})]})]})})]})})},qs=()=>{const{dispatch:m}=a.useContext(xe),C=cs(),i=ms(),[T,D]=a.useState("joined"),[V,B]=a.useState([]),[p,E]=a.useState([]),[se,Q]=a.useState([]),[te,o]=a.useState(!0),[h,v]=a.useState(!1),[g,A]=a.useState(!0),[R,ae]=a.useState([]),[re,oe]=a.useState("");a.useState(!1);const[b,$]=a.useState("");a.useState(!1),a.useState([]),a.useState(!1),a.useState(""),a.useState(!1),a.useState(null),a.useState(!0);const[k,X]=a.useState(null),[he,ce]=a.useState(!1),[be,me]=a.useState(null),[ve,pe]=a.useState([]);a.useState(!1),a.useState("card"),a.useState({card_number:"",exp_month:"",exp_year:"",cvc:"",name:"",is_default:!1}),a.useState({}),a.useState(!1);const[ge,u]=a.useState(!1),[P,I]=a.useState(!1),[G,J]=a.useState([]),[ne,le]=a.useState(!1),[we,ue]=a.useState(!1),[ke,Ce]=a.useState(null),[Fe,Me]=a.useState(!1),[De,Be]=a.useState(null);console.log(h),a.useEffect(()=>{(async()=>{var t,r;if((t=i.state)!=null&&t.showCommunity&&((r=i.state)!=null&&r.communityId)){try{const c=await new L().GetCommunityDetail(i.state.communityId);c.error||(X(c.model),v(!0),We(i.state.communityId))}catch(n){console.error("Error loading community:",n),f(m,"Failed to load community",5e3,"error")}window.history.replaceState({},document.title)}})(),Le(),Ge(),es()},[]),a.useEffect(()=>{(async()=>{var j;const t=new URLSearchParams(i.search),r=t.get("join_id"),n=t.get("community_invitation"),c=t.get("ref");if(r)try{const w=await new L().GetCommunityDetail(r);if(w.error)f(m,"Community not found",5e3,"error");else{X(w.model),u(!0);const q=window.location.pathname;window.history.replaceState({},"",q)}}catch(y){console.error("Error fetching community:",y),f(m,"Failed to load community information",5e3,"error")}if(n&&c)try{const w=await new L().GetCommunityDetail(n);if(w.error)f(m,"Community not found",5e3,"error");else{X(w.model),u(!0),f(m,`You've been invited to join ${(j=w.model.title)==null?void 0:j.value}. Do you want to join?`,8e3,"info");const q=window.location.pathname;window.history.replaceState({},"",q)}}catch(y){console.error("Error fetching community:",y),f(m,"Failed to load community information",5e3,"error")}})()},[i.search,m]);const Ee=(s,t)=>{let r;return function(...c){const j=()=>{clearTimeout(r),s(...c)};clearTimeout(r),r=setTimeout(j,t)}},Le=async()=>{try{A(!0);const t=await new L().GetCommunities();if(t.error)console.error("Error loading communities:",t.message),oe(t.message);else{const r=t.list||[];console.log("Loaded communities:",r.length),E(r),B(r)}}catch(s){console.error("Exception loading communities:",s),oe(s.message||"Failed to load communities")}finally{A(!1)}},Re=s=>{if(!s.trim()||!p.length){B(p);return}const t=s.toLowerCase(),r=p.filter(n=>{var q,Z,M,ee,U,ie;const c=(Z=(q=n.title)==null?void 0:q.value)==null?void 0:Z.toLowerCase().includes(t),j=(ee=(M=n.description)==null?void 0:M.value)==null?void 0:ee.toLowerCase().includes(t),y=(ie=(U=n.industry_name)==null?void 0:U.value)==null?void 0:ie.toLowerCase().includes(t);return n={...n,_searchMatches:{title:c,description:j,industry:y}},c||j||y});console.log(`Filtered ${p.length} communities to ${r.length} results for query "${t}"`),(r.length>0||!s.trim())&&B(r)},$e=Ae.useCallback(Ee(s=>Re(s),300),[p]);a.useEffect(()=>{p.length>0&&b.trim()&&Re(b)},[b,p]),a.useEffect(()=>{console.log("Tab changed to:",T),p.length>0&&(B(p),b&&$(""))},[T]);const ze=s=>{const t=s.target.value;$(t),!t.trim()&&p.length>0?B(p):t.trim()&&$e(t)},Ge=async()=>{try{o(!0);const t=await new L().GetCommunityFeed({limit:10});t.error||Q(t.list||[])}catch(s){console.error("Failed to load activity:",s)}finally{o(!1)}},Ve=async s=>{console.log("Viewing community:",s);try{A(!0);const t=new L,r=await t.GetCommunityDetail(s);r.error||X(r.model);const n=await t.GetCommunityReferrals(s);n.error||ae(n.list||[]),v(!0),D("joined")}catch(t){console.error("Failed to load community:",t),f(m,"Failed to load community",5e3,"error")}finally{A(!1)}},He=()=>{var s,t,r,n,c,j;return console.log("Rendering community view:",{selectedCommunity:k,communityPosts:R}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-full bg-[#363636] text-[#eaeaea] text-lg",children:(t=(s=k.title)==null?void 0:s.value)==null?void 0:t.charAt(0)}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:k.title.value}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:k.industry_name.value})]})]}),e.jsxs("div",{className:"flex gap-4 items-center",children:[!(((r=k.title)==null?void 0:r.value)==="RainmakerOS"&&((n=k.id)==null?void 0:n.value)===1)&&e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>{fe(k.id.value),I(!0)},className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"})}),"Members (",((c=k.member_count)==null?void 0:c.value)||0,")"]}),e.jsxs("button",{onClick:()=>C(`/member/communities/${k.id.value}/chat`),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),"Chat"]})]}),e.jsxs("button",{onClick:()=>{v(!1),X(null),ae([])},className:"flex items-center gap-2 text-sm text-[#7dd87d]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back"]})]})]}),e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#161616] p-6",children:[e.jsx("h3",{className:"mb-2 text-lg font-medium text-[#eaeaea]",children:"About"}),e.jsx("p",{className:"text-[#b5b5b5]",children:(j=k.description)==null?void 0:j.value})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:"Posts"})}),R.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg border border-[#363636] bg-[#161616] p-8",children:[e.jsx("svg",{className:"mb-4 h-12 w-12 text-[#b5b5b5]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2"})}),e.jsx("p",{className:"text-lg font-medium text-[#eaeaea]",children:"No posts yet"})]}):e.jsx("div",{className:"grid grid-cols-1 gap-6",children:R.map(y=>e.jsxs("div",{className:"flex flex-col gap-2 rounded-lg border border-[#363636] bg-[#161616] p-4",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsx(Se,{user:y.creator}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-[#eaeaea]",children:y.title.value}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[y.creator.name.value," -"," ",de(y.created_at.value)]})})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("button",{onClick:()=>is(y),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#242424]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})}),"View Details"]})})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Description"}),e.jsx("p",{className:"text-[#b5b5b5]",children:y.description.value})]}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("button",{onClick:()=>Ue(y.id.value),className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-white",children:"Refer"}),e.jsx("button",{onClick:()=>Ke(y.id.value),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]",children:"Chat"}),e.jsx("button",{onClick:()=>Qe(y.id.value),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]",children:"Repost"})]})]},y.id.value))})]}),e.jsx(Te,{isOpen:P,onClose:()=>I(!1)})]})},We=async s=>{try{A(!0);const r=await new L().GetCommunityReferrals(s);r.error||ae(r.list||[])}catch(t){console.error("Failed to load posts:",t)}finally{A(!1)}},qe=()=>{var s,t;return e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("div",{className:"flex gap-4 items-center",children:k?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-full bg-[#363636] text-[#eaeaea] text-lg",children:(t=(s=k.title)==null?void 0:s.value)==null?void 0:t.charAt(0)}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:k.title.value}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:k.industry_name.value})]})]}):e.jsxs(e.Fragment,{children:[e.jsx("img",{src:hs,alt:"RainmakerOS LLC",className:"w-12 h-12 rounded-full"}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:"RainmakerOS LLC"}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Community Management"})]})]})}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:b,onChange:ze,placeholder:"Search communities...",className:"h-[42px] w-64 border border-[#363636] bg-[#242424] pl-4 pr-10 text-[#eaeaea] placeholder-[#666]"}),b?e.jsx("button",{onClick:()=>{$(""),B(p)},className:"absolute top-1/2 right-3 -translate-y-1/2 cursor-pointer",title:"Clear search",children:e.jsx("svg",{className:"w-4 h-4 text-[#b5b5b5] hover:text-[#eaeaea]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}):e.jsx("div",{style:{top:"50%",right:"10px",transform:"translateY(-50%)"},className:"absolute right-3 text-white",children:e.jsx("svg",{className:"w-4 h-4",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M15.7955 15.8111L21 21M18 10.5C18 14.6421 14.6421 18 10.5 18C6.35786 18 3 14.6421 3 10.5C3 6.35786 6.35786 3 10.5 3C14.6421 3 18 6.35786 18 10.5Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsxs("button",{onClick:ns,className:"flex h-[42px] items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Post Opportunity"]})]})]})},Je=()=>e.jsxs("div",{className:"mb-6 border-b border-[#363636]",children:[e.jsx("button",{onClick:()=>D("joined"),className:`mr-6 border-b-2 pb-2 text-sm ${T==="joined"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"My Communities"}),e.jsx("button",{onClick:()=>{D("join"),v(!1)},className:`mr-6 border-b-2 pb-2 text-sm ${T==="join"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Join a Community"}),e.jsx("button",{onClick:()=>C("/member/communities/create"),className:`border-b-2 pb-2 text-sm ${T==="create"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Create a Community"})]}),Oe=()=>e.jsxs("div",{className:"flex flex-1",children:[e.jsxs("div",{style:{width:"65%"},className:"",children:[e.jsxs("div",{style:{padding:"16px",marginRight:"16px"},className:"mb-8 border-b border-[#363636] bg-black",children:[e.jsx("h2",{className:"mb-4 text-sm font-semibold text-[#eaeaea]",children:"Your Communities"}),e.jsx("div",{className:"space-y-4",children:g?[...Array(2)].map((s,t)=>e.jsx(Ie,{className:"w-full h-24 rounded-lg"},t)):V.filter(s=>{var t;return((t=s.is_member)==null?void 0:t.value)===!0}).sort((s,t)=>{var c,j,y,w;const r=((c=s.title)==null?void 0:c.value)==="RainmakerOS"&&((j=s.id)==null?void 0:j.value)===1,n=((y=t.title)==null?void 0:y.value)==="RainmakerOS"&&((w=t.id)==null?void 0:w.value)===1;return r&&!n?-1:!r&&n?1:0}).map(s=>{var t,r,n,c,j,y,w;return e.jsxs("div",{className:"flex items-center justify-between rounded-lg border border-[#363636] bg-[#242424] p-4",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[((t=s.title)==null?void 0:t.value)==="RainmakerOS"&&((r=s.id)==null?void 0:r.value)===1&&e.jsx("svg",{className:"h-5 w-5 text-[#7dd87d]",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M16 12V4a1 1 0 00-.5-.87L12 1 8.5 3.13A1 1 0 008 4v8l-2 2v1h12v-1l-2-2z"})}),((n=s.privacy)==null?void 0:n.value)==="private"&&e.jsx("svg",{className:"h-5 w-5 text-[#b5b5b5]",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M12 17a2 2 0 100-4 2 2 0 000 4zm6-9a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V10a2 2 0 012-2h1V6a5 5 0 0110 0v2h1zm-6-5a3 3 0 00-3 3v2h6V6a3 3 0 00-3-3z"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-[#eaeaea] flex items-center gap-2",children:s.title.value}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[((c=s.privacy_settings)==null?void 0:c.value.who_can_find)==="hidden"?"Hidden":((j=s.privacy_settings)==null?void 0:j.value.who_can_find)==="private"?"Private":"Public"," ","Community • ",((y=s.member_count)==null?void 0:y.value)||0," ","members"]})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>Ve(s.id.value),className:"rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea]",children:"View"}),((w=s.is_admin)==null?void 0:w.value)===!0&&e.jsx("button",{onClick:()=>as(s.id.value),className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Manage"})]})]},s.id.value)})})]}),e.jsxs("div",{style:{padding:"16px",marginRight:"16px"},className:"mb-8 border-b border-[#363636] bg-black",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-sm font-semibold text-[#eaeaea]",children:"Available Communities"}),e.jsx("button",{onClick:()=>D("join"),className:"text-sm text-[#7dd87d]",children:"View All"})]}),e.jsx("div",{className:"space-y-4",children:g?[...Array(2)].map((s,t)=>e.jsx(Ie,{className:"w-full h-24 rounded-lg"},t)):V.filter(s=>{var t;return((t=s.is_member)==null?void 0:t.value)===!1}).map(s=>{var t,r,n;return e.jsxs("div",{className:"flex items-center justify-between rounded-lg border border-[#363636] bg-[#242424] p-4",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[((t=s.privacy)==null?void 0:t.value)==="private"&&e.jsx("svg",{className:"h-5 w-5 text-[#b5b5b5]",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M12 17a2 2 0 100-4 2 2 0 000 4zm6-9a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V10a2 2 0 012-2h1V6a5 5 0 0110 0v2h1zm-6-5a3 3 0 00-3 3v2h6V6a3 3 0 00-3-3z"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-[#eaeaea]",children:s.title.value}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[((r=s.privacy)==null?void 0:r.value)==="private"?"Private":"Public"," ","Community • ",((n=s.member_count)==null?void 0:n.value)||0," ","members"]})]})]}),e.jsx("button",{onClick:()=>Pe(s),className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Join"})]},s.id.value)})})]})]}),Ye()]}),Ye=()=>e.jsx("div",{style:{width:"35%"},className:"space-y-6",children:e.jsxs("div",{className:"p-4 bg-black rounded",children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:"Recent Activity"}),e.jsx("div",{className:"space-y-4",children:se.map(s=>e.jsxs("div",{className:"border-b border-[#363636] pb-4",children:[e.jsx("p",{className:"text-sm text-[#eaeaea]",children:s.title.value}),e.jsx("p",{className:"text-xs text-[#b5b5b5]",children:de(new Date(s.created_at.value))})]},s.id.value))})]})}),Pe=s=>{X(s),u(!0)},Ze=()=>e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:"All Communities"})}),b.trim()&&V.length>0&&e.jsxs("div",{className:"mb-4 p-2 bg-[#1e1e1e] border border-[#363636] rounded-lg",children:[e.jsxs("p",{className:"text-[#eaeaea]",children:["Found ",e.jsx("span",{className:"font-semibold text-[#7dd87d]",children:V.length}),' communities matching "',b,'"']}),e.jsx("p",{className:"text-[#b5b5b5] text-sm mt-1",children:"Searching in title, description, and industry fields"})]}),b.trim()&&V.length===0&&!g&&e.jsxs("div",{className:"flex flex-col items-center justify-center p-8 text-center mb-4",children:[e.jsx("svg",{className:"w-16 h-16 mb-4 text-[#363636]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),e.jsx("h3",{className:"text-xl font-medium text-[#eaeaea] mb-2",children:"No communities found"}),e.jsxs("p",{className:"text-[#b5b5b5]",children:['No communities matching "',b,'" were found in titles, descriptions, or industry fields. Try a different search term.']})]}),e.jsx("div",{className:"grid grid-cols-3 gap-6",children:V.filter(s=>{var t;return((t=s.is_member)==null?void 0:t.value)===!1}).map(s=>{var t,r,n,c;return e.jsxs("div",{className:"flex flex-col rounded-lg border border-[#363636] bg-[#161616] p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("div",{className:"mr-4 mb-2",children:fs[(t=s.industry)==null?void 0:t.value]||e.jsx("svg",{className:"h-8 w-8 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}),e.jsxs("div",{className:"flex justify-between items-center w-full",children:[e.jsx("h3",{className:"mb-1 font-semibold text-[#eaeaea]",children:s.title.value}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[((r=s.member_count)==null?void 0:r.value)||0," members"]})]}),((n=s.privacy)==null?void 0:n.value)==="private"&&e.jsx("svg",{className:"h-5 w-5 text-[#b5b5b5]",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M12 17a2 2 0 100-4 2 2 0 000 4zm6-9a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V10a2 2 0 012-2h1V6a5 5 0 0110 0v2h1zm-6-5a3 3 0 00-3 3v2h6V6a3 3 0 00-3-3z"})})]}),e.jsx("p",{className:"mb-6 flex-grow text-sm text-[#b5b5b5]",children:(c=s.description)==null?void 0:c.value}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("p",{className:"text-sm text-[#7dd87d]",children:"Active"}),e.jsx("button",{onClick:()=>Pe(s),className:" rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Join Community"})]})]},s.id.value)})})]}),Ue=async s=>{Ce(s),ue(!0)},Ke=async s=>{C(`/member/chat/${s}`)},Qe=s=>{me(s),ce(!0)},Xe=({isOpen:s,onClose:t,referralId:r,communities:n})=>{if(!s)return null;const{dispatch:c}=a.useContext(xe),[j,y]=a.useState(""),[w,q]=a.useState(!1),Z=async()=>{if(j){q(!0);try{await new L().RepostReferral({referral_id:r,community_id:j}),f(c,"Referral reposted successfully!",5e3,"success"),t(!0)}catch(M){f(c,M.message||"Failed to repost referral",5e3,"error")}finally{q(!1)}}};return e.jsxs("div",{className:"flex fixed inset-0 z-50 justify-center items-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:()=>!w&&t()}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-lg bg-[#161616] p-6 shadow-xl",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-[#eaeaea]",children:"Select Community to Repost"}),e.jsx("button",{onClick:()=>!w&&t(),className:"text-[#b5b5b5] hover:text-[#eaeaea] disabled:opacity-50",disabled:w,children:e.jsx("svg",{className:"w-5 h-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),e.jsxs("select",{value:j,onChange:M=>y(M.target.value),className:"mb-6 h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]",required:!0,children:[e.jsx("option",{value:"",children:"Select a community"}),n.map(M=>e.jsx("option",{value:M.id.value,children:M.title.value},M.id.value))]}),e.jsxs("div",{className:"flex gap-4 justify-end",children:[e.jsx("button",{onClick:()=>!w&&t(),disabled:w,className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea] disabled:opacity-50",children:"Cancel"}),e.jsx("button",{onClick:Z,disabled:w||!j,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-white hover:bg-[#1b5e20] disabled:opacity-50",children:w?"Reposting...":"Repost"})]})]})]})},es=async()=>{try{const t=await new L().GetJoinedCommunities();t.error||pe(t.list||[])}catch(s){console.error("Failed to load communities:",s)}},ss=({isOpen:s,onClose:t,community:r})=>{if(!s)return null;const[n,c]=a.useState(1),[j,y]=a.useState(!1),[w,q]=a.useState(!1),[Z,M]=a.useState({}),[ee,U]=a.useState(!1),[ie,je]=a.useState([]),[d,O]=a.useState({firstName:"",lastName:"",industry:"",employeeCount:"",annualRevenue:"",goals:{buildPartnerships:!1,gainAccess:!1,getResources:!1,learnStrategies:!1,findSolutions:!1,other:!1},otherGoal:"",businessChallenge:"",majorBreakthrough:"",whyJoin:"",meetingCommitment:"",additionalNotes:"",agreeToTerms:!1,agreeToGuidelines:!1});a.useEffect(()=>{const S=async()=>{try{const N=await new L().callRawAPI("/v1/api/dealmaker/user/details",{},"GET");!N.error&&N.model&&O(ye=>({...ye,firstName:N.model.first_name.value||"",lastName:N.model.last_name.value||""}))}catch(l){console.error("Failed to fetch user details:",l)}},F=async()=>{try{const N=await new L().callRawAPI("/v1/api/dealmaker/industries",{},"GET");!N.error&&N.data?je(N.data):f(m,N.message||"Failed to load industries",5e3,"error")}catch(l){f(m,l.message||"Failed to load industries",5e3,"error")}};S(),F()},[]);const x=(S,F)=>{M(l=>({...l,[S]:!0})),O(l=>({...l,[S]:F}))},_=S=>{var F;return(Z[S]||ee)&&!((F=d[S])!=null&&F.trim())},H=()=>(Z.goals||ee)&&!Object.values(d.goals).some(S=>S===!0),Y=()=>(Z.otherGoal||ee)&&d.goals.other&&!d.otherGoal.trim(),K=()=>{U(!0),W()&&c(2)},W=()=>!(!d.firstName.trim()||!d.lastName.trim()||!d.industry.trim()||!d.employeeCount.trim()||!d.annualRevenue.trim()||!d.businessChallenge.trim()||!d.majorBreakthrough.trim()||!d.whyJoin.trim()||!d.meetingCommitment.trim()||!Object.values(d.goals).some(F=>F===!0)||d.goals.other&&!d.otherGoal.trim()),z=async(S=!1)=>{if(!d.agreeToTerms||!d.agreeToGuidelines){f(m,"Please agree to the terms and guidelines",5e3,"error");return}if(!S){q(!0);return}try{y(!0);const l=await new L().JoinCommunity({community_id:{value:r.id.value},first_name:{value:d.firstName},last_name:{value:d.lastName},industry_id:d.industry?parseInt(d.industry,10):null,company_size:{value:d.employeeCount},meeting_commitment:{value:d.meetingCommitment},additional_notes:{value:d.additionalNotes},goals:{value:{networking:d.goals.buildPartnerships,businessDevelopment:d.goals.gainAccess,learningAndDevelopment:d.goals.getResources}},skip_payment:S});l.error?f(m,l.message,5e3,"error"):(f(m,"Successfully joined community!",5e3,"success"),t(!0),C("/member/communities"),u(!1))}catch(F){f(m,F.message||"Failed to join community",5e3,"error")}finally{y(!1)}},Ne=()=>{var S,F;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:["Join ",(S=r==null?void 0:r.title)==null?void 0:S.value," Community"]}),_e(),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name *"}),e.jsx("input",{type:"text",value:d.firstName,onChange:l=>x("firstName",l.target.value),onBlur:()=>M(l=>({...l,firstName:!0})),className:`h-10 w-full rounded-lg border ${_("firstName")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] px-4 text-[#eaeaea]`,placeholder:"Enter first name",required:!0}),_("firstName")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"First name is required"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name *"}),e.jsx("input",{type:"text",value:d.lastName,onChange:l=>x("lastName",l.target.value),onBlur:()=>M(l=>({...l,lastName:!0})),className:`h-10 w-full rounded-lg border ${_("lastName")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] px-4 text-[#eaeaea]`,placeholder:"Enter last name",required:!0}),_("lastName")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Last name is required"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"What industry is your business in? *"}),e.jsxs("select",{value:d.industry,onChange:l=>x("industry",l.target.value),onBlur:()=>M(l=>({...l,industry:!0})),className:`h-10 w-full appearance-none rounded-lg border ${_("industry")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] px-4 text-[#eaeaea]`,required:!0,children:[e.jsx("option",{value:"",children:"Select your industry"}),ie.map(l=>e.jsx("option",{value:l.id,children:l.name},l.id))]}),_("industry")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Industry is required"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"How many employees does your business have? *"}),e.jsxs("select",{value:d.employeeCount,onChange:l=>x("employeeCount",l.target.value),onBlur:()=>M(l=>({...l,employeeCount:!0})),className:`h-10 w-full appearance-none rounded-lg border ${_("employeeCount")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] px-4 text-[#eaeaea]`,required:!0,children:[e.jsx("option",{value:"",children:"Select range"}),e.jsx("option",{value:"1-10",children:"1-10"}),e.jsx("option",{value:"11-50",children:"11-50"}),e.jsx("option",{value:"51-200",children:"51-200"}),e.jsx("option",{value:"201-500",children:"201-500"}),e.jsx("option",{value:"500+",children:"500+"})]}),_("employeeCount")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Employee count is required"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"What's your approximate annual revenue? *"}),e.jsxs("select",{value:d.annualRevenue,onChange:l=>x("annualRevenue",l.target.value),onBlur:()=>M(l=>({...l,annualRevenue:!0})),className:`h-10 w-full appearance-none rounded-lg border ${_("annualRevenue")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] px-4 text-[#eaeaea]`,required:!0,children:[e.jsx("option",{value:"",children:"Select range"}),e.jsx("option",{value:"under_100k",children:"under 100K"}),e.jsx("option",{value:"100k_500k",children:"100K - 500K"}),e.jsx("option",{value:"500k_1m",children:"500K - 1M"}),e.jsx("option",{value:"1m_5m",children:"1M - 5M"}),e.jsx("option",{value:"5m_plus",children:"5M+"})]}),_("annualRevenue")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Annual revenue is required"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"What are your top 3 goals for joining this community? *"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:d.goals.buildPartnerships,onChange:l=>O(N=>({...N,goals:{...N.goals,buildPartnerships:l.target.checked}})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Build partnerships to grow my business"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:d.goals.gainAccess,onChange:l=>O(N=>({...N,goals:{...N.goals,gainAccess:l.target.checked}})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Gain access to investors and capital partners"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:d.goals.getResources,onChange:l=>O(N=>({...N,goals:{...N.goals,getResources:l.target.checked}})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Get access to new resources and opportunities"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:d.goals.learnStrategies,onChange:l=>O(N=>({...N,goals:{...N.goals,learnStrategies:l.target.checked}})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Learn strategies to leverage relationships"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:d.goals.findSolutions,onChange:l=>O(N=>({...N,goals:{...N.goals,findSolutions:l.target.checked}})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Find solutions to specific business challenges"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:d.goals.other,onChange:l=>O(N=>({...N,goals:{...N.goals,other:l.target.checked}})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Other:"})]}),d.goals.other&&e.jsx("input",{type:"text",value:d.otherGoal,onChange:l=>O(N=>({...N,otherGoal:l.target.value})),className:"mt-2 h-10 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]",placeholder:"Please specify"})]}),H()&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Please select at least one goal"}),Y()&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Please specify your other goal"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"What is your biggest business challenge that needs solving sooner rather than later? *"}),e.jsx("textarea",{value:d.businessChallenge,onChange:l=>x("businessChallenge",l.target.value),onBlur:()=>M(l=>({...l,businessChallenge:!0})),className:`h-32 w-full rounded-lg border ${_("businessChallenge")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] p-4 text-[#eaeaea]`,placeholder:"Describe your challenge...",required:!0}),_("businessChallenge")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Business challenge is required"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"If you could achieve one major breakthrough in your business this year, what would it be? *"}),e.jsx("textarea",{value:d.majorBreakthrough,onChange:l=>x("majorBreakthrough",l.target.value),onBlur:()=>M(l=>({...l,majorBreakthrough:!0})),className:`h-32 w-full rounded-lg border ${_("majorBreakthrough")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] p-4 text-[#eaeaea]`,placeholder:"Describe your desired breakthrough...",required:!0}),_("majorBreakthrough")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Major breakthrough is required"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:["Why do you want to be part of ",(F=r==null?void 0:r.title)==null?void 0:F.value,"? *"]}),e.jsx("textarea",{value:d.whyJoin,onChange:l=>x("whyJoin",l.target.value),onBlur:()=>M(l=>({...l,whyJoin:!0})),className:`h-32 w-full rounded-lg border ${_("whyJoin")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] p-4 text-[#eaeaea]`,placeholder:"Share your reasons...",required:!0}),_("whyJoin")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"This field is required"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"How much time can you realistically commit to meeting with members and engaging in potential deals each month? *"}),e.jsx("div",{className:"space-y-2",children:["1-2 hours","3-5 hours","6+ hours"].map(l=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"meetingCommitment",value:l.toLowerCase(),checked:d.meetingCommitment===l.toLowerCase(),onChange:N=>x("meetingCommitment",N.target.value),onBlur:()=>M(N=>({...N,meetingCommitment:!0})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:l})]},l))}),_("meetingCommitment")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Please select your time commitment"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Is there anything else you'd like us to know?"}),e.jsx("textarea",{value:d.additionalNotes,onChange:l=>x("additionalNotes",l.target.value),className:"h-32 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-4 text-[#eaeaea]",placeholder:"Share any additional information..."})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:K,disabled:ee&&!W(),className:"rounded-lg bg-[#2e7d32] px-6 py-2 text-sm text-white disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})})]})},_e=()=>e.jsx("div",{className:"flex justify-center items-center mb-6",children:[1,2,3].map(S=>e.jsxs(Ae.Fragment,{children:[e.jsx("div",{onClick:()=>S<n&&c(S),className:`h-2 w-2 rounded-full ${n>=S?"cursor-pointer bg-[#2e7d32]":"bg-[#363636]"}`}),S<3&&e.jsx("div",{className:`h-[2px] w-16 ${n>S?"bg-[#2e7d32]":"bg-[#363636]"}`})]},S))}),ds=()=>{var S;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx("svg",{className:"h-5 w-5 text-[#7dd87d]",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("g",{clipPath:"url(#clip0_1_486)",children:e.jsx("path",{d:"M4.5 0C5.16304 0 5.79893 0.263392 6.26777 0.732233C6.73661 1.20107 7 1.83696 7 2.5C7 3.16304 6.73661 3.79893 6.26777 4.26777C5.79893 4.73661 5.16304 5 4.5 5C3.83696 5 3.20107 4.73661 2.73223 4.26777C2.26339 3.79893 2 3.16304 2 2.5C2 1.83696 2.26339 1.20107 2.73223 0.732233C3.20107 0.263392 3.83696 0 4.5 0ZM16 0C16.663 0 17.2989 0.263392 17.7678 0.732233C18.2366 1.20107 18.5 1.83696 18.5 2.5C18.5 3.16304 18.2366 3.79893 17.7678 4.26777C17.2989 4.73661 16.663 5 16 5C15.337 5 14.7011 4.73661 14.2322 4.26777C13.7634 3.79893 13.5 3.16304 13.5 2.5C13.5 1.83696 13.7634 1.20107 14.2322 0.732233C14.7011 0.263392 15.337 0 16 0ZM0 9.33438C0 7.49375 1.49375 6 3.33437 6H4.66875C5.16562 6 5.6375 6.10938 6.0625 6.30312C6.02187 6.52812 6.00313 6.7625 6.00313 7C6.00313 8.19375 6.52812 9.26562 7.35625 10C7.35 10 7.34375 10 7.33437 10H0.665625C0.3 10 0 9.7 0 9.33438ZM12.6656 10C12.6594 10 12.6531 10 12.6438 10C13.475 9.26562 13.9969 8.19375 13.9969 7C13.9969 6.7625 13.975 6.53125 13.9375 6.30312C14.3625 6.10625 14.8344 6 15.3313 6H16.6656C18.5063 6 20 7.49375 20 9.33438C20 9.70312 19.7 10 19.3344 10H12.6656ZM7 7C7 6.20435 7.31607 5.44129 7.87868 4.87868C8.44129 4.31607 9.20435 4 10 4C10.7956 4 11.5587 4.31607 12.1213 4.87868C12.6839 5.44129 13 6.20435 13 7C13 7.79565 12.6839 8.55871 12.1213 9.12132C11.5587 9.68393 10.7956 10 10 10C9.20435 10 8.44129 9.68393 7.87868 9.12132C7.31607 8.55871 7 7.79565 7 7ZM4 15.1656C4 12.8656 5.86562 11 8.16562 11H11.8344C14.1344 11 16 12.8656 16 15.1656C16 15.625 15.6281 16 15.1656 16H4.83437C4.375 16 4 15.6281 4 15.1656Z",fill:"#7dd87d"})})}),e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:"Community Guidelines"})]}),e.jsx("p",{className:"ml-4 text-sm text-[#b5b5b5]",children:r.title.value}),_e(),e.jsx("div",{style:{maxHeight:"400px",scrollbarWidth:"thin",scrollbarColor:"#7dd87d #242424",overflowY:"auto"},className:"rounded-lg bg-[#242424] p-4",children:e.jsx("div",{className:"text-[#b5b5b5]",dangerouslySetInnerHTML:{__html:((S=r.requirements)==null?void 0:S.value)||"No specific guidelines provided."}})}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("label",{className:"flex items-start",children:[e.jsx("input",{type:"checkbox",checked:d.agreeToGuidelines,onChange:F=>O(l=>({...l,agreeToGuidelines:F.target.checked})),className:"mt-1 mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"I agree to follow the community guidelines"})]}),e.jsxs("label",{className:"flex items-start",children:[e.jsx("input",{type:"checkbox",checked:d.agreeToTerms,onChange:F=>O(l=>({...l,agreeToTerms:F.target.checked})),className:"mt-1 mr-2"}),e.jsxs("span",{className:"text-sm text-[#eaeaea]",children:["I agree to the"," ",e.jsx("a",{href:"#",className:"text-[#7dd87d]",children:"Terms & Conditions"})]})]})]}),e.jsxs("div",{className:"flex gap-4 justify-end",children:[e.jsx("button",{onClick:()=>c(1),className:"rounded-lg border border-[#363636] px-6 py-2 text-sm text-[#b5b5b5]",children:"Back"}),e.jsx("button",{onClick:()=>c(3),disabled:!d.agreeToGuidelines||!d.agreeToTerms,className:"rounded-lg bg-[#2e7d32] px-6 py-2 text-sm text-white disabled:opacity-50",children:"Next"})]})]})},os=()=>{var S,F,l,N,ye;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col justify-center items-center",children:[e.jsx(js,{}),e.jsx("h2",{className:"mt-4 text-center text-xl font-semibold text-[#eaeaea]",children:"To Join a Community, Please Pay Your Community Fee"})]}),_e(),e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-sm font-medium text-[#7dd87d]",children:"Premium Plan"}),e.jsxs("div",{className:"mt-4 text-xl font-bold text-[#a3eca3]",children:["$",Number(((S=r.subscription_fee)==null?void 0:S.value)||((l=(F=r.privacy_settings)==null?void 0:F.value)==null?void 0:l.subscription_fee)||"0").toFixed(2)]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx("svg",{className:"h-5 w-5 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsx("span",{className:"text-[#eaeaea]",children:"Post Opportunities"})]}),e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx("svg",{className:"h-5 w-5 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsx("span",{className:"text-[#eaeaea]",children:"Get updated Opportunities"})]}),e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx("svg",{className:"h-5 w-5 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsx("span",{className:"text-[#eaeaea]",children:"Priority customer support"})]}),((ye=(N=r.privacy_settings)==null?void 0:N.value)==null?void 0:ye.enable_affiliate)&&e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx("svg",{className:"h-5 w-5 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e.jsx("span",{className:"text-[#eaeaea]",children:"Get enrolled into Affiliate Program"})]})]}),e.jsxs("div",{className:"flex flex-col gap-3 mt-6",children:[e.jsx("button",{onClick:()=>z(!1),disabled:j,className:"w-full rounded-lg bg-[#2e7d32] px-6 py-3 text-center text-sm font-medium text-white hover:bg-[#1b5e20] disabled:opacity-50",children:j?"Processing...":"Upgrade Now"}),e.jsx("button",{onClick:()=>z(!0),disabled:j,className:"w-full rounded-lg border border-[#363636] px-6 py-3 text-center text-sm text-[#b5b5b5] hover:text-[#eaeaea] disabled:opacity-50",children:"Maybe Later"}),e.jsx("a",{href:"#",className:"mt-2 text-center text-sm text-[#7dd87d] hover:underline",children:"View all plan features"})]})]})]})};return e.jsxs("div",{className:"flex fixed inset-0 z-50 justify-center items-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:()=>!j&&t()}),e.jsxs("div",{style:{scrollbarWidth:"thin",scrollbarColor:"#7dd87d #242424"},className:"relative z-50 max-h-[90vh]  w-full max-w-2xl overflow-y-auto rounded-lg bg-[#252525] p-6 shadow-xl",children:[n===1&&Ne(),n===2&&ds(),n===3&&os(),w&&e.jsx(ts,{onClose:()=>q(!1),joinFormData:d,community:r})]})]})},ts=({onClose:s,joinFormData:t,community:r})=>{const[n,c]=a.useState({card_number:"",exp_month:"",exp_year:"",cvc:"",name:"",is_default:!1}),[j,y]=a.useState("card"),[w,q]=a.useState({}),[Z,M]=a.useState(!1),ee=x=>{const H=x.replace(/\s+/g,"").replace(/[^0-9]/gi,"").match(/\d{4,16}/g),Y=H&&H[0]||"",K=[];for(let W=0,z=Y.length;W<z;W+=4)K.push(Y.substring(W,W+4));return K.length?K.join(" "):x},U=x=>{const{name:_,value:H,type:Y,checked:K}=x.target;if(Y==="checkbox"){c(z=>({...z,[_]:K}));return}let W=H;switch(_){case"card_number":if(W=ee(H),W.replace(/\s/g,"").length>16)return;break;case"exp_month":const z=H.replace(/\D/g,"");if(z===""){W="";break}if(z.length>2)return;const Ne=parseInt(z);if(z.length===1){W=z;break}if(z.length===2){if(Ne<1||Ne>12)return;W=z;break}W=z;break;case"exp_year":if(!/^\d{0,4}$/.test(H))return;break;case"cvc":if(!/^\d{0,3}$/.test(H))return;break}c(z=>({...z,[_]:W}))},ie=x=>{y(x),q({})},je=x=>{x&&(x.preventDefault(),x.stopPropagation()),s()},d=()=>{const x={};return j==="card"&&(n.card_number.trim()?/^\d{16}$/.test(n.card_number.replace(/\s/g,""))||(x.card_number="Invalid card number"):x.card_number="Card number is required",n.exp_month.trim()?/^(0[1-9]|1[0-2])$/.test(n.exp_month)||(x.exp_month="Invalid month (01-12)"):x.exp_month="Expiration month is required",n.exp_year.trim()?/^\d{4}$/.test(n.exp_year)||(x.exp_year="Invalid year format (YYYY)"):x.exp_year="Expiration year is required",n.cvc.trim()?/^\d{3,4}$/.test(n.cvc)||(x.cvc="Invalid CVC"):x.cvc="CVC is required",n.name.trim()||(x.name="Cardholder name is required")),x},O=async x=>{x.preventDefault(),x.stopPropagation();const _=d();if(q(_),Object.keys(_).length===0){M(!0);try{const H=new L,Y={type:{value:j},is_default:{value:n.is_default}};j==="card"&&(Y.card_number={value:n.card_number.replace(/\s/g,"")},Y.exp_month={value:n.exp_month},Y.exp_year={value:n.exp_year},Y.cvc={value:n.cvc},Y.name={value:n.name});const K=await H.AddPaymentMethod(Y);if(K.error)f(m,K.message||"Failed to add payment method",5e3,"error");else{f(m,"Payment method added successfully",5e3,"success");const W={community_id:{value:r.id.value},first_name:{value:t.firstName},last_name:{value:t.lastName},industry:{value:t.industry},company_size:{value:t.employeeCount},meeting_commitment:{value:t.meetingCommitment},additional_notes:{value:t.additionalNotes},goals:{value:{networking:t.goals.buildPartnerships,businessDevelopment:t.goals.gainAccess,learningAndDevelopment:t.goals.getResources}},skip_payment:!1},z=await H.JoinCommunity(W);z.error?(f(m,z.message||"Failed to join community",5e3,"error"),s(),C("/member/communities"),u(!1)):(f(m,"Successfully joined community!",5e3,"success"),s(),C("/member/communities"),u(!1))}}catch(H){f(m,H.message||"Failed to process request",5e3,"error")}finally{M(!1)}}};return e.jsx("div",{className:"flex fixed inset-0 z-50 justify-center items-center bg-black/50",onClick:je,children:e.jsxs("div",{className:"w-full max-w-md rounded-lg bg-[#161616] p-6",onClick:x=>x.stopPropagation(),children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h2",{className:"text-lg font-semibold text-[#eaeaea]",children:"Add Payment Method"}),e.jsx("button",{onClick:je,className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:"×"})]}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"mb-4 flex rounded bg-[#242424]",children:[e.jsx("button",{type:"button",className:`flex-1 rounded-l py-2 text-sm ${j==="card"?"bg-[#2e7d32] text-[#eaeaea]":"text-[#b5b5b5]"}`,onClick:x=>{x.stopPropagation(),ie("card")},children:"Credit Card"}),e.jsx("button",{type:"button",className:`flex-1 rounded-r py-2 text-sm ${j==="paypal"?"bg-[#2e7d32] text-[#eaeaea]":"text-[#b5b5b5]"}`,onClick:x=>{x.stopPropagation(),ie("paypal")},children:"PayPal"})]}),e.jsxs("form",{onSubmit:O,children:[j==="card"?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-3",children:[e.jsx("label",{className:"mb-1 block text-sm text-[#b5b5b5]",children:"Card Number"}),e.jsx("input",{type:"text",name:"card_number",value:n.card_number,onChange:U,placeholder:"1234 5678 9012 3456",maxLength:19,className:"w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"}),w.card_number&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:w.card_number})]}),e.jsxs("div",{className:"flex gap-3 mb-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"mb-1 block text-sm text-[#b5b5b5]",children:"Exp. Month"}),e.jsx("input",{type:"text",name:"exp_month",value:n.exp_month,onChange:U,onBlur:x=>{const _=x.target.value;_.length===1&&/^[1-9]$/.test(_)&&c(H=>({...H,exp_month:`0${_}`}))},placeholder:"MM",maxLength:2,className:"w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"}),w.exp_month&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:w.exp_month})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"mb-1 block text-sm text-[#b5b5b5]",children:"Exp. Year"}),e.jsx("input",{type:"text",name:"exp_year",value:n.exp_year,onChange:U,placeholder:"YYYY",maxLength:4,className:"w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"}),w.exp_year&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:w.exp_year})]}),e.jsxs("div",{className:"w-20",children:[e.jsx("label",{className:"mb-1 block text-sm text-[#b5b5b5]",children:"CVC"}),e.jsx("input",{type:"text",name:"cvc",value:n.cvc,onChange:U,placeholder:"123",maxLength:3,className:"w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"}),w.cvc&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:w.cvc})]})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx("label",{className:"mb-1 block text-sm text-[#b5b5b5]",children:"Cardholder Name"}),e.jsx("input",{type:"text",name:"name",value:n.name,onChange:U,placeholder:"John Doe",className:"w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"}),w.name&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:w.name})]})]}):e.jsx("div",{className:"rounded bg-[#242424] p-4 text-center text-sm text-[#b5b5b5]",children:"You will be redirected to PayPal to complete setup"}),e.jsxs("div",{className:"flex items-center mt-4",children:[e.jsx("input",{type:"checkbox",id:"is_default",name:"is_default",checked:n.is_default,onChange:U,className:"h-4 w-4 rounded bg-[#242424]"}),e.jsx("label",{htmlFor:"is_default",className:"ml-2 text-sm text-[#b5b5b5]",children:"Set as default payment method"})]}),e.jsx("button",{type:"submit",disabled:Z,className:"mt-6 w-full rounded bg-[#2e7d32] py-2 text-sm text-[#eaeaea] disabled:opacity-50",children:Z?"Processing...":"Add Payment Method"})]})]})]})})},as=s=>{C(`/member/communities/edit/${s}`)},fe=async s=>{try{le(!0);const r=await new L().callRawAPI(`/v1/api/dealmaker/user/community/${s}/users`,{},"GET");r.error||J(r.list||[])}catch(t){console.error("Failed to load members:",t),le(!1),f(m,t.message,5e3,"error")}finally{le(!1)}},ls=async s=>{try{const t=new L,r={user_id:{value:s},remove:{value:!0},role:{value:"member"}},n=await t.callRawAPI(`/v1/api/dealmaker/user/community/${k.id.value}/update`,r,"POST");if(n.error){f(m,n.message||"Failed to remove member",5e3,"error");return}fe(k.id.value)}catch(t){console.error("Failed to remove member:",t),f(m,t.message||"Failed to remove member",5e3,"error")}},rs=async(s,t)=>{try{const r=new L,n={user_id:{value:s},remove:{value:!1},role:{value:t}},c=await r.callRawAPI(`/v1/api/dealmaker/user/community/${k.id.value}/update`,n,"POST");if(c.error){f(m,c.message||"Failed to update role",5e3,"error");return}fe(k.id.value)}catch(r){console.error("Failed to update role:",r),f(m,r.message||"Failed to update role",5e3,"error")}},Te=({isOpen:s,onClose:t})=>{const[r,n]=a.useState(!1);return s?e.jsxs("div",{className:"flex fixed inset-0 z-50 justify-center items-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t}),e.jsxs("div",{className:"relative z-50 w-full max-w-4xl rounded-lg bg-[#161616] p-6 shadow-xl",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea] mr-4",children:"Community Members"}),e.jsx("button",{onClick:()=>n(!r),className:"rounded-lg bg-[#7dd87d] px-4 py-1.5 text-sm text-[#1e1e1e] hover:bg-[#7dd87d]/90",children:"Add Members"})]}),e.jsx("button",{onClick:t,className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:e.jsx("svg",{className:"w-5 h-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),r&&e.jsx(ps,{communityId:k.id.value,onClose:()=>n(!1),onSuccess:()=>fe(k.id.value)}),ne?e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"h-8 w-8 animate-spin rounded-full border-2 border-[#7dd87d] border-t-transparent"})}):e.jsx("div",{className:"max-h-[60vh] overflow-y-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"border-b border-[#363636] text-left",children:e.jsxs("tr",{children:[e.jsx("th",{className:"pb-3 text-sm font-medium text-[#b5b5b5]",children:"Member"}),e.jsx("th",{className:"pb-3 text-sm font-medium text-[#b5b5b5]",children:"Role"}),e.jsx("th",{className:"pb-3 text-sm font-medium text-[#b5b5b5]",children:"Industry"}),e.jsx("th",{className:"pb-3 text-sm font-medium text-[#b5b5b5]",children:"Joined"}),e.jsx("th",{className:"pb-3 text-sm font-medium text-[#b5b5b5]",children:"Actions"})]})}),e.jsx("tbody",{children:G.map(c=>{var j;return e.jsxs("tr",{className:"border-b border-[#363636]",children:[e.jsx("td",{className:"py-4",children:e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx(Se,{user:c}),e.jsx("span",{className:"text-[#eaeaea]",children:c.name.value})]})}),e.jsx("td",{className:"py-4",children:e.jsxs("select",{value:c.role.value,onChange:y=>rs(c.id.value,y.target.value),className:"rounded bg-[#242424] px-2 py-1 text-sm text-[#eaeaea]",children:[e.jsx("option",{value:"member",children:"Member"}),e.jsx("option",{value:"admin",children:"Admin"})]})}),e.jsx("td",{className:"py-4 text-[#eaeaea]",children:((j=c.industry)==null?void 0:j.value)||"N/A"}),e.jsx("td",{className:"py-4 text-[#eaeaea]",children:de(c.joined_at.value)}),e.jsx("td",{className:"py-4",children:e.jsx("button",{onClick:()=>ls(c.id.value),className:"text-red-500 hover:text-red-400",children:"Remove"})})]},`${c.id.value}-${c.joined_at.value}`)})})]})})]})]}):null},ns=()=>{var t;const s=(t=k==null?void 0:k.id)==null?void 0:t.value;C("/member/referrals/add",{state:{referralType:"community referral",communityId:s}})},is=s=>{Be(s),Me(!0)};return e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-4",children:[re&&e.jsx(us,{message:re,type:"error"}),we&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-full items-center justify-center p-4",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:()=>{ue(!1),Ce(null)},"aria-hidden":"true"}),e.jsx("div",{className:"relative z-50 w-full max-w-3xl transform overflow-hidden",children:e.jsx("div",{className:"relative bg-[#1e1e1e] rounded-lg shadow-xl",children:e.jsx(bs,{referralId:ke,onClose:()=>{ue(!1),Ce(null)}})})})]})}),qe(),Je(),h?He():T==="join"?Ze():Oe(),e.jsx(ss,{isOpen:ge,onClose:()=>u(!1),community:k}),e.jsx(Te,{isOpen:P,onClose:()=>I(!1)}),e.jsx(Xe,{isOpen:he,onClose:s=>{ce(!1),me(null),s&&Le()},referralId:be,communities:ve}),e.jsx(Ns,{isOpen:Fe,onClose:()=>Me(!1),post:De})]})};export{qs as default};
