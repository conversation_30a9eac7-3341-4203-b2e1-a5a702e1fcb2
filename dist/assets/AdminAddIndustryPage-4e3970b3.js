import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as r,d as x}from"./vendor-1c28ea83.js";import{u as h}from"./react-hook-form-eec8b32f.js";import{G as y,A as b,o as g,M as S,s as A,t as E}from"./index-b3edd152.js";import{c as w,a as N}from"./yup-1b5612ec.js";import{M as j}from"./MkdInput-67f7082d.js";import{I}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const rt=({setSidebar:i})=>{const{dispatch:o}=r.useContext(y),m=w({name:N().required()}).required(),{dispatch:n}=r.useContext(b),[a,s]=r.useState(!1),d=x(),{register:p,handleSubmit:l,setError:R,formState:{errors:u}}=h({resolver:g(m)}),c=async f=>{s(!0);try{let t=new S;t.setTable("industry"),(await t.callRestAPI({name:f.name},"POST")).error||(A(o,"Added"),d("/admin/industry"),i(!1),o({type:"REFRESH_DATA",payload:{refreshData:!0}})),s(!1)}catch(t){s(!1),console.log("Error",t),E(n,t.message)}};return r.useEffect(()=>{o({type:"SETPATH",payload:{path:"industry"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Industry"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:l(c),children:[e.jsx(j,{type:"text",page:"add",name:"name",errors:u,label:"Name",placeholder:"Name",register:p,className:""}),e.jsx(I,{type:"submit",loading:a,disabled:a,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{rt as default};
