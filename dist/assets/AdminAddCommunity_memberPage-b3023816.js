import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as o,d as b}from"./vendor-1c28ea83.js";import{u as h}from"./react-hook-form-eec8b32f.js";import{G as _,A as g,o as S,M as A,s as j,t as E}from"./index-b3edd152.js";import{c as w,a as d}from"./yup-1b5612ec.js";import{M as l}from"./MkdInput-67f7082d.js";import{I as C}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const oe=({setSidebar:p})=>{const{dispatch:r}=o.useContext(_),u=w({user_id:d().required(),community_id:d().required(),role:d().required()}).required(),{dispatch:c}=o.useContext(g),[n,m]=o.useState(!1),x=b(),{register:s,handleSubmit:f,setError:N,formState:{errors:i}}=h({resolver:S(u)}),y=async a=>{m(!0);try{let t=new A;t.setTable("community_member"),(await t.callRestAPI({user_id:a.user_id,community_id:a.community_id,role:a.role},"POST")).error||(j(r,"Added"),x("/admin/community_member"),p(!1),r({type:"REFRESH_DATA",payload:{refreshData:!0}})),m(!1)}catch(t){m(!1),console.log("Error",t),E(c,t.message)}};return o.useEffect(()=>{r({type:"SETPATH",payload:{path:"community_member"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Community_member"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:f(y),children:[e.jsx(l,{type:"text",page:"add",name:"user_id",errors:i,label:"User_id",placeholder:"User_id",register:s,className:""}),e.jsx(l,{type:"text",page:"add",name:"community_id",errors:i,label:"Community_id",placeholder:"Community_id",register:s,className:""}),e.jsx(l,{type:"text",page:"add",name:"role",errors:i,label:"Role",placeholder:"Role",register:s,className:""}),e.jsx(C,{type:"submit",loading:n,disabled:n,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{oe as default};
