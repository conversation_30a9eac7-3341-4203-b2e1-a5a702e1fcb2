import{j as t}from"./@react-google-maps/api-211df1ae.js";import{r as S,R as F,L}from"./vendor-1c28ea83.js";import{u as R}from"./react-hook-form-eec8b32f.js";import{G as B,o as C,M as $,s as q,t as D}from"./index-b3edd152.js";import{c as G,a as I}from"./yup-1b5612ec.js";import{I as M}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const le=()=>{var r,n;const[i,a]=S.useState(!1),y=G({email:I().email().required()}).required(),{register:j,handleSubmit:N,setError:m,formState:{errors:s}}=R({resolver:C(y)}),{dispatch:l}=F.useContext(B),k=async v=>{var d,c,p,u,x,g,f,b;let E=new $;try{a(!0);const e=await E.forgot(v.email,"user");if(!e.error)q(l,"Reset Code Sent");else if(e.validation){const h=Object.keys(e.validation);for(let o=0;o<h.length;o++){const w=h[o];m(w,{type:"manual",message:e.validation[w]})}}a(!1)}catch(e){a(!1),console.log("Error",e),m("email",{type:"manual",message:(c=(d=e==null?void 0:e.response)==null?void 0:d.data)!=null&&c.message?(u=(p=e==null?void 0:e.response)==null?void 0:p.data)==null?void 0:u.message:e==null?void 0:e.message}),D(l,(g=(x=e==null?void 0:e.response)==null?void 0:x.data)!=null&&g.message?(b=(f=e==null?void 0:e.response)==null?void 0:f.data)==null?void 0:b.message:e==null?void 0:e.message)}};return t.jsx(t.Fragment,{children:t.jsxs("div",{className:"mx-auto w-full max-w-xs",children:[t.jsxs("form",{onSubmit:N(k),className:"mb-4 mt-8 rounded bg-white px-8 pb-8 pt-6 shadow-md ",children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),t.jsx("input",{type:"email",placeholder:"Email",...j("email"),className:`"shadow focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${s&&((r=s.email)!=null&&r.message)?"border-red-500":""}`}),t.jsx("p",{className:"text-xs italic text-red-500",children:s&&((n=s.email)==null?void 0:n.message)})]}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx(M,{className:"focus:shadow-outline rounded bg-primaryBlue px-4 py-2 font-bold text-white focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:i,disabled:i,children:"Forgot Password"}),t.jsx(L,{className:"inline-block align-baseline text-sm font-bold text-primaryBlue",to:"/member/login",children:"Login?"})]})]}),t.jsxs("p",{className:"text-center text-xs text-gray-500",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})})};export{le as default};
