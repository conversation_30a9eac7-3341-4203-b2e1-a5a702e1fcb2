import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as t,d as S,r as x}from"./vendor-1c28ea83.js";import{M as g,A as w,G as b,L as o,H as v,I as A}from"./index-b3edd152.js";import{M as m}from"./index-0cdf3a8c.js";import{M as E}from"./index-db36e1ef.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";new g;const j=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Created_at",accessor:"created_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Updated_at",accessor:"updated_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"User_id",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Community_id",accessor:"community_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],X=()=>{t.useContext(w),t.useContext(b),S();const[c,s]=t.useState(!1),[r,i]=t.useState(!1),[p,u]=t.useState(),f=x.useRef(null),[_,h]=t.useState([]),d=(a,l,n=[])=>{switch(a){case"add":s(l);break;case"edit":i(l),h(n),u(n[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mt-[100px] overflow-x-auto rounded bg-white p-5 shadow",children:e.jsx(o,{children:e.jsx(E,{columns:j,tableRole:"admin",table:"community_invite",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:a=>d("edit",!0,a)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPosition:"ontable",refreshRef:f})})}),e.jsx(o,{children:e.jsx(m,{isModalActive:c,closeModalFn:()=>s(!1),children:e.jsx(v,{setSidebar:s})})}),r&&e.jsx(o,{children:e.jsx(m,{isModalActive:r,closeModalFn:()=>i(!1),children:e.jsx(A,{activeId:p,setSidebar:i})})})]})};export{X as default};
