import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as c,d as A,f as $,r as C}from"./vendor-1c28ea83.js";import{u as D}from"./react-hook-form-eec8b32f.js";import{M as q,A as F,G as R,t as f,o as T,s as g}from"./index-b3edd152.js";import{c as G,a as y,g as I}from"./yup-1b5612ec.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let j=new q;const ne=()=>{var m,u,p,x;const w=G({name:y().required(),description:y().nullable(),status:I().required()}).required(),{dispatch:d}=c.useContext(F),{dispatch:a}=c.useContext(R),N=A(),o=$(),[M,v]=C.useState(0),{register:i,handleSubmit:S,setError:k,setValue:n,formState:{errors:r}}=D({resolver:T(w)}),E=[{key:"0",value:"Inactive"},{key:"1",value:"Active"}],P=async e=>{try{console.log(e);const s=await j.updateStripeProduct(o==null?void 0:o.id,{name:e.name,description:e.description,status:e.status});if(!s.error)g(a,"Edited",4e3),N("/admin/product");else if(s.validation){const h=Object.keys(s.validation);for(let l=0;l<h.length;l++){const b=h[l];k(b,{type:"manual",message:s.validation[b]})}}}catch(s){console.log("Error",s),g(a,s.message,4e3),f(d,s.message)}};return c.useEffect(()=>{a({type:"SETPATH",payload:{path:"products"}}),async function(){try{const e=await j.getStripeProduct(o==null?void 0:o.id);if(!e.error){const s=e.model.object;n("name",s.name),n("description",s.description),n("status",e.model.status),v(e.model.id)}}catch(e){console.log("Error",e),f(d,e.message)}}()},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Product"}),t.jsxs("form",{className:"w-full max-w-lg",onSubmit:S(P),children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Name"}),t.jsx("input",{type:"text",placeholder:"Name",...i("name"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(m=r.name)!=null&&m.message?"border-red-500":""}`}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(u=r.name)==null?void 0:u.message})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"description",children:"Description"}),t.jsx("input",{type:"text",placeholder:"Description",...i("description"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(p=r.description)!=null&&p.message?"border-red-500":""}`}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(x=r.description)==null?void 0:x.message})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Status"}),t.jsx("select",{className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...i("status"),children:E.map(e=>t.jsx("option",{value:e.key,children:e.value},e.key))})]}),t.jsx("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ne as default};
