import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as r,d as h}from"./vendor-1c28ea83.js";import{u as _}from"./react-hook-form-eec8b32f.js";import{G as b,A as S,o as y,M as A,s as E,t as j}from"./index-b3edd152.js";import{c as w,a as n}from"./yup-1b5612ec.js";import{M as p}from"./MkdInput-67f7082d.js";import{I as M}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const re=({setSidebar:l})=>{const{dispatch:o}=r.useContext(b),u=w({meeting_id:n().required(),user_id:n().required()}).required(),{dispatch:c}=r.useContext(S),[a,s]=r.useState(!1),g=h(),{register:i,handleSubmit:f,setError:N,formState:{errors:m}}=_({resolver:y(u)}),x=async d=>{s(!0);try{let t=new A;t.setTable("meeting_attendee"),(await t.callRestAPI({meeting_id:d.meeting_id,user_id:d.user_id},"POST")).error||(E(o,"Added"),g("/admin/meeting_attendee"),l(!1),o({type:"REFRESH_DATA",payload:{refreshData:!0}})),s(!1)}catch(t){s(!1),console.log("Error",t),j(c,t.message)}};return r.useEffect(()=>{o({type:"SETPATH",payload:{path:"meeting_attendee"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Meeting_attendee"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:f(x),children:[e.jsx(p,{type:"text",page:"add",name:"meeting_id",errors:m,label:"Meeting_id",placeholder:"Meeting_id",register:i,className:""}),e.jsx(p,{type:"text",page:"add",name:"user_id",errors:m,label:"User_id",placeholder:"User_id",register:i,className:""}),e.jsx(M,{type:"submit",loading:a,disabled:a,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{re as default};
