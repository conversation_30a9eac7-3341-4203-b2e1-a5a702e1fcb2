import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as i,d as _}from"./vendor-1c28ea83.js";import{u as f}from"./react-hook-form-eec8b32f.js";import{G as g,A as j,o as S,M as y,s as N,t as A}from"./index-b3edd152.js";import{c as E,a as s}from"./yup-1b5612ec.js";import{M as o}from"./MkdInput-67f7082d.js";import{I as w}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const at=({setSidebar:c})=>{const{dispatch:d}=i.useContext(g),u=E({create_at:s(),update_at:s(),name:s(),product_id:s(),stripe_id:s(),object:s(),status:s()}).required(),{dispatch:n}=i.useContext(j),[l,m]=i.useState(!1),x=_(),{register:e,handleSubmit:b,setError:C,formState:{errors:a}}=f({resolver:S(u)}),h=async r=>{m(!0);try{let p=new y;p.setTable("stripe_product"),(await p.callRestAPI({create_at:r.create_at,update_at:r.update_at,name:r.name,product_id:r.product_id,stripe_id:r.stripe_id,object:r.object,status:r.status},"POST")).error||(N(d,"Added"),x("/admin/stripe_product"),c(!1),d({type:"REFRESH_DATA",payload:{refreshData:!0}})),m(!1)}catch(p){m(!1),console.log("Error",p),A(n,p.message)}};return i.useEffect(()=>{d({type:"SETPATH",payload:{path:"stripe_product"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Stripe_product"}),t.jsxs("form",{className:"w-full max-w-lg",onSubmit:b(h),children:[t.jsx(o,{type:"text",page:"add",name:"create_at",errors:a,label:"Create_at",placeholder:"Create_at",register:e,className:""}),t.jsx(o,{type:"text",page:"add",name:"update_at",errors:a,label:"Update_at",placeholder:"Update_at",register:e,className:""}),t.jsx(o,{type:"text",page:"add",name:"name",errors:a,label:"Name",placeholder:"Name",register:e,className:""}),t.jsx(o,{type:"text",page:"add",name:"product_id",errors:a,label:"Product_id",placeholder:"Product_id",register:e,className:""}),t.jsx(o,{type:"text",page:"add",name:"stripe_id",errors:a,label:"Stripe_id",placeholder:"Stripe_id",register:e,className:""}),t.jsx(o,{type:"text",page:"add",name:"object",errors:a,label:"Object",placeholder:"Object",register:e,className:""}),t.jsx(o,{type:"text",page:"add",name:"status",errors:a,label:"Status",placeholder:"Status",register:e,className:""}),t.jsx(w,{type:"submit",loading:l,disabled:l,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{at as default};
