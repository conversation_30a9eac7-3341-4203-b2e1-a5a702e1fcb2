import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as p,d as y}from"./vendor-1c28ea83.js";import{u as b}from"./react-hook-form-eec8b32f.js";import{G as h,A as j,o as f,M as N,s as S,t as A}from"./index-b3edd152.js";import{c as T,a as r}from"./yup-1b5612ec.js";import{M as o}from"./MkdInput-67f7082d.js";import{I as E}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const ae=({setSidebar:c})=>{const{dispatch:l}=p.useContext(h),u=T({create_at:r(),update_at:r(),name:r(),product_id:r(),stripe_id:r(),is_usage_metered:r(),usage_limit:r(),object:r(),amount:r(),trial_days:r(),type:r(),status:r()}).required(),{dispatch:n}=p.useContext(j),[d,m]=p.useState(!1),_=y(),{register:t,handleSubmit:x,setError:w,formState:{errors:a}}=b({resolver:f(u)}),g=async s=>{m(!0);try{let i=new N;i.setTable("stripe_price"),(await i.callRestAPI({create_at:s.create_at,update_at:s.update_at,name:s.name,product_id:s.product_id,stripe_id:s.stripe_id,is_usage_metered:s.is_usage_metered,usage_limit:s.usage_limit,object:s.object,amount:s.amount,trial_days:s.trial_days,type:s.type,status:s.status},"POST")).error||(S(l,"Added"),_("/admin/stripe_price"),c(!1),l({type:"REFRESH_DATA",payload:{refreshData:!0}})),m(!1)}catch(i){m(!1),console.log("Error",i),A(n,i.message)}};return p.useEffect(()=>{l({type:"SETPATH",payload:{path:"stripe_price"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Stripe_price"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:x(g),children:[e.jsx(o,{type:"text",page:"add",name:"create_at",errors:a,label:"Create_at",placeholder:"Create_at",register:t,className:""}),e.jsx(o,{type:"text",page:"add",name:"update_at",errors:a,label:"Update_at",placeholder:"Update_at",register:t,className:""}),e.jsx(o,{type:"text",page:"add",name:"name",errors:a,label:"Name",placeholder:"Name",register:t,className:""}),e.jsx(o,{type:"text",page:"add",name:"product_id",errors:a,label:"Product_id",placeholder:"Product_id",register:t,className:""}),e.jsx(o,{type:"text",page:"add",name:"stripe_id",errors:a,label:"Stripe_id",placeholder:"Stripe_id",register:t,className:""}),e.jsx(o,{type:"text",page:"add",name:"is_usage_metered",errors:a,label:"Is_usage_metered",placeholder:"Is_usage_metered",register:t,className:""}),e.jsx(o,{type:"text",page:"add",name:"usage_limit",errors:a,label:"Usage_limit",placeholder:"Usage_limit",register:t,className:""}),e.jsx(o,{type:"text",page:"add",name:"object",errors:a,label:"Object",placeholder:"Object",register:t,className:""}),e.jsx(o,{type:"text",page:"add",name:"amount",errors:a,label:"Amount",placeholder:"Amount",register:t,className:""}),e.jsx(o,{type:"text",page:"add",name:"trial_days",errors:a,label:"Trial_days",placeholder:"Trial_days",register:t,className:""}),e.jsx(o,{type:"text",page:"add",name:"type",errors:a,label:"Type",placeholder:"Type",register:t,className:""}),e.jsx(o,{type:"text",page:"add",name:"status",errors:a,label:"Status",placeholder:"Status",register:t,className:""}),e.jsx(E,{type:"submit",loading:d,disabled:d,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ae as default};
