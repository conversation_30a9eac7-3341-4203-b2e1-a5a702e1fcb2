import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as m,d as h}from"./vendor-1c28ea83.js";import{u as f}from"./react-hook-form-eec8b32f.js";import{G as y,A as S,o as _,M as j,s as N,t as k}from"./index-b3edd152.js";import{c as A,a as i}from"./yup-1b5612ec.js";import{M as r}from"./MkdInput-67f7082d.js";import{I as M}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const ae=({setSidebar:p})=>{const{dispatch:n}=m.useContext(y),c=A({status:i().required(),topic:i().required(),description:i().required(),meeting_datetime:i().required(),created_by:i().required(),meeting_link:i().required()}).required(),{dispatch:u}=m.useContext(S),[d,l]=m.useState(!1),g=h(),{register:t,handleSubmit:x,setError:E,formState:{errors:a}}=f({resolver:_(c)}),b=async s=>{l(!0);try{let o=new j;o.setTable("meetings"),(await o.callRestAPI({status:s.status,topic:s.topic,description:s.description,meeting_datetime:s.meeting_datetime,created_by:s.created_by,meeting_link:s.meeting_link},"POST")).error||(N(n,"Added"),g("/admin/meetings"),p(!1),n({type:"REFRESH_DATA",payload:{refreshData:!0}})),l(!1)}catch(o){l(!1),console.log("Error",o),k(u,o.message)}};return m.useEffect(()=>{n({type:"SETPATH",payload:{path:"meetings"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Meetings"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:x(b),children:[e.jsx(r,{type:"text",page:"add",name:"status",errors:a,label:"Status",placeholder:"Status",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"topic",errors:a,label:"Topic",placeholder:"Topic",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"description",errors:a,label:"Description",placeholder:"Description",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"meeting_datetime",errors:a,label:"Meeting_datetime",placeholder:"Meeting_datetime",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"created_by",errors:a,label:"Created_by",placeholder:"Created_by",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"meeting_link",errors:a,label:"Meeting_link",placeholder:"Meeting_link",register:t,className:""}),e.jsx(M,{type:"submit",loading:d,disabled:d,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ae as default};
