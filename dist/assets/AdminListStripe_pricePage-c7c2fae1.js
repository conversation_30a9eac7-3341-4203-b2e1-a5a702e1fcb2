import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as s,d as h,r as g}from"./vendor-1c28ea83.js";import{M as x,A as E,G as _,L as r,q as w,r as b}from"./index-b3edd152.js";import{M as c}from"./index-0cdf3a8c.js";import{M as j}from"./index-db36e1ef.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";new x;const A=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create_at",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update_at",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Name",accessor:"name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Product_id",accessor:"product_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Stripe_id",accessor:"stripe_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Is_usage_metered",accessor:"is_usage_metered",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Usage_limit",accessor:"usage_limit",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Object",accessor:"object",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Amount",accessor:"amount",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Trial_days",accessor:"trial_days",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],X=()=>{s.useContext(E),s.useContext(_),h();const[m,a]=s.useState(!1),[o,t]=s.useState(!1),[n,f]=s.useState(),S=g.useRef(null),[D,u]=s.useState([]),d=(i,l,p=[])=>{switch(i){case"add":a(l);break;case"edit":t(l),u(p),f(p[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mt-[100px] overflow-x-auto rounded bg-white p-5 shadow",children:e.jsx(r,{children:e.jsx(j,{columns:A,tableRole:"admin",table:"stripe_price",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>d("edit",!0,i)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPosition:"ontable",refreshRef:S})})}),e.jsx(r,{children:e.jsx(c,{isModalActive:m,closeModalFn:()=>a(!1),children:e.jsx(w,{setSidebar:a})})}),o&&e.jsx(r,{children:e.jsx(c,{isModalActive:o,closeModalFn:()=>t(!1),children:e.jsx(b,{activeId:n,setSidebar:t})})})]})};export{X as default};
