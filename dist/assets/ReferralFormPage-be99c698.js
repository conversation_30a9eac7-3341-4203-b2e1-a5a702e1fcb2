import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{u as s}from"./react-hook-form-eec8b32f.js";import"./index-b3edd152.js";import"./pdf-lib-623decea.js";import"./react-toggle-58b0879a.js";/* empty css                 */import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./@uppy/dashboard-3a4b1704.js";const B=()=>{const{register:o,handleSubmit:r,formState:{errors:a}}=s(),t=i=>{console.log(i)};return e.jsx("div",{className:"space-y-6 p-4 md:p-6",children:e.jsxs("div",{className:"rounded-xl bg-[#161616] p-6",children:[e.jsx("h2",{className:"mb-6 text-2xl font-bold text-[#eaeaea]",children:"Submit a Referral"}),e.jsxs("form",{onSubmit:r(t),className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-[#eaeaea]",children:"Type of Opportunity"}),e.jsx("div",{className:"relative",children:e.jsxs("select",{className:"h-[50px] w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]",...o("opportunityType"),children:[e.jsx("option",{value:"",children:"Select type"}),e.jsx("option",{value:"looking_for_service",children:"Looking for Service"}),e.jsx("option",{value:"looking_for_product",children:"Looking for Product"}),e.jsx("option",{value:"looking_for_buyer",children:"Looking for Buyer"}),e.jsx("option",{value:"looking_for_investor",children:"Looking for Investor"}),e.jsx("option",{value:"looking_for_partner",children:"Looking for Partner"})]})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-[#eaeaea]",children:"Description"}),e.jsx("textarea",{className:"h-[122px] w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-4 text-[#eaeaea]",placeholder:"Write your text here...",...o("description")})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{type:"button",className:"rounded-lg border border-[#363636] px-6 py-3 text-[#eaeaea] hover:bg-[#1e1e1e]",children:"Cancel"}),e.jsx("button",{type:"submit",className:"rounded-lg bg-[#2e7d32] px-6 py-3 font-medium text-white hover:bg-[#1b5e20]",children:"Submit Referral"})]})]})]})})};export{B as default};
