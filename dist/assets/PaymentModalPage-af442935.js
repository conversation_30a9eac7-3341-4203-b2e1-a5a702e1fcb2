import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{L as a}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const k=({isOpen:t,onClose:s})=>t?e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-[#121212cc]",children:e.jsxs("div",{className:"w-full max-w-2xl rounded-xl bg-[#252525fd] p-12",children:[e.jsxs("div",{className:"mb-8 text-center",children:[e.jsx(a,{children:e.jsx("img",{className:"mx-auto mb-4 h-9 w-10",alt:"Premium",src:"premium-icon.svg"})}),e.jsxs("h2",{className:"mb-2 text-3xl font-bold text-[#eaeaea]",children:["To Join a Community, Please Pay Your",e.jsx("br",{}),"Community Fee"]}),e.jsx("div",{className:"mx-auto h-1 w-24 rounded-full bg-[#7dd87d]"})]}),e.jsxs("div",{className:"mb-8 rounded-xl bg-[#1e1e1e] p-8",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsx("h3",{className:"text-xl font-bold text-[#7dd87d]",children:"Premium Plan"}),e.jsx("span",{className:"text-3xl font-bold text-[#a3eca3]",children:"$50.00"})]}),e.jsx("ul",{className:"mb-8 space-y-4",children:e.jsxs("li",{className:"flex items-center gap-2 text-[#eaeaea]",children:[e.jsx("img",{className:"h-4 w-4",alt:"Check",src:"check-icon.svg"}),"Post Opportunities"]})})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx("button",{className:"flex-1 rounded-lg bg-[#2e7d32] py-4 font-bold text-[#eaeaea]",onClick:()=>{},children:"Upgrade Now"}),e.jsx("button",{className:"flex-1 rounded-lg border border-[#363636] py-4 text-[#eaeaea]",onClick:s,children:"Maybe Later"})]}),e.jsx("button",{className:"mt-4 w-full text-center text-sm text-[#7dd87d]",onClick:()=>{},children:"View all plan features"})]})}):null;export{k as default};
