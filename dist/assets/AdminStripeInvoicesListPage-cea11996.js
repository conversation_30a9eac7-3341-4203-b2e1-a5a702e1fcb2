import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as o}from"./vendor-1c28ea83.js";import{M as T,A as _,G as z,o as F,t as G}from"./index-b3edd152.js";import{u as L}from"./react-hook-form-eec8b32f.js";import{c as M}from"./yup-1b5612ec.js";import"./pdf-lib-623decea.js";import{P as $}from"./index-7ba88dde.js";import"./index-e2604cb4.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const l=[{header:"Row",accessor:"row"},{header:"Status",accessor:"status"},{header:"Currency",accessor:"currency"},{header:"Amount due",accessor:"amount_due",type:"currency"},{header:"Amount paid",accessor:"amount_paid",type:"currency"},{header:"Amount remaining",accessor:"amount_remaining",type:"currency"},{header:"Created at",accessor:"created",type:"timestamp"}],xe=()=>{var p;const h=new T,{dispatch:g,state:B}=o.useContext(_),{dispatch:c}=o.useContext(z),[d,x]=o.useState(null),[a,f]=o.useState({}),[m,u]=o.useState(10),[y,H]=o.useState(0),[w,K]=o.useState(0),[b,j]=o.useState(!1),[v,S]=o.useState(!1),N=M({}),{register:O,handleSubmit:P,formState:{errors:U}}=L({resolver:F(N)});function C(s){(async function(){u(s),await i({limit:s})})()}function R(){(async function(){await i({limit:m,before:a==null?void 0:a.data[0].id})})()}function k(){(async function(){await i({limit:m,after:a==null?void 0:a.data[(a==null?void 0:a.data.length)-1].id})})()}async function i(s){var n,r;try{const{list:t,limit:D,error:E,message:I}=await h.getStripeInvoices(s);if(console.log(t),E&&showToast(c,I,5e3),!t)return;d||x(((n=t==null?void 0:t.data[0])==null?void 0:n.id)??""),f(t),u(+D),j(d&&d!==((r=t.data[0])==null?void 0:r.id)),S(t.has_more)}catch(t){console.error("ERROR",t),showToast(c,t.message,5e3),G(g,t.message)}}const A=s=>{i({})};return o.useEffect(()=>{c({type:"SETPATH",payload:{path:"invoices"}}),async function(){i({})}()},[]),e.jsxs(e.Fragment,{children:[e.jsxs("form",{className:"mb-10 rounded bg-white p-5 shadow",onSubmit:P(A),children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Search"}),e.jsx("div",{className:"filter-form-holder mt-10 flex flex-wrap"}),e.jsxs("div",{className:"search-buttons pl-2",children:[e.jsx("button",{type:"submit",className:"mr-2 inline-block rounded bg-blue-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:"Search"}),e.jsx("button",{type:"reset",onClick:()=>i(1),className:"inline-block rounded bg-gray-800 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-gray-900 hover:shadow-lg focus:bg-gray-900 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-gray-900 active:shadow-lg",children:"Reset"})]})]}),e.jsxs("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:[e.jsx("div",{className:"mb-3 flex w-full justify-between text-center  ",children:e.jsx("h4",{className:"text-2xl font-medium",children:"Invoices "})}),e.jsx("div",{className:"overflow-x-auto border-b border-gray-200 shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:l.map((s,n)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[s.header,e.jsx("span",{children:s.isSorted?s.isSortedDesc?" ▼":" ▲":""})]},n))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:(p=a==null?void 0:a.data)==null?void 0:p.map((s,n)=>e.jsx("tr",{children:l.map((r,t)=>r.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4"},t):r.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:r.mapping[s[r.accessor]]},t):r.type==="timestamp"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(s[r.accessor]*1e3).toLocaleString("en-US")},t):r.type==="currency"?e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:["$",Number(s[r.accessor]/100).toFixed(2)]},t):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s[r.accessor]},t))},n))})]})})]}),e.jsx($,{currentPage:w,pageCount:y,pageSize:m,canPreviousPage:b,canNextPage:v,updatePageSize:C,previousPage:R,nextPage:k})]})};export{xe as default};
