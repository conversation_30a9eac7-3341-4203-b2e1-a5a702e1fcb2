import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as o,d as E,f as S}from"./vendor-1c28ea83.js";import{u as A}from"./react-hook-form-eec8b32f.js";import{M as I,A as q,G as w,t as _,S as N,o as T,s as R}from"./index-b3edd152.js";import{c as C,a as x}from"./yup-1b5612ec.js";import{M as h}from"./MkdInput-67f7082d.js";import{I as P}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let i=new I;const de=s=>{const{dispatch:l}=o.useContext(q),y=C({user_id:x().required(),approver_id:x().required()}).required(),{dispatch:a}=o.useContext(w),[u,m]=o.useState(!1),[g,d]=o.useState(!1),b=E(),{register:c,handleSubmit:v,setError:k,setValue:p,formState:{errors:f}}=A({resolver:T(y)}),r=S();o.useEffect(function(){(async function(){try{d(!0),i.setTable("community_join_request");const e=await i.callRestAPI({id:s.activeId?s.activeId:Number(r==null?void 0:r.id)},"GET");e.error||(p("user_id",e.model.user_id),p("approver_id",e.model.approver_id),d(!1))}catch(e){d(!1),console.log("error",e),_(l,e.message)}})()},[]);const j=async e=>{m(!0);try{i.setTable("community_join_request"),(await i.callRestAPI({id:s.activeId?s.activeId:Number(r==null?void 0:r.id),user_id:e.user_id,approver_id:e.approver_id},"PUT")).error||(R(a,"Updated"),b("/admin/community_join_request"),s.setSidebar(!1),a({type:"REFRESH_DATA",payload:{refreshData:!0}})),m(!1)}catch(n){m(!1),console.log("Error",n),_(l,n.message)}};return o.useEffect(()=>{a({type:"SETPATH",payload:{path:"community_join_request"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Community_join_request"}),g?t.jsx(N,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:v(j),children:[t.jsx(h,{type:"text",page:"edit",name:"user_id",errors:f,label:"User_id",placeholder:"User_id",register:c,className:""}),t.jsx(h,{type:"text",page:"edit",name:"approver_id",errors:f,label:"Approver_id",placeholder:"Approver_id",register:c,className:""}),t.jsx(P,{type:"submit",loading:u,disabled:u,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{de as default};
