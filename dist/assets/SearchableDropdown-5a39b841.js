import{j as t}from"./@react-google-maps/api-211df1ae.js";import{r as a,R as ee}from"./vendor-1c28ea83.js";import{G as be,A as je,S as ye,aF as re,a as te,aG as we}from"./index-b3edd152.js";import"./react-toggle-58b0879a.js";/* empty css                 */import"./react-quill-4ec0fa7c.js";import{v as W}from"./uuid-11097a38.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./@craftjs/core-a5d68af1.js";import"./@uppy/dashboard-3a4b1704.js";const ke=F=>"max-h-[18.75rem]",Ne=({onSelect:F,showBorder:Ae=!0,display:b="display",value:d,uniqueKey:g,selector:Se=null,disabled:$=!1,placeholder:se="- search -",label:J="Select",maxHeight:Ve="18.75rem",height:$e="fit-content",selectedOptions:Z=[],table:G="",errors:H,name:Q,className:U="w-[23rem]",join:C=[],filter:X=[],mode:Ce="static",useExternalData:L=!1,externalDataLoading:le=!1,externalDataOptions:x=[],onReady:Y=null,refreshRef:ne=null,clearRef:ce=null,showSearchIcon:ae=!1,required:fe=!1,dataRetrievalState:T=null,displaySeparator:j="",customOptions:v=[],onPopoverStateChange:N=null,popoverShown:D})=>{var K;const y=a.useRef(null),A=a.useRef(null),S=a.useRef(null),{dispatch:ie,state:ue}=ee.useContext(be),{dispatch:me}=ee.useContext(je),I=ue[T??G],[f,M]=a.useState([]),de=W({namespace:"SearchableDropdown"});W({namespace:"SearchableDropdown"}),W({namespace:"SearchableDropdown"});const[w,E]=a.useState(!1),[o,P]=a.useState(""),[B,O]=a.useState(null),[h,V]=a.useState([]),z=a.useMemo(()=>X,[X]),he=a.useMemo(()=>JSON.stringify(x),[x]),q=async()=>{var r;const e=await we(ie,me,G,{...z!=null&&z.length?{filter:z}:null,...C&&(C!=null&&C.length)?{join:C}:null},T&&T);if(!e.error){if(G==="user"){const m=(r=e==null?void 0:e.data)==null?void 0:r.map(u=>u!=null&&u.first_name||u!=null&&u.last_name?{...u,full_name:te(`${u.first_name} ${u.last_name}`,{casetype:"capitalize",separator:" "})}:u);M(()=>[...m]),V(()=>[...m])}else M(()=>[...e==null?void 0:e.data]),V(()=>[...e==null?void 0:e.data]);if(d){const m=e==null?void 0:e.data.find(u=>u[g]==d);m&&O(()=>m)}Y&&Y(e==null?void 0:e.data)}},k=(e,r,m)=>{if(typeof r=="string")return String(e[r]);if(typeof r=="object")if(Array.isArray(r)){if(r.some(l=>typeof l!="string"))return String(e[Object.keys(e)[0]]);const R=r.map(l=>e[l]);return String(R.length?R.join(` ${j} `):R.join(" "))}else{if(![1,2].includes(Object.keys(r).length)||Object.keys(r).some(s=>!["and","or"].includes(s)))return String(e[Object.keys(e)[0]]);const l=r.and,i=r.or;if(l&&i){if(typeof l=="string"&&typeof i=="string"){const s=e[l],n=e[i];return String(s||n||e[Object.keys(e)[0]])}if(Array.isArray(l)&&Array.isArray(i)){if(l.some(c=>!e[c])){const c=i.map(p=>{if(e[p])return e[p]}).filter(Boolean);return String(c.length?c.length?c.join(` ${j} `):c.join(" "):e[Object.keys(e)[0]])}const n=l.map(c=>{if(e[c])return e[c]}).filter(Boolean);return String(n.length?n.join(` ${j} `):n.join(" "))}if(Array.isArray(l)&&typeof i=="string"){if(l.some(c=>!e[c])){const c=e[i];return String(c||e[Object.keys(e)[0]])}const n=l.map(c=>{if(e[c])return e[c]}).filter(Boolean);return String(n.length?n.join(` ${j} `):n.join(" "))}if(Array.isArray(i)&&typeof l=="string"){const s=e[l];if(s)return String(s);const n=i.map(c=>{if(e[c])return e[c]}).filter(Boolean);return String(n.length?n.length?n.join(` ${j} `):n.join(" "):e[Object.keys(e)[0]])}}else if(l&&!i){if(typeof l=="string"){const s=e[l];return String(s||e[Object.keys(e)[0]])}if(Array.isArray(l)){const s=l.map(n=>{if(e[n])return e[n]}).filter(Boolean);return String(s.length?s.length?s.join(` ${j} `):s.join(" "):e[Object.keys(e)[0]])}}else if(!l&&i){if(typeof i=="string"){const s=e[i];return String(s||e[Object.keys(e)[0]])}if(Array.isArray(i)){const s=i.map(n=>{if(e[n])return e[n]}).filter(Boolean);return String(s.length?s.length?s.join(` ${j} `):s.join(" "):e[Object.keys(e)[0]])}}}},_=(e,r=!1,m=!0)=>{if(!(m&&!F)){if(r)return O(null),F(null,!0),E(!1);O(e),m&&F(e),o&&P(""),f.length>h&&V(f),w&&E(!1)}};a.useCallback(e=>{if(g)return!!Z.find(m=>m[g]===e[g])},[f,h,Z]);const ge=a.useCallback(()=>{if(w||o)return o;if(B)return k(B,b);if(d&&f&&(f!=null&&f.length)){const e=f==null?void 0:f.find(r=>r[g]===Number(d));return e?k(e,b):""}else return""},[w,d,o,_,f]),xe=a.useCallback(e=>{if(P(e),e){const r=f.filter(m=>k(m,b).toLowerCase().includes(e.toLowerCase()));V(r)}else V(f)},[o]);return a.useEffect(()=>(window.addEventListener("click",e=>{(!y.current||!y.current.id||y.current.id!==e.target.id)&&(!A.current||!A.current.id||A.current.id!==e.target.id)&&(!S.current||!S.current.id||S.current.id!==e.target.id)&&E(r=>r&&(N&&N(!1),!r))}),window.removeEventListener("click",e=>{(!y.current||!y.current.id||y.current.id!==e.target.id)&&(!A.current||!A.current.id||A.current.id!==e.target.id)&&(!S.current||!S.current.id||S.current.id!==e.target.id)&&E(r=>r&&(N&&N(!1),!r))})),[]),a.useEffect(()=>{!L&&!(f!=null&&f.length)&&q()},[L]),a.useEffect(()=>{L&&(M(()=>[...x]),V(()=>[...x]))},[L,he]),a.useEffect(()=>{N&&!D&&w&&N(!0)},[D,w]),t.jsxs(t.Fragment,{children:[t.jsx("button",{ref:ne,type:"button",hidden:!0,onClick:()=>{if(L){if(h!=null&&h.length){const e=h.find(r=>r[g]===d);e&&_(e,!1,!1)}else if(x!=null&&x.length){const e=x.find(r=>r[g]===d);e&&_(e,!1,!1)}}else q()}}),t.jsx("button",{ref:ce,type:"button",hidden:!0,onClick:()=>O(null)}),t.jsxs("div",{className:`relative ${U}`,children:[J&&t.jsxs("label",{className:"mb-2 block cursor-pointer text-sm font-bold text-gray-700",children:[J,fe&&t.jsx("sup",{className:"text-[.825rem] text-red-600",children:"*"})]}),le||I!=null&&I.loading?t.jsx(ye,{count:1,counts:[2],className:`!h-[3rem] !max-h-[3rem] !min-h-[3rem] !gap-0 overflow-hidden rounded-[.625rem] !bg-[#ebebeb] !p-0 ${U}`}):t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:`flex h-[3rem] w-full items-center justify-normal rounded-[.625rem] border pl-3 shadow ${$?"bg-gray-200":"bg-white"}`,children:[ae&&!$&&t.jsx("div",{className:"!w-4 ",children:t.jsx("svg",{className:"h-4 w-4 text-gray-500 dark:text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",children:t.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"})})}),t.jsx("div",{className:"grow ",children:t.jsx("input",{type:"text",disabled:$,placeholder:se,id:de,ref:e=>{y.current=e},className:`${$?"bg-gray-200":"bg-white"} showListButton h-full w-full appearance-none rounded-[.625rem] border-0 px-3  py-2 leading-tight text-black focus:outline-none focus:outline-0 focus:ring-0`,value:ge(),onFocus:()=>{w||E(!0)},onChange:e=>xe(e.target.value.toLowerCase()),onKeyDown:e=>{e.key==="Enter"&&e.preventDefault()}})}),!$&&t.jsxs("div",{className:"mr-3 flex flex-col items-center justify-center",children:[t.jsx(re,{className:"h-4 w-5 p-0",stroke:"#717179"}),t.jsx(re,{className:"h-4 w-5 rotate-180 p-0",stroke:"#717179"})]})]}),t.jsx("div",{style:{},className:`group-hover:block ${w?"block":"hidden"} absolute top-full h-fit w-full overflow-y-auto rounded-b-md bg-white py-3 text-sm shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm ${ke()}`,children:t.jsxs("div",{className:"",children:[t.jsx("div",{className:`flex h-[2.8rem] min-h-[2.8rem] cursor-pointer items-center justify-start gap-5 truncate px-3 py-2 text-sm font-normal capitalize text-gray-900 hover:bg-primary-light
                  ${!B&&!d?"bg-primary-light":""}
                  `,onClick:()=>_(null,!0),children:"None"}),v.length&&v.find(e=>e==null?void 0:e.show)?v==null?void 0:v.map((e,r)=>{if(e!=null&&e.show)return t.jsxs("div",{title:e!=null&&e.children&&typeof(e==null?void 0:e.children)=="string"?e==null?void 0:e.children:e!=null&&e.icon&&typeof(e==null?void 0:e.icon)=="string"?e==null?void 0:e.icon:null,className:"flex h-[2.8rem] min-h-[2.8rem] cursor-pointer items-center justify-start gap-3 truncate px-3 py-2 text-sm font-normal capitalize text-gray-900 hover:bg-primary-light ",onClick:()=>(e==null?void 0:e.action)&&(e==null?void 0:e.action()),children:[e!=null&&e.icon?e.icon:null,e!=null&&e.children?e.children:null]},r)}):null,h.length?h==null?void 0:h.map((e,r)=>(e==null?void 0:e.searchableType)==="section"?t.jsx("div",{disabled:!0,className:"flex h-[2.8rem] min-h-[2.8rem] w-full items-center justify-start gap-5 truncate bg-black px-3 py-2 text-sm font-bold capitalize text-white",children:e==null?void 0:e.display},r):t.jsx("button",{type:"button",title:e&&k(e,b),className:`flex h-[2.8rem] min-h-[2.8rem] w-full cursor-pointer items-center justify-start gap-5 truncate px-3 py-2 text-sm font-normal capitalize text-gray-900 hover:bg-primary-light ${B&&(d&&d===e[g]||k(e,b)===k(B,b))?"bg-primary-light":""} `,onClick:()=>_(e),children:k(e,b)},r)):null]})}),H&&H[Q]&&t.jsx("p",{className:"text-field-error absolute inset-x-0 top-[90%] m-auto mt-2 text-[.8rem] italic text-red-500",children:te((K=H[Q])==null?void 0:K.message,{casetype:"capitalize",separator:" "})})]})]})]})},pe=a.memo(Ne);export{pe as default};
