import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as l,d as j,f as N}from"./vendor-1c28ea83.js";import{u as T}from"./react-hook-form-eec8b32f.js";import{M as _,A as I,G as v,t as y,S as w,o as A,s as R}from"./index-b3edd152.js";import{c as q,a as r}from"./yup-1b5612ec.js";import{M as i}from"./MkdInput-67f7082d.js";import{I as C}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let n=new _;const le=m=>{const{dispatch:f}=l.useContext(I),g=q({user_id:r().required(),token:r().required(),code:r().required(),type:r().required(),data:r(),status:r().required(),expired_at:r()}).required(),{dispatch:p}=l.useContext(v),[h,c]=l.useState(!1),[b,u]=l.useState(!1),k=j(),{register:a,handleSubmit:E,setError:D,setValue:s,formState:{errors:o}}=T({resolver:A(g)}),d=N();l.useEffect(function(){(async function(){try{u(!0),n.setTable("tokens");const e=await n.callRestAPI({id:m.activeId?m.activeId:Number(d==null?void 0:d.id)},"GET");e.error||(s("user_id",e.model.user_id),s("token",e.model.token),s("code",e.model.code),s("type",e.model.type),s("data",e.model.data),s("status",e.model.status),s("expired_at",e.model.expired_at),u(!1))}catch(e){u(!1),console.log("error",e),y(f,e.message)}})()},[]);const S=async e=>{c(!0);try{n.setTable("tokens"),(await n.callRestAPI({id:m.activeId?m.activeId:Number(d==null?void 0:d.id),user_id:e.user_id,token:e.token,code:e.code,type:e.type,data:e.data,status:e.status,expired_at:e.expired_at},"PUT")).error||(R(p,"Updated"),k("/admin/tokens"),m.setSidebar(!1),p({type:"REFRESH_DATA",payload:{refreshData:!0}})),c(!1)}catch(x){c(!1),console.log("Error",x),y(f,x.message)}};return l.useEffect(()=>{p({type:"SETPATH",payload:{path:"tokens"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Tokens"}),b?t.jsx(w,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:E(S),children:[t.jsx(i,{type:"text",page:"edit",name:"user_id",errors:o,label:"User_id",placeholder:"User_id",register:a,className:""}),t.jsx(i,{type:"text",page:"edit",name:"token",errors:o,label:"Token",placeholder:"Token",register:a,className:""}),t.jsx(i,{type:"text",page:"edit",name:"code",errors:o,label:"Code",placeholder:"Code",register:a,className:""}),t.jsx(i,{type:"text",page:"edit",name:"type",errors:o,label:"Type",placeholder:"Type",register:a,className:""}),t.jsx(i,{type:"text",page:"edit",name:"data",errors:o,label:"Data",placeholder:"Data",register:a,className:""}),t.jsx(i,{type:"text",page:"edit",name:"status",errors:o,label:"Status",placeholder:"Status",register:a,className:""}),t.jsx(i,{type:"text",page:"edit",name:"expired_at",errors:o,label:"Expired_at",placeholder:"Expired_at",register:a,className:""}),t.jsx(C,{type:"submit",loading:h,disabled:h,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{le as default};
