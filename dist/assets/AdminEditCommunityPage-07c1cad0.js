import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as m,r as g,d as T,f as k}from"./vendor-1c28ea83.js";import{u as D}from"./react-hook-form-eec8b32f.js";import{M as $,A as M,G as q,t as E,S as R,o as O,s as U}from"./index-b3edd152.js";import{c as G,a as u,f as w,g as H}from"./yup-1b5612ec.js";import{I as L}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let h=new $;const pe=n=>{const{dispatch:_}=m.useContext(M),A=G({title:u().required("Title is required"),description:u(),industry_id:w().required("Industry is required").typeError("Industry must be a number"),guidelines:u(),privacy:u().required("Privacy setting is required"),user_id:w().required("User ID is required").typeError("User ID must be a number"),subscription_fee:u(),has_paid:H()}).required(),{dispatch:v}=m.useContext(q),[f,x]=m.useState(!1),[I,j]=m.useState(!1),[l,y]=g.useState("basic"),[o,N]=g.useState({}),[C,b]=g.useState(""),S=T(),{register:r,handleSubmit:P,setValue:a,formState:{errors:s}}=D({resolver:O(A)}),c=k();m.useEffect(function(){(async function(){try{j(!0),h.setTable("community");const i=await h.callRestAPI({id:n.activeId?n.activeId:Number(c==null?void 0:c.id)},"GET");if(!i.error){if(a("title",i.model.title),a("description",i.model.description),a("industry_id",i.model.industry_id),a("guidelines",i.model.guidelines),a("privacy",i.model.privacy),a("user_id",i.model.user_id),a("subscription_fee",i.model.subscription_fee),a("has_paid",i.model.has_paid===1),i.model.privacy_settings)try{const t=JSON.parse(i.model.privacy_settings);N(t)}catch(t){console.error("Error parsing privacy settings",t)}j(!1)}}catch(i){j(!1),console.log("error",i),E(_,i.message)}})()},[]);const F=async i=>{x(!0),b("");try{const t=JSON.stringify({...o,subscription_fee:i.subscription_fee||"0"});h.setTable("community");const p=await h.callRestAPI({id:n.activeId?n.activeId:Number(c==null?void 0:c.id),title:i.title,description:i.description,industry_id:parseInt(i.industry_id),guidelines:i.guidelines,privacy:i.privacy,user_id:parseInt(i.user_id),has_paid:i.has_paid?1:0,subscription_fee:i.subscription_fee||"0.00",privacy_settings:t},"PUT");p.error?b(p.message||"Failed to update community"):(U(v,"Community Updated Successfully"),n.setSidebar?n.setSidebar(!1):S("/admin/community"),v({type:"REFRESH_DATA",payload:{refreshData:!0}})),x(!1)}catch(t){x(!1),console.log("Error",t),b(t.message||"Failed to update community"),E(_,t.message)}};m.useEffect(()=>{v({type:"SETPATH",payload:{path:"community"}})},[]);const d=(i,t)=>{N(p=>({...p,[i]:t}))};return e.jsx("div",{className:"edit-community-page bg-[#161616] text-white",children:e.jsxs("div",{className:"container mx-auto p-6",children:[e.jsxs("div",{className:"header mb-6",children:[e.jsx("h1",{className:"text-2xl font-semibold mb-2",children:"Edit Community"}),e.jsx("p",{className:"text-[#9ca3af] text-sm",children:"Update community information and settings"})]}),I?e.jsx("div",{className:"bg-[#161616] p-6 rounded",children:e.jsx(R,{})}):e.jsxs("div",{className:"form-container bg-[#161616]",children:[e.jsxs("div",{className:"tabs mb-6",children:[e.jsx("div",{className:`tab ${l==="basic"?"active":""}`,onClick:()=>y("basic"),children:"Basic Information"}),e.jsx("div",{className:`tab ${l==="description"?"active":""}`,onClick:()=>y("description"),children:"Description & Guidelines"}),e.jsx("div",{className:`tab ${l==="privacy"?"active":""}`,onClick:()=>y("privacy"),children:"Privacy Settings"})]}),e.jsxs("form",{onSubmit:P(F),children:[e.jsx("div",{className:`tab-content ${l==="basic"?"active":""}`,children:e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{children:"Community Details"}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"title",children:"Community Name"}),e.jsx("input",{type:"text",id:"title",...r("title"),placeholder:"Enter community name"}),s.title&&e.jsx("p",{className:"error-message",children:s.title.message})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"industry_id",children:"Industry"}),e.jsxs("select",{id:"industry_id",...r("industry_id"),children:[e.jsx("option",{value:"",children:"Select industry"}),e.jsx("option",{value:"1",children:"Technology"}),e.jsx("option",{value:"2",children:"Healthcare"}),e.jsx("option",{value:"3",children:"Finance"}),e.jsx("option",{value:"4",children:"Retail"}),e.jsx("option",{value:"5",children:"Manufacturing"}),e.jsx("option",{value:"6",children:"Education"}),e.jsx("option",{value:"7",children:"Real Estate"}),e.jsx("option",{value:"8",children:"Other"})]}),s.industry_id&&e.jsx("p",{className:"error-message",children:s.industry_id.message})]}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"privacy",children:"Privacy"}),e.jsxs("select",{id:"privacy",...r("privacy"),children:[e.jsx("option",{value:"",children:"Select privacy"}),e.jsx("option",{value:"public",children:"Public"}),e.jsx("option",{value:"private",children:"Private"})]}),s.privacy&&e.jsx("p",{className:"error-message",children:s.privacy.message})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"user_id",children:"Creator User ID"}),e.jsx("input",{type:"number",id:"user_id",...r("user_id"),placeholder:"Enter user ID"}),s.user_id&&e.jsx("p",{className:"error-message",children:s.user_id.message})]}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"subscription_fee",children:"Monthly Subscription Fee ($)"}),e.jsx("input",{type:"text",id:"subscription_fee",...r("subscription_fee"),placeholder:"0.00"}),s.subscription_fee&&e.jsx("p",{className:"error-message",children:s.subscription_fee.message})]})]}),e.jsx("div",{className:"form-input",children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",...r("has_paid"),className:"mr-2"}),"Has Paid"]})})]})}),e.jsxs("div",{className:`tab-content ${l==="description"?"active":""}`,children:[e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{children:"Description"}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"description",children:"Community Description"}),e.jsx("textarea",{id:"description",...r("description"),placeholder:"Enter community description",rows:4}),s.description&&e.jsx("p",{className:"error-message",children:s.description.message})]})]}),e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{children:"Guidelines"}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"guidelines",children:"Community Guidelines"}),e.jsx("textarea",{id:"guidelines",...r("guidelines"),placeholder:"Enter community guidelines (HTML supported)",rows:6}),s.guidelines&&e.jsx("p",{className:"error-message",children:s.guidelines.message})]})]})]}),e.jsx("div",{className:`tab-content ${l==="privacy"?"active":""}`,children:e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{children:"Privacy & Access Settings"}),e.jsx("div",{className:"form-input",children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:o.enable_affiliate||!1,onChange:i=>d("enable_affiliate",i.target.checked),className:"mr-2"}),"Enable Affiliate Program"]})}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"who_can_join",children:"Who Can Join"}),e.jsxs("select",{id:"who_can_join",value:o.who_can_join||"invite_only",onChange:i=>d("who_can_join",i.target.value),children:[e.jsx("option",{value:"anyone",children:"Anyone"}),e.jsx("option",{value:"invite_only",children:"Invite Only"}),e.jsx("option",{value:"approval",children:"With Approval"})]})]}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"who_can_invite",children:"Who Can Invite"}),e.jsxs("select",{id:"who_can_invite",value:o.who_can_invite||"everyone",onChange:i=>d("who_can_invite",i.target.value),children:[e.jsx("option",{value:"everyone",children:"Everyone"}),e.jsx("option",{value:"admins_only",children:"Admins Only"}),e.jsx("option",{value:"moderators",children:"Moderators & Admins"})]})]}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"who_can_post",children:"Who Can Post"}),e.jsxs("select",{id:"who_can_post",value:o.who_can_post||"everyone",onChange:i=>d("who_can_post",i.target.value),children:[e.jsx("option",{value:"everyone",children:"Everyone"}),e.jsx("option",{value:"admins_only",children:"Admins Only"}),e.jsx("option",{value:"moderators",children:"Moderators & Admins"})]})]}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"who_can_see_content",children:"Who Can See Content"}),e.jsxs("select",{id:"who_can_see_content",value:o.who_can_see_content||"members_only",onChange:i=>d("who_can_see_content",i.target.value),children:[e.jsx("option",{value:"everyone",children:"Everyone"}),e.jsx("option",{value:"members_only",children:"Members Only"})]})]}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"content_moderation",children:"Content Moderation"}),e.jsxs("select",{id:"content_moderation",value:o.content_moderation||"admin_approval",onChange:i=>d("content_moderation",i.target.value),children:[e.jsx("option",{value:"none",children:"None"}),e.jsx("option",{value:"admin_approval",children:"Admin Approval"}),e.jsx("option",{value:"moderator_approval",children:"Moderator Approval"})]})]})]})}),C&&e.jsx("div",{className:"bg-red-500 bg-opacity-10 border border-red-500 text-red-500 px-4 py-3 rounded mb-4",children:C}),e.jsxs("div",{className:"flex justify-end mt-6",children:[e.jsx("button",{type:"button",onClick:()=>{n.setSidebar?n.setSidebar(!1):S("/admin/community")},className:"cancel-button",children:"Cancel"}),e.jsx(L,{type:"submit",loading:f,disabled:f,className:"submit-button",children:"Update Community"})]})]})]})]})})};export{pe as default};
