import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as a,f as n}from"./vendor-1c28ea83.js";import{M as o,A as x,G as f,t as h,S as p}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let m=new o;const F=()=>{const{dispatch:l}=a.useContext(x);a.useContext(f);const[s,d]=a.useState({}),[c,i]=a.useState(!0),r=n();return a.useEffect(function(){(async function(){try{i(!0),m.setTable("meeting_attendee");const t=await m.callRestAPI({id:Number(r==null?void 0:r.id),join:""},"GET");t.error||(d(t.model),i(!1))}catch(t){i(!1),console.log("error",t),h(l,t.message)}})()},[]),e.jsx("div",{className:"shadow-md rounded mx-auto p-5",children:c?e.jsx(p,{}):e.jsxs(e.Fragment,{children:[e.jsx("h4",{className:"text-2xl font-medium",children:"View Meeting_attendee"}),e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"flex mb-4",children:[e.jsx("div",{className:"flex-1",children:"Id"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.id})]})}),e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"flex mb-4",children:[e.jsx("div",{className:"flex-1",children:"Meeting_id"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.meeting_id})]})}),e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"flex mb-4",children:[e.jsx("div",{className:"flex-1",children:"User_id"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.user_id})]})}),e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"flex mb-4",children:[e.jsx("div",{className:"flex-1",children:"Created_at"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.created_at})]})}),e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"flex mb-4",children:[e.jsx("div",{className:"flex-1",children:"Updated_at"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.updated_at})]})})]})})};export{F as default};
