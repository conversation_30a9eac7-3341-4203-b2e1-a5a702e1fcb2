import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as r,d as j,f as I}from"./vendor-1c28ea83.js";import{u as N}from"./react-hook-form-eec8b32f.js";import{M as v,A as w,G as R,t as b,S as A,o as C,s as T}from"./index-b3edd152.js";import{c as P,a as p}from"./yup-1b5612ec.js";import{M as f}from"./MkdInput-67f7082d.js";import{I as k}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let s=new v;const le=i=>{const{dispatch:y}=r.useContext(w),h=P({user_id:p().required(),community_id:p().required(),role:p().required()}).required(),{dispatch:m}=r.useContext(R),[x,a]=r.useState(!1),[_,l]=r.useState(!1),g=j(),{register:d,handleSubmit:E,setError:q,setValue:n,formState:{errors:u}}=N({resolver:C(h)}),o=I();r.useEffect(function(){(async function(){try{l(!0),s.setTable("community_member");const e=await s.callRestAPI({id:i.activeId?i.activeId:Number(o==null?void 0:o.id)},"GET");e.error||(n("user_id",e.model.user_id),n("community_id",e.model.community_id),n("role",e.model.role),l(!1))}catch(e){l(!1),console.log("error",e),b(y,e.message)}})()},[]);const S=async e=>{a(!0);try{s.setTable("community_member"),(await s.callRestAPI({id:i.activeId?i.activeId:Number(o==null?void 0:o.id),user_id:e.user_id,community_id:e.community_id,role:e.role},"PUT")).error||(T(m,"Updated"),g("/admin/community_member"),i.setSidebar(!1),m({type:"REFRESH_DATA",payload:{refreshData:!0}})),a(!1)}catch(c){a(!1),console.log("Error",c),b(y,c.message)}};return r.useEffect(()=>{m({type:"SETPATH",payload:{path:"community_member"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Community_member"}),_?t.jsx(A,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:E(S),children:[t.jsx(f,{type:"text",page:"edit",name:"user_id",errors:u,label:"User_id",placeholder:"User_id",register:d,className:""}),t.jsx(f,{type:"text",page:"edit",name:"community_id",errors:u,label:"Community_id",placeholder:"Community_id",register:d,className:""}),t.jsx(f,{type:"text",page:"edit",name:"role",errors:u,label:"Role",placeholder:"Role",register:d,className:""}),t.jsx(k,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{le as default};
