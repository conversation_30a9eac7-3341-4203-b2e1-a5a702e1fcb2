import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{u as l}from"./react-hook-form-eec8b32f.js";import"./index-b3edd152.js";import"./pdf-lib-623decea.js";import"./react-toggle-58b0879a.js";/* empty css                 */import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./@uppy/dashboard-3a4b1704.js";const L=()=>{const{register:a,handleSubmit:r}=l(),s=t=>{console.log(t)};return e.jsx("div",{className:"space-y-6 p-4 md:p-6",children:e.jsxs("div",{className:"rounded-xl bg-[#161616] p-6",children:[e.jsx("h2",{className:"mb-8 text-2xl font-bold text-[#eaeaea]",children:"Join Community"}),e.jsxs("form",{onSubmit:r(s),className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-[#eaeaea]",children:"First Name"}),e.jsx("input",{className:"h-[50px] w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]",placeholder:"Enter first name",...a("first_name")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-[#eaeaea]",children:"Last Name"}),e.jsx("input",{className:"h-[50px] w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]",placeholder:"Enter last name",...a("last_name")})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-[#eaeaea]",children:"Email"}),e.jsx("input",{className:"h-[50px] w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]",placeholder:"Enter email",type:"email",...a("email")})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{type:"button",className:"rounded-lg border border-[#363636] px-6 py-3 text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{type:"submit",className:"rounded-lg bg-[#2e7d32] px-6 py-3 text-[#eaeaea]",children:"Join Now"})]})]})]})})};export{L as default};
