import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as t,d as _}from"./vendor-1c28ea83.js";import{u as h}from"./react-hook-form-eec8b32f.js";import{G as b,A as g,o as S,M as A,s as j,t as E}from"./index-b3edd152.js";import{c as R,a as l}from"./yup-1b5612ec.js";import{M as d}from"./MkdInput-67f7082d.js";import{I as w}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const te=({setSidebar:n})=>{const{dispatch:i}=t.useContext(b),u=R({referral_id:l().required(),community_id:l().required(),is_primary:l().required()}).required(),{dispatch:c}=t.useContext(g),[p,a]=t.useState(!1),f=_(),{register:o,handleSubmit:y,setError:N,formState:{errors:s}}=h({resolver:S(u)}),x=async m=>{a(!0);try{let r=new A;r.setTable("referral_communities"),(await r.callRestAPI({referral_id:m.referral_id,community_id:m.community_id,is_primary:m.is_primary},"POST")).error||(j(i,"Added"),f("/admin/referral_communities"),n(!1),i({type:"REFRESH_DATA",payload:{refreshData:!0}})),a(!1)}catch(r){a(!1),console.log("Error",r),E(c,r.message)}};return t.useEffect(()=>{i({type:"SETPATH",payload:{path:"referral_communities"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Opportunity_communities"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:y(x),children:[e.jsx(d,{type:"text",page:"add",name:"referral_id",errors:s,label:"Referral_id",placeholder:"Referral_id",register:o,className:""}),e.jsx(d,{type:"text",page:"add",name:"community_id",errors:s,label:"Community_id",placeholder:"Community_id",register:o,className:""}),e.jsx(d,{type:"text",page:"add",name:"is_primary",errors:s,label:"Is_primary",placeholder:"Is_primary",register:o,className:""}),e.jsx(w,{type:"submit",loading:p,disabled:p,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{te as default};
