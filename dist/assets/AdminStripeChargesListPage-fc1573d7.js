import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as o}from"./vendor-1c28ea83.js";import{M as T,A as z,G as F,o as I,t as G}from"./index-b3edd152.js";import{u as L}from"./react-hook-form-eec8b32f.js";import{c as M}from"./yup-1b5612ec.js";import"./pdf-lib-623decea.js";import{P as $}from"./index-7ba88dde.js";import"./index-e2604cb4.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const h=[{header:"Row",accessor:"row"},{header:"Product",type:"metadata",pre_accessor:"metadata",accessor:"app_product_name"},{header:"Status",accessor:"status"},{header:"Currency",accessor:"currency"},{header:"Amount",accessor:"amount",type:"currency"},{header:"Amount captured",accessor:"amount_captured",type:"currency"},{header:"Amount refunded",accessor:"amount_refunded",type:"currency"},{header:"Created at",accessor:"created",type:"timestamp"}],xe=()=>{var u;const l=new T,{dispatch:g,state:B}=o.useContext(z),{dispatch:c}=o.useContext(F),[d,x]=o.useState(null),[r,f]=o.useState({}),[p,m]=o.useState(10),[y,H]=o.useState(0),[w,K]=o.useState(0),[b,j]=o.useState(!1),[S,v]=o.useState(!1),N=M({}),{register:O,handleSubmit:P,formState:{errors:U}}=L({resolver:I(N)});function C(s){(async function(){m(s),await i({limit:s})})()}function R(){(async function(){await i({limit:p,before:r==null?void 0:r.data[0].id})})()}function k(){(async function(){await i({limit:p,after:r==null?void 0:r.data[(r==null?void 0:r.data.length)-1].id})})()}async function i(s){var n,a;try{const{list:t,limit:_,error:D,message:E}=await l.getStripeCharges(s);if(console.log(t),D&&showToast(c,E,5e3),!t)return;d||x(((n=t==null?void 0:t.data[0])==null?void 0:n.id)??""),f(t),m(+_),j(d&&d!==((a=t.data[0])==null?void 0:a.id)),v(t.has_more)}catch(t){console.error("ERROR",t),showToast(c,t.message,5e3),G(g,t.message)}}const A=s=>{i({})};return o.useEffect(()=>{c({type:"SETPATH",payload:{path:"orders"}}),async function(){i({})}()},[]),e.jsxs(e.Fragment,{children:[e.jsxs("form",{className:"mb-10 rounded bg-white p-5 shadow",onSubmit:P(A),children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Search"}),e.jsx("div",{className:"filter-form-holder mt-10 flex flex-wrap"}),e.jsxs("div",{className:"search-buttons pl-2",children:[e.jsx("button",{type:"submit",className:"mr-2 inline-block rounded bg-blue-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:"Search"}),e.jsx("button",{type:"reset",onClick:()=>i(1),className:"inline-block rounded bg-gray-800 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-gray-900 hover:shadow-lg focus:bg-gray-900 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-gray-900 active:shadow-lg",children:"Reset"})]})]}),e.jsxs("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:[e.jsx("div",{className:"mb-3 flex w-full justify-between text-center  ",children:e.jsx("h4",{className:"text-2xl font-medium",children:"Prices "})}),e.jsx("div",{className:"overflow-x-auto border-b border-gray-200 shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:h.map((s,n)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[s.header,e.jsx("span",{children:s.isSorted?s.isSortedDesc?" ▼":" ▲":""})]},n))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:(u=r==null?void 0:r.data)==null?void 0:u.map((s,n)=>e.jsx("tr",{children:h.map((a,t)=>a.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4"},t):a.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.mapping[s[a.accessor]]},t):a.type==="timestamp"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(s[a.accessor]*1e3).toLocaleString("en-US")},t):a.type==="currency"?e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:["$",Number(s[a.accessor]/100).toFixed(2)]},t):a.type==="metadata"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s[a.pre_accessor][a.accessor]??"n/a"},t):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s[a.accessor]},t))},n))})]})})]}),e.jsx($,{currentPage:w,pageCount:y,pageSize:p,canPreviousPage:b,canNextPage:S,updatePageSize:C,previousPage:R,nextPage:k})]})};export{xe as default};
