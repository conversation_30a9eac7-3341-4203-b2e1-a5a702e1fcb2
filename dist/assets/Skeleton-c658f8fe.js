import{j as i}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{S as e}from"./react-loading-skeleton-ac550a67.js";import"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const N=({className:p="",count:t=5,counts:o=[2,1,3,1,1],circle:m=!1})=>i.jsx("div",{className:`flex h-fit max-h-screen min-h-fit w-full flex-col gap-5 overflow-hidden p-4 ${p}`,children:Array.from({length:t}).map((l,r)=>i.jsx(e,{count:o[r]??1,height:o[r]&&o[r]>1||r+1===t?25:80,circle:m,baseColor:"#1f2937",highlightColor:"#374151",duration:2,enableAnimation:!0,style:{borderRadius:m?"50%":"0.375rem"}},`skeleton-${r}`))});export{N as default};
