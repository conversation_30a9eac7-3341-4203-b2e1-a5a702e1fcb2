import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import"./index-b3edd152.js";import{I as c}from"./index-632d14e3.js";import{M as x}from"./index.esm-1ac45320.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const G=({open:l,closeModalFunction:s,actionHandler:a,message:i,title:m,messageClasses:d,titleClasses:n,acceptText:t="",rejectText:p="Cancel",loading:r=!1,allowAccept:o=!0})=>e.jsx("aside",{className:`fixed inset-0 m-auto flex items-center justify-center backdrop-blur-sm transition-all ${l?"scale-100":"scale-0"}`,style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:e.jsxs("section",{className:"flex w-[25rem] min-w-[25rem] flex-col  gap-6 rounded-md bg-white px-6 py-6",children:[e.jsxs("div",{className:"flex justify-between ",children:[e.jsx("div",{children:m?e.jsx("div",{className:` ${n}`,children:m}):null}),e.jsx("button",{disabled:r,onClick:s,children:e.jsx(x,{className:"text-xl"})})]}),i?e.jsx("div",{className:`text-[#667085] ${d}`,children:i}):null,e.jsxs("div",{className:"flex w-full justify-between gap-2 font-medium uppercase leading-[1.5rem] text-[base]",children:[e.jsx(c,{disabled:r,loading:r,className:`${o?"flex !w-1/2":"hidden w-0"}  h-[2.75rem] !rounded-[.625rem] bg-primary uppercase !text-white`,onClick:a,children:t&&t.toLowerCase()!=="yes"?`Yes ${t}`:"Yes"}),e.jsx("button",{disabled:r,className:`flex h-[2.75rem] items-center justify-center rounded-[.625rem]  border border-[#d8dae5] text-[#667085] ${o?" w-1/2":"grow"}`,onClick:s,children:p})]})]})});export{G as default};
