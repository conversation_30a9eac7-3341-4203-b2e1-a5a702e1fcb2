import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as e,d as b}from"./vendor-1c28ea83.js";import{u as g}from"./react-hook-form-eec8b32f.js";import{G as S,A as y,o as A,M as E,s as j,t as w}from"./index-b3edd152.js";import{c as C,a as p}from"./yup-1b5612ec.js";import{M as c}from"./MkdInput-67f7082d.js";import{I as N}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const et=({setSidebar:d})=>{const{dispatch:s}=e.useContext(S),l=C({commission_rate:p().required(),description:p().required()}).required(),{dispatch:u}=e.useContext(y),[i,r]=e.useState(!1),f=b(),{register:a,handleSubmit:x,setError:D,formState:{errors:m}}=g({resolver:A(l)}),h=async n=>{r(!0);try{let o=new E;o.setTable("commission"),(await o.callRestAPI({commission_rate:n.commission_rate,description:n.description},"POST")).error||(j(s,"Added"),f("/admin/commission"),d(!1),s({type:"REFRESH_DATA",payload:{refreshData:!0}})),r(!1)}catch(o){r(!1),console.log("Error",o),w(u,o.message)}};return e.useEffect(()=>{s({type:"SETPATH",payload:{path:"commission"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Commission"}),t.jsxs("form",{className:"w-full max-w-lg",onSubmit:x(h),children:[t.jsx(c,{type:"text",page:"add",name:"commission_rate",errors:m,label:"Commission_rate",placeholder:"Commission_rate",register:a,className:""}),t.jsx(c,{type:"text",page:"add",name:"description",errors:m,label:"Description",placeholder:"Description",register:a,className:""}),t.jsx(N,{type:"submit",loading:i,disabled:i,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{et as default};
