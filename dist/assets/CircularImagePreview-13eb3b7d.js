import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r,R as a}from"./vendor-1c28ea83.js";import{M as s,A as p,L as m}from"./index-b3edd152.js";import"./index-e2604cb4.js";import{M as n}from"./index-2d6e6aa7.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";new s;const u=({className:i,image:t})=>{a.useContext(p),r.useRef(null),r.useState(!1);const[o,l]=r.useState(!1);return r.useState(""),e.jsxs(e.Fragment,{children:[t?e.jsx(e.Fragment,{children:e.jsx("div",{onClick:()=>l(!0),className:`h-[3rem] w-[3rem] cursor-pointer overflow-hidden rounded-full border ${i}`,children:e.jsx("img",{src:t,className:"h-full w-full",alt:""})})}):null,e.jsx(m,{children:e.jsx(n,{isOpen:o,modalCloseClick:()=>l(!1),title:"Image Preview",modalHeader:!0,classes:{modalDialog:"max-h-[90%] h-fit min-h-fit overflow-clip !w-full md:!w-[29.0625rem]",modal:"h-full",modalContent:"h-full w-full flex items-center"},children:o&&e.jsx(m,{children:e.jsx("img",{className:"max-h-auto min-h-auto h-auto w-full",src:t,alt:"preview"})})})})]})},y=r.memo(u);export{y as default};
