import{j as e}from"./@react-google-maps/api-211df1ae.js";import{f as t,d as r}from"./vendor-1c28ea83.js";import{M as a}from"./MkdInput-67f7082d.js";import"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const I=()=>{t();const s=r();return e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsx("div",{className:"border-b border-[#363636] bg-[#161616] p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{onClick:()=>s("/member/chat"),className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:"← Back"}),e.jsx("div",{className:"h-10 w-10 rounded-full bg-[#2e7d32]"}),e.jsxs("div",{children:[e.jsx("h2",{className:"font-bold text-[#eaeaea]",children:"John Doe"}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Online"})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsx("button",{className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea]",children:"View Profile"})})]})}),e.jsx("div",{className:"flex-1 overflow-y-auto p-4"}),e.jsx("div",{className:"border-t border-[#363636] bg-[#161616] p-4",children:e.jsxs("div",{className:"flex gap-4",children:[e.jsx(a,{className:"flex-1",placeholder:"Type your message..."}),e.jsx("button",{className:"rounded-lg bg-[#2e7d32] px-6 py-2 text-[#eaeaea]",children:"Send"})]})})]})};export{I as default};
