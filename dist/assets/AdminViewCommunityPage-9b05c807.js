import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as r,f as g,d as b}from"./vendor-1c28ea83.js";import{M as v,A as y,G as _,t as k,S as C}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let o=new v;const K=()=>{const{dispatch:x}=r.useContext(y);r.useContext(_);const[s,h]=r.useState({}),[p,c]=r.useState(!0),[d,j]=r.useState({}),[a,u]=r.useState({}),n=g(),l=b(),m=t=>t?new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A";r.useEffect(function(){(async function(){try{c(!0),o.setTable("community");const t=await o.callRestAPI({id:Number(n==null?void 0:n.id)},"GET");if(!t.error){if(h(t.model),t.model.privacy_settings)try{const i=JSON.parse(t.model.privacy_settings);j(i)}catch(i){console.error("Error parsing privacy settings",i)}if(t.model.user_details)try{const i=JSON.parse(t.model.user_details);u(i)}catch(i){console.error("Error parsing user details",i)}c(!1)}}catch(t){c(!1),console.log("error",t),k(x,t.message)}})()},[]);const N=t=>t==="public"?"privacy-badge public":"privacy-badge private";return e.jsx("div",{className:"view-community-page bg-[#1E1E1E] text-white min-h-screen",children:e.jsx("div",{className:"container mx-auto p-6",children:p?e.jsx("div",{className:"bg-[#161616] p-6 rounded",children:e.jsx(C,{})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"header mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Community Details"}),e.jsxs("button",{onClick:()=>l("/admin/community"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to List"]})]}),e.jsx("p",{className:"text-[#9ca3af] text-sm",children:"View and manage community details"})]}),e.jsxs("div",{className:"content-wrapper bg-[#161616] rounded-lg border border-[#2d2d3d] overflow-hidden",children:[e.jsx("div",{className:"header-section p-6 border-b border-[#2d2d3d]",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-2",children:(s==null?void 0:s.title)||"Untitled Community"}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-[#9ca3af]",children:[e.jsxs("div",{children:["ID: ",s==null?void 0:s.id]}),e.jsxs("div",{children:["Created: ",m(s==null?void 0:s.created_at)]}),(d==null?void 0:d.enable_affiliate)&&e.jsx("span",{className:"affiliate-badge",children:"Affiliate Enabled"})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:N(s==null?void 0:s.privacy),children:(s==null?void 0:s.privacy)||"Unknown"}),(s==null?void 0:s.subscription_fee)&&parseFloat(s.subscription_fee)>0&&e.jsxs("span",{className:"type-badge bg-[#252538] text-white px-3 py-1 rounded-full text-xs",children:["$",parseFloat(s.subscription_fee).toFixed(2),"/month"]})]})]})}),e.jsxs("div",{className:"details-grid p-6 grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"left-column space-y-6",children:[e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Basic Information"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Title"}),e.jsx("div",{children:(s==null?void 0:s.title)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Industry ID"}),e.jsx("div",{children:(s==null?void 0:s.industry_id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Privacy"}),e.jsx("div",{children:(s==null?void 0:s.privacy)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Has Paid"}),e.jsx("div",{children:s!=null&&s.has_paid?"Yes":"No"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Subscription Fee"}),e.jsx("div",{children:parseFloat(s==null?void 0:s.subscription_fee)>0?`$${parseFloat(s.subscription_fee).toFixed(2)}/month`:"Free"})]})]})]}),e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Creator Information"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"User ID"}),e.jsx("div",{children:(s==null?void 0:s.user_id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Creator Name"}),e.jsx("div",{children:a!=null&&a.first_name&&(a!=null&&a.last_name)?`${a.first_name} ${a.last_name}`:"N/A"})]}),(a==null?void 0:a.linkedin_profile)&&e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"LinkedIn Profile"}),e.jsx("div",{children:a.linkedin_profile})]}),(a==null?void 0:a.website_url)&&e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Website"}),e.jsx("div",{children:a.website_url})]})]})]})]}),e.jsxs("div",{className:"right-column space-y-6",children:[e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Description"}),e.jsx("div",{className:"bg-[#1a1a1a] rounded-lg p-4",children:e.jsx("p",{className:"whitespace-pre-wrap",children:(s==null?void 0:s.description)||"No description provided."})})]}),e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Guidelines"}),e.jsx("div",{className:"bg-[#1a1a1a] rounded-lg p-4",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:(s==null?void 0:s.guidelines)||"No guidelines provided."}})})]}),e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Privacy Settings"}),e.jsx("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:Object.entries(d).map(([t,i])=>e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:t.replace(/_/g," ").replace(/\b\w/g,f=>f.toUpperCase())}),e.jsx("div",{children:typeof i=="boolean"?i?"Yes":"No":i})]},t))})]})]})]}),e.jsxs("div",{className:"dates-section p-6 border-t border-[#2d2d3d]",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Dates"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Created At"}),e.jsx("div",{children:m(s==null?void 0:s.created_at)})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Updated At"}),e.jsx("div",{children:m(s==null?void 0:s.updated_at)})]})]})]}),e.jsxs("div",{className:"actions-section p-6 border-t border-[#2d2d3d] flex justify-end gap-4",children:[e.jsxs("button",{onClick:()=>l(`/admin/edit-community/${s==null?void 0:s.id}`),className:"edit-button bg-[#22c55e] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Edit Community"]}),e.jsxs("button",{onClick:()=>l("/admin/community"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to List"]})]})]})]})})})};export{K as default};
