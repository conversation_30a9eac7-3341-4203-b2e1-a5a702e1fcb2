import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as s,d as S,r as g}from"./vendor-1c28ea83.js";import{M as x,A as w,G as b,L as o,R as M,U as E}from"./index-b3edd152.js";import{M as c}from"./index-0cdf3a8c.js";import{M as A}from"./index-db36e1ef.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";new x;const j=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Topic",accessor:"topic",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Description",accessor:"description",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Meeting_datetime",accessor:"meeting_datetime",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Created_by",accessor:"created_by",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Meeting_link",accessor:"meeting_link",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Created_at",accessor:"created_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],X=()=>{s.useContext(w),s.useContext(b),S();const[p,t]=s.useState(!1),[r,a]=s.useState(!1),[m,f]=s.useState(),h=g.useRef(null),[D,u]=s.useState([]),d=(i,l,n=[])=>{switch(i){case"add":t(l);break;case"edit":a(l),u(n),f(n[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mt-[100px] overflow-x-auto rounded bg-white p-5 shadow",children:e.jsx(o,{children:e.jsx(A,{columns:j,tableRole:"admin",table:"meetings",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!1,multiple:!1,action:i=>d("edit",!0,i)},delete:{show:!1,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!1,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPosition:"ontable",refreshRef:h})})}),e.jsx(o,{children:e.jsx(c,{isModalActive:p,closeModalFn:()=>t(!1),children:e.jsx(M,{setSidebar:t})})}),r&&e.jsx(o,{children:e.jsx(c,{isModalActive:r,closeModalFn:()=>a(!1),children:e.jsx(E,{activeId:m,setSidebar:a})})})]})};export{X as default};
