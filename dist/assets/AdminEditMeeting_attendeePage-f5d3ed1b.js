import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as s,d as I,f as j}from"./vendor-1c28ea83.js";import{u as v}from"./react-hook-form-eec8b32f.js";import{M as w,A as N,G as A,t as g,S as M,o as T,s as R}from"./index-b3edd152.js";import{c as P,a as x}from"./yup-1b5612ec.js";import{M as _}from"./MkdInput-67f7082d.js";import{I as k}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let a=new w;const ne=r=>{const{dispatch:l}=s.useContext(N),h=P({meeting_id:x().required(),user_id:x().required()}).required(),{dispatch:o}=s.useContext(A),[c,d]=s.useState(!1),[b,n]=s.useState(!1),y=I(),{register:u,handleSubmit:E,setError:C,setValue:p,formState:{errors:f}}=v({resolver:T(h)}),i=j();s.useEffect(function(){(async function(){try{n(!0),a.setTable("meeting_attendee");const e=await a.callRestAPI({id:r.activeId?r.activeId:Number(i==null?void 0:i.id)},"GET");e.error||(p("meeting_id",e.model.meeting_id),p("user_id",e.model.user_id),n(!1))}catch(e){n(!1),console.log("error",e),g(l,e.message)}})()},[]);const S=async e=>{d(!0);try{a.setTable("meeting_attendee"),(await a.callRestAPI({id:r.activeId?r.activeId:Number(i==null?void 0:i.id),meeting_id:e.meeting_id,user_id:e.user_id},"PUT")).error||(R(o,"Updated"),y("/admin/meeting_attendee"),r.setSidebar(!1),o({type:"REFRESH_DATA",payload:{refreshData:!0}})),d(!1)}catch(m){d(!1),console.log("Error",m),g(l,m.message)}};return s.useEffect(()=>{o({type:"SETPATH",payload:{path:"meeting_attendee"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Meeting_attendee"}),b?t.jsx(M,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:E(S),children:[t.jsx(_,{type:"text",page:"edit",name:"meeting_id",errors:f,label:"Meeting_id",placeholder:"Meeting_id",register:u,className:""}),t.jsx(_,{type:"text",page:"edit",name:"user_id",errors:f,label:"User_id",placeholder:"User_id",register:u,className:""}),t.jsx(k,{type:"submit",loading:c,disabled:c,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ne as default};
