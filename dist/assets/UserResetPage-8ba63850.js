import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as P,r as R,d as F,u as S,L as M}from"./vendor-1c28ea83.js";import{u as H}from"./react-hook-form-eec8b32f.js";import{A as V,$ as Z,B as D,o as $,M as q,s as O,t as K}from"./index-b3edd152.js";import{c as T,a as n,b as U}from"./yup-1b5612ec.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const z=()=>e.jsx("svg",{width:"30",height:"24",viewBox:"0 0 30 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("g",{id:"Frame",children:e.jsx("path",{id:"Vector",d:"M15.1594 3.99375L10.6219 7.66875C9.86719 8.27812 9.72188 9.375 10.2938 10.1578C10.8984 10.9922 12.075 11.1562 12.8859 10.5234L17.5406 6.90469C17.8688 6.65156 18.3375 6.70781 18.5953 7.03594C18.8531 7.36406 18.7922 7.83281 18.4641 8.09062L17.4844 8.85L24 14.85V6H23.9672L23.7844 5.88281L20.3812 3.70312C19.6641 3.24375 18.825 3 17.9719 3C16.95 3 15.9562 3.35156 15.1594 3.99375ZM16.2281 9.825L13.8047 11.7094C12.3281 12.8625 10.1859 12.5625 9.07969 11.0437C8.03906 9.61406 8.30156 7.61719 9.675 6.50625L13.575 3.35156C13.0312 3.12187 12.4453 3.00469 11.85 3.00469C10.9688 3 10.1109 3.2625 9.375 3.75L6 6V16.5H7.32187L11.6063 20.4094C12.525 21.2484 13.9453 21.1828 14.7844 20.2641C15.0422 19.9781 15.2156 19.6453 15.3047 19.2984L16.1016 20.0297C17.0156 20.8687 18.4406 20.8078 19.2797 19.8937C19.4906 19.6641 19.6453 19.3969 19.7438 19.1203C20.6531 19.7297 21.8906 19.6031 22.6547 18.7687C23.4937 17.8547 23.4328 16.4297 22.5187 15.5906L16.2281 9.825ZM0.75 6C0.3375 6 0 6.3375 0 6.75V16.5C0 17.3297 0.670312 18 1.5 18H3C3.82969 18 4.5 17.3297 4.5 16.5V6H0.75ZM2.25 15C2.44891 15 2.63968 15.079 2.78033 15.2197C2.92098 15.3603 3 15.5511 3 15.75C3 15.9489 2.92098 16.1397 2.78033 16.2803C2.63968 16.421 2.44891 16.5 2.25 16.5C2.05109 16.5 1.86032 16.421 1.71967 16.2803C1.57902 16.1397 1.5 15.9489 1.5 15.75C1.5 15.5511 1.57902 15.3603 1.71967 15.2197C1.86032 15.079 2.05109 15 2.25 15ZM25.5 6V16.5C25.5 17.3297 26.1703 18 27 18H28.5C29.3297 18 30 17.3297 30 16.5V6.75C30 6.3375 29.6625 6 29.25 6H25.5ZM27 15.75C27 15.5511 27.079 15.3603 27.2197 15.2197C27.3603 15.079 27.5511 15 27.75 15C27.9489 15 28.1397 15.079 28.2803 15.2197C28.421 15.3603 28.5 15.5511 28.5 15.75C28.5 15.9489 28.421 16.1397 28.2803 16.2803C28.1397 16.421 27.9489 16.5 27.75 16.5C27.5511 16.5 27.3603 16.421 27.2197 16.2803C27.079 16.1397 27 15.9489 27 15.75Z",fill:"#7DD87D"})})}),fe=()=>{var d;const{dispatch:l}=P.useContext(V),[N,a]=R.useState(!1),y=F(),i=(d=S().state)==null?void 0:d.email,v=window.location.search,B=new URLSearchParams(v).get("token"),E=T({code:n().required(),password:n().required(),confirmPassword:n().oneOf([U("password"),null],"Passwords must match")}).required(),{register:r,handleSubmit:L,setError:m,formState:{errors:t}}=H({resolver:$(E)}),A=async c=>{var x,p,h,u,f,w,b,g;let k=new q;try{a(!0);const s=await k.reset(B,c.code,c.password);if(!s.error)O(l,"Password Reset"),setTimeout(()=>{y("/member/login")},2e3);else if(s.validation){const C=Object.keys(s.validation);for(let o=0;o<C.length;o++){const j=C[o];m(j,{type:"manual",message:s.validation[j]})}}a(!1)}catch(s){a(!1),console.log("Error",s),m("code",{type:"manual",message:(p=(x=s==null?void 0:s.response)==null?void 0:x.data)!=null&&p.message?(u=(h=s==null?void 0:s.response)==null?void 0:h.data)==null?void 0:u.message:s==null?void 0:s.message}),K(l,(w=(f=s==null?void 0:s.response)==null?void 0:f.data)!=null&&w.message?(g=(b=s==null?void 0:s.response)==null?void 0:b.data)==null?void 0:g.message:s==null?void 0:s.message)}};return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"w-full min-h-screen bg-black flex flex-col",children:[e.jsxs("header",{style:{marginBottom:"20px"},className:"flex justify-between px-[5vw]  bg-[#161616] h-[62px] items-center py-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(z,{}),e.jsx("span",{className:"text-[16px] font-bold text-[#EAEAEA]",children:"RainmakerOS"})]}),e.jsxs("div",{className:"flex items-center gap-[2rem]",children:[e.jsx("a",{href:"/member/login",className:"text-[16px] text-[#eaeaea] hover:text-[#7dd87d]",children:"Home"}),e.jsx("a",{href:"/member/signup",className:"text-[16px] text-[#eaeaea] hover:text-[#7dd87d]",children:"Register"})]})]}),e.jsx("div",{className:"flex justify-center items-center flex-1 bg-[#1f1f1f]",children:e.jsxs("div",{className:"w-full max-w-md",children:[e.jsx("div",{className:"bg-[#161616] w-[448px] rounded-lg shadow-lg overflow-hidden",children:e.jsxs("div",{className:"px-8 py-6",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx("div",{className:"h-16 w-16 flex items-center justify-center",children:e.jsx(Z,{className:"text-[#4CAF50] text-4xl"})})}),e.jsx("h2",{className:"text-center text-[24px] font-bold text-[#EAEAEA] mb-2",children:"Reset Password"}),e.jsx("p",{className:"text-center text-[14px] text-[#B5B5B5] mb-6",children:i?`Enter the code sent to ${i} and your new password`:"Enter the code sent to your email and your new password"}),e.jsxs("form",{onSubmit:L(A),children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-sm font-medium text-[#B5B5B5] mb-2",htmlFor:"code",children:"Reset Code"}),e.jsx("input",{type:"text",placeholder:"Enter reset code",...r("code"),className:"w-full h-[50px] bg-transparent text-white text-[16px] border border-gray-600 rounded px-3 py-2 focus:outline-none focus:border-[#4CAF50]"}),t.code&&e.jsx("p",{className:"text-xs text-red-500 mt-1",children:t.code.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-sm font-medium text-[#B5B5B5] mb-2",htmlFor:"password",children:"New Password"}),e.jsx("input",{type:"password",placeholder:"Enter new password",...r("password"),className:"w-full h-[50px] bg-transparent text-white text-[16px] border border-gray-600 rounded px-3 py-2 focus:outline-none focus:border-[#4CAF50]"}),t.password&&e.jsx("p",{className:"text-xs text-red-500 mt-1",children:t.password.message})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-[#B5B5B5] mb-2",htmlFor:"confirmPassword",children:"Confirm Password"}),e.jsx("input",{type:"password",placeholder:"Confirm new password",...r("confirmPassword"),className:"w-full h-[50px] bg-transparent text-white text-[16px] border border-gray-600 rounded px-3 py-2 focus:outline-none focus:border-[#4CAF50]"}),t.confirmPassword&&e.jsx("p",{className:"text-xs text-red-500 mt-1",children:t.confirmPassword.message})]}),e.jsxs("button",{type:"submit",disabled:N,className:"w-full h-[48px] bg-[#2E7D32] hover:bg-[#45a049] text-[#EAEAEA] gap-4 font-bold text-[16px] py-2 px-4 rounded flex items-center justify-center",children:[e.jsx("span",{children:"Reset Password"}),e.jsx(D,{className:"text-xl"})]})]})]})}),e.jsxs("div",{className:"mt-6 text-center text-[16px] text-[#B5B5B5] ml-[2rem]",children:["Remember your password?",e.jsx(M,{to:"/member/login",className:"text-[#4CAF50] ml-1 hover:underline",children:"Back to Login"})]})]})})]})})};export{fe as default};
