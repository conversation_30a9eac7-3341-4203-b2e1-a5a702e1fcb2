import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as i,d as k,f as E,r as P}from"./vendor-1c28ea83.js";import{u as A}from"./react-hook-form-eec8b32f.js";import{M as C,A as $,G as q,t as h,o as R,s as f}from"./index-b3edd152.js";import{c as T,a as D,g as F}from"./yup-1b5612ec.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let b=new C;const re=()=>{var d,u;const g=T({name:D().required(),status:F().required()}).required(),{dispatch:n}=i.useContext($),{dispatch:a}=i.useContext(q),y=k(),o=E(),[G,j]=P.useState(0),{register:l,handleSubmit:v,setError:w,setValue:c,formState:{errors:m}}=A({resolver:R(g)}),N=[{key:"0",value:"Inactive"},{key:"1",value:"Active"}],S=async e=>{try{console.log(e);const s=await b.updateStripePrice(o==null?void 0:o.id,e);if(!s.error)f(a,"Edited",4e3),y("/admin/prices");else if(s.validation){const p=Object.keys(s.validation);for(let r=0;r<p.length;r++){const x=p[r];w(x,{type:"manual",message:s.validation[x]})}}}catch(s){console.log("Error",s),f(a,s.message,4e3),h(n,s.message)}};return i.useEffect(()=>{a({type:"SETPATH",payload:{path:"prices"}}),async function(){try{const e=await b.getStripePrice(o==null?void 0:o.id);if(!e.error){const s=e.model.object;c("name",s.nickname),c("status",e.model.status),j(e.model.id)}}catch(e){console.log("Error",e),h(n,e.message)}}()},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Product"}),t.jsxs("form",{className:"w-full max-w-lg",onSubmit:v(S),children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"name",children:"Name"}),t.jsx("input",{type:"text",placeholder:"Name",...l("name"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(d=m.name)!=null&&d.message?"border-red-500":""}`}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(u=m.name)==null?void 0:u.message})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Status"}),t.jsx("select",{className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...l("status"),children:N.map(e=>t.jsx("option",{value:e.key,children:e.value},e.key))})]}),t.jsx("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{re as default};
