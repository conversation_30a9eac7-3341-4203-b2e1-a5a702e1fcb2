import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as d,d as y}from"./vendor-1c28ea83.js";import{u as E}from"./react-hook-form-eec8b32f.js";import{A as N,o as _,M as k,s as u}from"./index-b3edd152.js";import{c as v,a as l,b as D,d as B}from"./yup-1b5612ec.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const L=()=>e.jsx("svg",{width:"30",height:"24",viewBox:"0 0 30 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("g",{id:"Frame",children:e.jsx("path",{id:"Vector",d:"M15.1594 3.99375L10.6219 7.66875C9.86719 8.27812 9.72188 9.375 10.2938 10.1578C10.8984 10.9922 12.075 11.1562 12.8859 10.5234L17.5406 6.90469C17.8688 6.65156 18.3375 6.70781 18.5953 7.03594C18.8531 7.36406 18.7922 7.83281 18.4641 8.09062L17.4844 8.85L24 14.85V6H23.9672L23.7844 5.88281L20.3812 3.70312C19.6641 3.24375 18.825 3 17.9719 3C16.95 3 15.9562 3.35156 15.1594 3.99375ZM16.2281 9.825L13.8047 11.7094C12.3281 12.8625 10.1859 12.5625 9.07969 11.0437C8.03906 9.61406 8.30156 7.61719 9.675 6.50625L13.575 3.35156C13.0312 3.12187 12.4453 3.00469 11.85 3.00469C10.9688 3 10.1109 3.2625 9.375 3.75L6 6V16.5H7.32187L11.6063 20.4094C12.525 21.2484 13.9453 21.1828 14.7844 20.2641C15.0422 19.9781 15.2156 19.6453 15.3047 19.2984L16.1016 20.0297C17.0156 20.8687 18.4406 20.8078 19.2797 19.8937C19.4906 19.6641 19.6453 19.3969 19.7438 19.1203C20.6531 19.7297 21.8906 19.6031 22.6547 18.7687C23.4937 17.8547 23.4328 16.4297 22.5187 15.5906L16.2281 9.825ZM0.75 6C0.3375 6 0 6.3375 0 6.75V16.5C0 17.3297 0.670312 18 1.5 18H3C3.82969 18 4.5 17.3297 4.5 16.5V6H0.75ZM2.25 15C2.44891 15 2.63968 15.079 2.78033 15.2197C2.92098 15.3603 3 15.5511 3 15.75C3 15.9489 2.92098 16.1397 2.78033 16.2803C2.63968 16.421 2.44891 16.5 2.25 16.5C2.05109 16.5 1.86032 16.421 1.71967 16.2803C1.57902 16.1397 1.5 15.9489 1.5 15.75C1.5 15.5511 1.57902 15.3603 1.71967 15.2197C1.86032 15.079 2.05109 15 2.25 15ZM25.5 6V16.5C25.5 17.3297 26.1703 18 27 18H28.5C29.3297 18 30 17.3297 30 16.5V6.75C30 6.3375 29.6625 6 29.25 6H25.5ZM27 15.75C27 15.5511 27.079 15.3603 27.2197 15.2197C27.3603 15.079 27.5511 15 27.75 15C27.9489 15 28.1397 15.079 28.2803 15.2197C28.421 15.3603 28.5 15.5511 28.5 15.75C28.5 15.9489 28.421 16.1397 28.2803 16.2803C28.1397 16.421 27.9489 16.5 27.75 16.5C27.5511 16.5 27.3603 16.421 27.2197 16.2803C27.079 16.1397 27 15.9489 27 15.75Z",fill:"#7DD87D"})})}),te=()=>{const h=v({first_name:l().required("First name is required"),last_name:l().required("Last name is required"),email:l().email("Invalid email").required("Email is required"),password:l().min(8,"Password must be at least 8 characters").required("Password is required"),confirm_password:l().oneOf([D("password")],"Passwords must match").required("Please confirm your password"),user_type:B().min(1,"Please select your role").max(1,"Please select only one role").required("Please select your role"),industry:l().required("Please select your industry")}),[P,b]=d.useState(""),[x,m]=d.useState(!1),{dispatch:f}=d.useContext(N),p=new URLSearchParams(window.location.search),i=y(),{register:s,handleSubmit:g,setValue:w,watch:c,formState:{errors:t}}=E({resolver:_(h),defaultValues:{user_type:[]}}),n=r=>{w("user_type",[r],{shouldValidate:!0})},C=async r=>{console.log("Form data:",r);try{m(!0),b("");const o=new k,j=p.get("ref"),A=p.get("community"),a=await o.register({...r,referral_code:j,community_id:A});a.error||(a.token?(localStorage.setItem("token",a.token),localStorage.setItem("role","member"),f({type:"LOGIN",payload:{user:a.user_id,token:a.token,role:"member"}}),a.community_id?i(`/member/communities?join_id=${a.community_id}`):i("/member/dashboard"),u(globalDispatch,"Registration successful! Please login to continue.",5e3,"success")):i("/member/login",{state:{message:"Registration successful! Please login to continue."}}))}catch(o){console.error("Registration error:",o),u(globalDispatch,o.message||"Registration failed. Please try again.",5e3,"error")}finally{m(!1)}};return e.jsxs("div",{className:"w-full min-h-screen bg-[#1E1E1E] flex flex-col",children:[e.jsxs("header",{style:{marginBottom:"20px"},className:"flex justify-between px-[5vw]  bg-[#161616] h-[62px] items-center py-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(L,{}),e.jsx("span",{className:"text-[16px] font-bold text-[#EAEAEA]",children:"RainmakerOS"})]}),e.jsxs("div",{className:"flex items-center gap-[2rem]",children:[e.jsx("a",{href:"/member/login",className:"text-[16px] text-[#eaeaea] hover:text-[#7dd87d]",children:"Home"}),e.jsx("a",{href:"/member/signup",className:"text-[16px] text-[#eaeaea] hover:text-[#7dd87d]",children:"Sign Up"})]})]}),e.jsx("div",{className:"flex-1 bg-[#1E1E1E] flex flex-col items-center w-full",children:e.jsxs("div",{style:{width:"896px"},className:"space-y-4 ",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h2",{className:"text-[36px] font-bold text-[#EAEAEA]",children:"Join Rain Maker"}),e.jsx("p",{className:"mt-1 text-[18px] text-[#B5B5B5]",children:"Create your account to start connecting"})]}),e.jsxs("form",{onSubmit:g(C),className:"space-y-4 bg-black p-[2rem] mx-auto mt-[1rem]",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#EAEAEA] mb-1",children:"First Name"}),e.jsx("input",{type:"text",...s("first_name"),className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]",placeholder:"Enter your first name"}),t.first_name&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.first_name.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Last Name"}),e.jsx("input",{type:"text",...s("last_name"),className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]",placeholder:"Enter your last name"}),t.last_name&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.last_name.message})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Email"}),e.jsx("input",{type:"email",...s("email"),autoComplete:"off",className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]",placeholder:"Enter your email"}),t.email&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.email.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Password"}),e.jsx("input",{type:"password",...s("password"),autoComplete:"new-password",className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]",placeholder:"Create password"}),t.password&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.password.message})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-2",children:"I am:"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",...s("user_type"),value:"opportunity",onChange:r=>n("opportunity"),checked:c("user_type").includes("opportunity"),className:"h-4 w-4 rounded bg-white text-[#2e7d32] focus:ring-0 border-0"}),e.jsx("span",{className:"ml-2 text-[16px] text-[#ADAEBC]",children:"Looking for Opportunity"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",...s("user_type"),value:"referrals",onChange:r=>n("referrals"),checked:c("user_type").includes("referrals"),className:"h-4 w-4 rounded bg-white text-[#2e7d32] focus:ring-0 border-0"}),e.jsx("span",{className:"ml-2 text-[16px] text-[#ADAEBC]",children:"I Have Referrals for Others"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",...s("user_type"),value:"both",onChange:r=>n("both"),checked:c("user_type").includes("both"),className:"h-4 w-4 rounded bg-white text-[#2e7d32] focus:ring-0 border-0"}),e.jsx("span",{className:"ml-2 text-[16px] text-[#ADAEBC]",children:"I Am Looking for Both"})]})]}),t.user_type&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.user_type.message})]}),e.jsxs("div",{className:"flex flex-col space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Confirm Password"}),e.jsx("input",{type:"password",...s("confirm_password"),autoComplete:"new-password",className:"w-full h-[50px] rounded-[8px] mt-[0.5rem]border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]",placeholder:"Confirm password"}),t.confirm_password&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.confirm_password.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Industry"}),e.jsxs("select",{...s("industry"),className:"w-full h-[50px] rounded-[8px] mt-[0.5rem]border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none appearance-none",children:[e.jsx("option",{value:"",children:"Select your industry"}),e.jsx("option",{value:"technology",children:"Technology"}),e.jsx("option",{value:"finance",children:"Finance"}),e.jsx("option",{value:"healthcare",children:"Healthcare"}),e.jsx("option",{value:"retail",children:"Retail"})]}),t.industry&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.industry.message})]})]})]}),e.jsx("div",{className:"flex justify-center",children:e.jsx("button",{type:"submit",disabled:x,className:"w-[244px] h-[56px] rounded-[8px] mt-[2rem] bg-[#2e7d32] text-[16px] font-bold text-[#EAEAEA] hover:bg-[#266d2a] focus:outline-none disabled:opacity-50",children:x?"Creating Account...":"Create Account"})})]}),e.jsx("div",{className:"text-center ",children:e.jsxs("p",{className:"text-[16px] text-[#b5b5b5] mt-[1rem]",children:["Already have an account?"," ",e.jsx("a",{href:"/member/login",className:"text-[#7dd87d] hover:text-[#6bc76b]",children:"Login here"})]})})]})})]})};export{te as default};
