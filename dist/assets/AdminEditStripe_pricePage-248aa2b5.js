import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as m,d as S,f as E}from"./vendor-1c28ea83.js";import{u as I}from"./react-hook-form-eec8b32f.js";import{M as T,A,G as v,t as y,S as w,o as P,s as R}from"./index-b3edd152.js";import{c as C,a as i}from"./yup-1b5612ec.js";import{M as o}from"./MkdInput-67f7082d.js";import{I as U}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let p=new T;const me=d=>{const{dispatch:g}=m.useContext(A),b=C({create_at:i(),update_at:i(),name:i(),product_id:i(),stripe_id:i(),is_usage_metered:i(),usage_limit:i(),object:i(),amount:i(),trial_days:i(),type:i(),status:i()}).required(),{dispatch:c}=m.useContext(v),[x,u]=m.useState(!1),[f,n]=m.useState(!1),h=S(),{register:a,handleSubmit:j,setError:k,setValue:s,formState:{errors:r}}=I({resolver:P(b)}),l=E();m.useEffect(function(){(async function(){try{n(!0),p.setTable("stripe_price");const e=await p.callRestAPI({id:d.activeId?d.activeId:Number(l==null?void 0:l.id)},"GET");e.error||(s("create_at",e.model.create_at),s("update_at",e.model.update_at),s("name",e.model.name),s("product_id",e.model.product_id),s("stripe_id",e.model.stripe_id),s("is_usage_metered",e.model.is_usage_metered),s("usage_limit",e.model.usage_limit),s("object",e.model.object),s("amount",e.model.amount),s("trial_days",e.model.trial_days),s("type",e.model.type),s("status",e.model.status),n(!1))}catch(e){n(!1),console.log("error",e),y(g,e.message)}})()},[]);const N=async e=>{u(!0);try{p.setTable("stripe_price"),(await p.callRestAPI({id:d.activeId?d.activeId:Number(l==null?void 0:l.id),create_at:e.create_at,update_at:e.update_at,name:e.name,product_id:e.product_id,stripe_id:e.stripe_id,is_usage_metered:e.is_usage_metered,usage_limit:e.usage_limit,object:e.object,amount:e.amount,trial_days:e.trial_days,type:e.type,status:e.status},"PUT")).error||(R(c,"Updated"),h("/admin/stripe_price"),d.setSidebar(!1),c({type:"REFRESH_DATA",payload:{refreshData:!0}})),u(!1)}catch(_){u(!1),console.log("Error",_),y(g,_.message)}};return m.useEffect(()=>{c({type:"SETPATH",payload:{path:"stripe_price"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Stripe_price"}),f?t.jsx(w,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:j(N),children:[t.jsx(o,{type:"text",page:"edit",name:"create_at",errors:r,label:"Create_at",placeholder:"Create_at",register:a,className:""}),t.jsx(o,{type:"text",page:"edit",name:"update_at",errors:r,label:"Update_at",placeholder:"Update_at",register:a,className:""}),t.jsx(o,{type:"text",page:"edit",name:"name",errors:r,label:"Name",placeholder:"Name",register:a,className:""}),t.jsx(o,{type:"text",page:"edit",name:"product_id",errors:r,label:"Product_id",placeholder:"Product_id",register:a,className:""}),t.jsx(o,{type:"text",page:"edit",name:"stripe_id",errors:r,label:"Stripe_id",placeholder:"Stripe_id",register:a,className:""}),t.jsx(o,{type:"text",page:"edit",name:"is_usage_metered",errors:r,label:"Is_usage_metered",placeholder:"Is_usage_metered",register:a,className:""}),t.jsx(o,{type:"text",page:"edit",name:"usage_limit",errors:r,label:"Usage_limit",placeholder:"Usage_limit",register:a,className:""}),t.jsx(o,{type:"text",page:"edit",name:"object",errors:r,label:"Object",placeholder:"Object",register:a,className:""}),t.jsx(o,{type:"text",page:"edit",name:"amount",errors:r,label:"Amount",placeholder:"Amount",register:a,className:""}),t.jsx(o,{type:"text",page:"edit",name:"trial_days",errors:r,label:"Trial_days",placeholder:"Trial_days",register:a,className:""}),t.jsx(o,{type:"text",page:"edit",name:"type",errors:r,label:"Type",placeholder:"Type",register:a,className:""}),t.jsx(o,{type:"text",page:"edit",name:"status",errors:r,label:"Status",placeholder:"Status",register:a,className:""}),t.jsx(U,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{me as default};
