import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as g,r as f,d as P,f as R}from"./vendor-1c28ea83.js";import{u as C}from"./react-hook-form-eec8b32f.js";import{A,G as L,M as S,t as k,S as q,T as z,o as F,s as M}from"./index-b3edd152.js";import{c as O,a as o}from"./yup-1b5612ec.js";import{I as U}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const me=c=>{const{dispatch:y}=g.useContext(A),w=O({title:o().required(),type:o().required(),industry:o().required(),description:o().required(),deal_size:o().required(),referral_fee:o().required(),payment_method:o().required(),referral_type:o().required(),community:o(),direct_person:o(),additional_notes:o(),expiration_date:o()}).required(),{dispatch:u}=g.useContext(L),[v,h]=f.useState(!1),[E,b]=f.useState(!1),[_,j]=f.useState("");P();const{register:l,handleSubmit:T,setValue:s,formState:{errors:t}}=C({resolver:F(w)}),n=R();g.useEffect(function(){u({type:"SETPATH",payload:{path:"referral"}}),async function(){var r,a,m,p,d;try{b(!0);const x=c.activeId||Number(n==null?void 0:n.id),N=new S;N.setTable("referral");const i=await N.callRestAPI({id:x},"GET");if(!i.error&&(s("title",i.model.job_title||i.model.title),s("description",i.model.description),s("requirements",i.model.requirements||i.model.description),s("industry",(r=i.model.industry_id)==null?void 0:r.toString()),s("type",i.model.type),s("know_client",i.model.know_client==="yes"?"yes":"no"),s("deal_size",i.model.deal_size),s("referral_fee",i.model.referral_fee||i.model.pay),s("payment_method",i.model.payment_method),s("referral_type",(a=i.model.referral_type)!=null&&a.includes("open")?"open":"direct"),s("community",(m=i.model.community_id)==null?void 0:m.toString()),s("direct_person",((p=i.model.referred_to_id)==null?void 0:p.toString())||((d=i.model.user_id)==null?void 0:d.toString())),s("additional_notes",i.model.additional_notes||i.model.client_details),i.model.expiration_date)){const D=i.model.expiration_date.split(" ")[0];s("expiration_date",D)}b(!1)}catch(x){b(!1),console.log("Error",x),k(y,x.message)}}()},[]);const I=async r=>{h(!0),j("");try{const a=new S;a.setTable("referral");const m=new Date().toISOString().split("T")[0]+" 00:00:00",p={id:c.activeId||Number(n==null?void 0:n.id),user_id:r.direct_person?parseInt(r.direct_person,10):1,reposted_from:0,job_title:r.title,title:r.title,description:r.description,description_image:"",pay:r.referral_fee,referred_to_id:r.direct_person?parseInt(r.direct_person,10):0,industry_id:r.industry?parseInt(r.industry,10):null,is_active:1,type:r.type,client_details:r.additional_notes||"",completed_by:null,status:"active",expiration_date:r.expiration_date?new Date(r.expiration_date).toISOString().split("T")[0]+" 00:00:00":null,updated_at:m,deal_size:r.deal_size,payment_method:r.payment_method,referral_type:r.referral_type==="open"?"open referral":"direct referral",additional_notes:r.additional_notes||null,community_id:r.community?parseInt(r.community,10):null},d=await a.callRestAPI(p,"PUT");d.error?j(d.message||"Failed to update opportunity"):(M(u,"Opportunity Updated Successfully"),console.log("Dispatching REFRESH_DATA action to refresh the table"),u({type:"REFRESH_DATA",payload:{refreshData:!0,timestamp:new Date().getTime()}}),setTimeout(()=>{c.setSidebar&&c.setSidebar(!1)},100)),h(!1)}catch(a){h(!1),console.log("Error",a),j(a.message||"Failed to update opportunity"),k(y,a.message)}};return e.jsxs("div",{className:"w-full rounded-lg bg-[#1e1e1e] p-6",children:[e.jsx("style",{children:`
          input[type="date"]::-webkit-calendar-picker-indicator {
            filter: invert(1);
            cursor: pointer;
          }
          input[type="date"]::-webkit-calendar-picker-indicator:hover {
            filter: invert(1) brightness(1.2);
          }
        `}),e.jsx("h2",{className:"mb-6 text-xl font-bold text-[#eaeaea]",children:"Edit Opportunity"}),E?e.jsx(q,{}):e.jsxs("form",{onSubmit:T(I),className:"space-y-4  w-[60vw] mx-auto",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Title"}),e.jsx("input",{type:"text",...l("title"),placeholder:"Enter referral title",className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"}),t.title&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.title.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Type of Opportunity"}),e.jsxs("select",{...l("type"),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select type"}),e.jsx("option",{value:"looking_for_service",children:"Looking for Service"}),e.jsx("option",{value:"looking_for_product",children:"Looking for Product"}),e.jsx("option",{value:"looking_for_buyer",children:"Looking for Buyer"}),e.jsx("option",{value:"looking_for_investor",children:"Looking for Investor"}),e.jsx("option",{value:"looking_for_partner",children:"Looking for Partner"})]}),t.type&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.type.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Industry"}),e.jsxs("select",{...l("industry"),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select industry"}),e.jsx("option",{value:"1",children:"Agriculture & Farming"}),e.jsx("option",{value:"2",children:"Construction"}),e.jsx("option",{value:"3",children:"Education & Training"}),e.jsx("option",{value:"4",children:"Energy & Utilities"}),e.jsx("option",{value:"5",children:"Financial Services"}),e.jsx("option",{value:"6",children:"Government & Public Sector"}),e.jsx("option",{value:"7",children:"Healthcare & Life Sciences"}),e.jsx("option",{value:"8",children:"Hospitality & Tourism"}),e.jsx("option",{value:"9",children:"Information Technology & Software"}),e.jsx("option",{value:"10",children:"Legal Services"}),e.jsx("option",{value:"11",children:"Logistics & Transportation"}),e.jsx("option",{value:"12",children:"Manufacturing"}),e.jsx("option",{value:"13",children:"Marketing & Advertising"}),e.jsx("option",{value:"14",children:"Media & Entertainment"}),e.jsx("option",{value:"15",children:"Non-Profit & Charities"}),e.jsx("option",{value:"16",children:"Professional Services (e.g., consulting, accounting)"}),e.jsx("option",{value:"17",children:"Real Estate & Property Management"}),e.jsx("option",{value:"18",children:"Retail & E-Commerce"}),e.jsx("option",{value:"19",children:"Telecommunications"}),e.jsx("option",{value:"20",children:"Wholesale & Distribution"})]}),t.industry&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.industry.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Description"}),e.jsx("div",{className:"relative",children:e.jsx("textarea",{...l("description"),placeholder:"Write your text here...",rows:4,className:"mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea]"})}),t.description&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.description.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Estimated Deal Size"}),e.jsx("input",{type:"text",...l("deal_size"),placeholder:"e.g. $500",className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"}),t.deal_size&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.deal_size.message})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Commission Percentage (%)"}),e.jsx("input",{type:"text",...l("referral_fee"),placeholder:"Enter percentage (e.g. 10, 15)",className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"}),t.referral_fee&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.referral_fee.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Payment Method"}),e.jsxs("select",{...l("payment_method"),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select method"}),e.jsx("option",{value:"bank",children:"Bank Transfer"}),e.jsx("option",{value:"card",children:"Bank card"})]}),t.payment_method&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.payment_method.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Referral Type"}),e.jsxs("select",{...l("referral_type"),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select type"}),e.jsx("option",{value:"open",children:"Open Referral"}),e.jsx("option",{value:"direct",children:"Direct Referral"})]}),t.referral_type&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.referral_type.message})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Select Community"}),e.jsxs("select",{...l("community"),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select community"}),e.jsx("option",{value:"1",children:"Community 1"}),e.jsx("option",{value:"2",children:"Community 2"}),e.jsx("option",{value:"3",children:"Community 3"})]}),t.community&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.community.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Select Direct Person"}),e.jsxs("select",{...l("direct_person"),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select person"}),e.jsx("option",{value:"1",children:"User 1"}),e.jsx("option",{value:"2",children:"User 2"}),e.jsx("option",{value:"3",children:"User 3"})]}),t.direct_person&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.direct_person.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Additional Notes"}),e.jsx("textarea",{...l("additional_notes"),placeholder:"Write any additional notes here...",rows:4,className:"mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea]"}),t.additional_notes&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.additional_notes.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Expiration Date"}),e.jsx("input",{type:"date",...l("expiration_date"),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"}),e.jsx("p",{className:"mt-1 text-xs text-[#b5b5b5]",children:"If not set, the existing expiration date will be kept"}),t.expiration_date&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.expiration_date.message})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{type:"button",onClick:()=>{window.location.reload()},className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea]",children:"Close"}),e.jsx(U,{type:"submit",loading:v,disabled:v,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Update Opportunity"})]})]}),_&&e.jsx(z,{message:_})]})};export{me as default};
