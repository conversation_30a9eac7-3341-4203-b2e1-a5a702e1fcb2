import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as r,d as h}from"./vendor-1c28ea83.js";import{u as y}from"./react-hook-form-eec8b32f.js";import{G as b,A as g,o as j,M as A,s as S,t as v}from"./index-b3edd152.js";import{c as E,a as d}from"./yup-1b5612ec.js";import{M as n}from"./MkdInput-67f7082d.js";import{I as q}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const re=({setSidebar:u})=>{const{dispatch:o}=r.useContext(b),l=E({user_id:d().required(),approver_id:d().required()}).required(),{dispatch:c}=r.useContext(g),[a,s]=r.useState(!1),f=h(),{register:i,handleSubmit:x,setError:w,formState:{errors:m}}=y({resolver:j(l)}),_=async p=>{s(!0);try{let t=new A;t.setTable("community_join_request"),(await t.callRestAPI({user_id:p.user_id,approver_id:p.approver_id},"POST")).error||(S(o,"Added"),f("/admin/community_join_request"),u(!1),o({type:"REFRESH_DATA",payload:{refreshData:!0}})),s(!1)}catch(t){s(!1),console.log("Error",t),v(c,t.message)}};return r.useEffect(()=>{o({type:"SETPATH",payload:{path:"community_join_request"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Community_join_request"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:x(_),children:[e.jsx(n,{type:"text",page:"add",name:"user_id",errors:m,label:"User_id",placeholder:"User_id",register:i,className:""}),e.jsx(n,{type:"text",page:"add",name:"approver_id",errors:m,label:"Approver_id",placeholder:"Approver_id",register:i,className:""}),e.jsx(q,{type:"submit",loading:a,disabled:a,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{re as default};
