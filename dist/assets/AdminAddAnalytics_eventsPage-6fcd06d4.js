import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as s,d as h}from"./vendor-1c28ea83.js";import{u as b}from"./react-hook-form-eec8b32f.js";import{G as g,A,o as S,M as v,s as _,t as j}from"./index-b3edd152.js";import{c as E,a as m}from"./yup-1b5612ec.js";import{M as d}from"./MkdInput-67f7082d.js";import{I as w}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const se=({setSidebar:n})=>{const{dispatch:a}=s.useContext(g),c=E({details:m().required(),user_id:m().required(),type:m().required()}).required(),{dispatch:u}=s.useContext(A),[p,r]=s.useState(!1),x=h(),{register:o,handleSubmit:f,setError:N,formState:{errors:i}}=b({resolver:S(c)}),y=async l=>{r(!0);try{let t=new v;t.setTable("analytics_events"),(await t.callRestAPI({details:l.details,user_id:l.user_id,type:l.type},"POST")).error||(_(a,"Added"),x("/admin/analytics_events"),n(!1),a({type:"REFRESH_DATA",payload:{refreshData:!0}})),r(!1)}catch(t){r(!1),console.log("Error",t),j(u,t.message)}};return s.useEffect(()=>{a({type:"SETPATH",payload:{path:"analytics_events"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Analytics_events"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:f(y),children:[e.jsx(d,{type:"text",page:"add",name:"details",errors:i,label:"Details",placeholder:"Details",register:o,className:""}),e.jsx(d,{type:"text",page:"add",name:"user_id",errors:i,label:"User_id",placeholder:"User_id",register:o,className:""}),e.jsx(d,{type:"text",page:"add",name:"type",errors:i,label:"Type",placeholder:"Type",register:o,className:""}),e.jsx(w,{type:"submit",loading:p,disabled:p,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{se as default};
