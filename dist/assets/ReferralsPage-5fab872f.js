import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as o,d as xe,u as we,L as Ce}from"./vendor-1c28ea83.js";import{T as ue,M as D,G as le,S as _e,s as M}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const Re=()=>e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM10 18C5.58 18 2 14.42 2 10C2 5.58 5.58 2 10 2C14.42 2 18 5.58 18 10C18 14.42 14.42 18 10 18ZM7 6C7 6.82843 6.32843 7.5 5.5 7.5C4.67157 7.5 4 6.82843 4 6C4 5.17157 4.67157 4.5 5.5 4.5C6.32843 4.5 7 5.17157 7 6ZM16 6C16 6.82843 15.3284 7.5 14.5 7.5C13.6716 7.5 13 6.82843 13 6C13 5.17157 13.6716 4.5 14.5 4.5C15.3284 4.5 16 5.17157 16 6ZM10 15.5C12.33 15.5 14.32 14.05 15.12 12H4.88C5.68 14.05 7.67 15.5 10 15.5Z",fill:"#B5B5B5"})}),Le=()=>e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M17.5 9.375L11.25 15.625C10.2083 16.6667 8.89583 17.1875 7.3125 17.1875C5.72917 17.1875 4.41667 16.6667 3.375 15.625C2.33333 14.5833 1.8125 13.2708 1.8125 11.6875C1.8125 10.1042 2.33333 8.79167 3.375 7.75L10.9375 0.1875C11.6458 -0.520833 12.5104 -0.875 13.5312 -0.875C14.5521 -0.875 15.4167 -0.520833 16.125 0.1875C16.8333 0.895833 17.1875 1.76042 17.1875 2.78125C17.1875 3.80208 16.8333 4.66667 16.125 5.375L8.5625 12.9375C8.20833 13.2917 7.78125 13.4688 7.28125 13.4688C6.78125 13.4688 6.35417 13.2917 6 12.9375C5.64583 12.5833 5.46875 12.1562 5.46875 11.6562C5.46875 11.1562 5.64583 10.7292 6 10.375L12.8125 3.5625",stroke:"#B5B5B5","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),Se=({isOpen:m,onClose:v,referral:t=null})=>{var n,a,l,d,F,C,$,I,z,O,H,P;const[p,g]=o.useState(""),[N,y]=o.useState(!1),[i,r]=o.useState([]),[f,x]=o.useState({title:((n=t==null?void 0:t.title)==null?void 0:n.value)||"",type:((a=t==null?void 0:t.type)==null?void 0:a.value)||"",industry:((l=t==null?void 0:t.industry)==null?void 0:l.value)||"",description:((d=t==null?void 0:t.description)==null?void 0:d.value)||"",know_client:((F=t==null?void 0:t.know_client)==null?void 0:F.value)||"",deal_size:((C=t==null?void 0:t.deal_size)==null?void 0:C.value)||"",referral_fee:(($=t==null?void 0:t.referral_fee)==null?void 0:$.value)||"",payment_method:((I=t==null?void 0:t.payment_method)==null?void 0:I.value)||"",referral_type:((z=t==null?void 0:t.referral_type)==null?void 0:z.value)||"",community:((O=t==null?void 0:t.community)==null?void 0:O.value)||"",direct_person:((H=t==null?void 0:t.direct_person)==null?void 0:H.value)||"",additional_notes:((P=t==null?void 0:t.additional_notes)==null?void 0:P.value)||""});if(o.useEffect(()=>{m&&(async()=>{try{const B=await new D().callRawAPI("/v1/api/dealmaker/industries",{},"GET");!B.error&&B.data&&r(B.data)}catch(h){console.error("Failed to load industries:",h)}})()},[m]),!m)return null;const L=async u=>{u.preventDefault(),y(!0),g("");try{const h=new D,B={...f,industry_id:f.industry?parseInt(f.industry,10):null,industry:void 0},W=t?await h.EditReferral({...B,id:t.id.value}):await h.CreateReferral(B);W.error?g(W.message):v(!0)}catch(h){g(h.message||"Failed to save referral")}finally{y(!1)}};return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50",children:e.jsxs("div",{className:"w-full max-w-2xl rounded-lg bg-[#1e1e1e] p-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-bold text-[#eaeaea]",children:t?"Edit Opportunity":"Add Opportunity"}),e.jsxs("form",{onSubmit:L,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Title"}),e.jsx("input",{type:"text",value:f.title,onChange:u=>x(h=>({...h,title:u.target.value})),placeholder:"Enter referral title",className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Type of Opportunity"}),e.jsxs("select",{value:f.type,onChange:u=>x(h=>({...h,type:u.target.value})),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select type"}),e.jsx("option",{value:"looking_for_service",children:"Looking for Service"}),e.jsx("option",{value:"looking_for_product",children:"Looking for Product"}),e.jsx("option",{value:"looking_for_buyer",children:"Looking for Buyer"}),e.jsx("option",{value:"looking_for_investor",children:"Looking for Investor"}),e.jsx("option",{value:"looking_for_partner",children:"Looking for Partner"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Industry"}),e.jsxs("select",{value:f.industry,onChange:u=>x(h=>({...h,industry:u.target.value})),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select industry"}),i.map(u=>e.jsx("option",{value:u.id,children:u.name},u.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Description"}),e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"absolute left-4 top-4 flex items-center gap-2",children:[e.jsx("button",{type:"button",className:"text-[#b5b5b5] hover:text-[#eaeaea]",onClick:()=>{},children:e.jsx(Re,{})}),e.jsx("button",{type:"button",className:"text-[#b5b5b5] hover:text-[#eaeaea]",onClick:()=>{},children:e.jsx(Le,{})})]}),e.jsx("textarea",{value:f.description,onChange:u=>x(h=>({...h,description:u.target.value})),placeholder:"Write your text here...",rows:4,className:"mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] p-4 pl-24 text-[#eaeaea]"})]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Estimated Deal Size"}),e.jsx("input",{type:"text",value:f.deal_size,onChange:u=>x(h=>({...h,deal_size:u.target.value})),placeholder:"e.g. $500",className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Referral Fee (%)"}),e.jsx("input",{type:"text",value:f.referral_fee,onChange:u=>x(h=>({...h,referral_fee:u.target.value})),placeholder:"Enter percentage",className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Payment Method"}),e.jsxs("select",{value:f.payment_method,onChange:u=>x(h=>({...h,payment_method:u.target.value})),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select method"}),e.jsx("option",{value:"bank",children:"Bank Transfer"}),e.jsx("option",{value:"bank",children:"Bank card"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Referral Type"}),e.jsxs("select",{value:f.referral_type,onChange:u=>x(h=>({...h,referral_type:u.target.value})),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select type"}),e.jsx("option",{value:"open",children:"Open Referral"}),e.jsx("option",{value:"direct",children:"Direct Referral"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Select Community"}),e.jsx("select",{value:f.community,onChange:u=>x(h=>({...h,community:u.target.value})),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",children:e.jsx("option",{value:"",children:"Select community"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Select Direct Person"}),e.jsx("select",{value:f.direct_person,onChange:u=>x(h=>({...h,direct_person:u.target.value})),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",children:e.jsx("option",{value:"",children:"Select person"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Additional Notes"}),e.jsx("textarea",{value:f.additional_notes,onChange:u=>x(h=>({...h,additional_notes:u.target.value})),placeholder:"Write any additional notes here...",rows:4,className:"mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea]"})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{type:"button",onClick:v,className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:N,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:N?"Saving...":"Submit Referral"})]})]}),p&&e.jsx(ue,{message:p})]})})},Me=({isOpen:m,onClose:v,onConfirm:t,title:p,message:g})=>m?e.jsxs("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:v}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-lg bg-[#1e1e1e] p-6 shadow-xl",children:[e.jsx("h3",{className:"mb-4 text-lg font-medium text-[#eaeaea]",children:p}),e.jsx("p",{className:"mb-6 text-sm text-[#b5b5b5]",children:g}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:v,className:"rounded px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{onClick:t,className:"rounded bg-red-600 px-4 py-2 text-sm text-white hover:bg-red-700",children:"Delete"})]})]})]}):null,G=m=>{const v=new Date(m),t=v.getDate(),p=v.toLocaleString("default",{month:"short"}),g=v.getFullYear();return`${t} ${p}, ${g}`},De=()=>e.jsx("svg",{width:"11",height:"12",viewBox:"0 0 15 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("g",{clipPath:"url(#clip0_3_1374)",children:e.jsx("path",{d:"M11.8253 1.39354C12.0097 0.965419 11.8722 0.465419 11.4941 0.190419C11.1159 -0.0845806 10.6003 -0.0595806 10.2472 0.246669L2.24719 7.24667C1.93469 7.52167 1.82219 7.96229 1.96907 8.34979C2.11594 8.73729 2.49094 8.99979 2.90657 8.99979H6.39094L3.98782 14.606C3.80344 15.0342 3.94094 15.5342 4.31907 15.8092C4.69719 16.0842 5.21282 16.0592 5.56594 15.7529L13.5659 8.75292C13.8784 8.47792 13.9909 8.03729 13.8441 7.64979C13.6972 7.26229 13.3253 7.00292 12.9066 7.00292H9.42219L11.8253 1.39354Z",fill:"#7DD87D"})})}),ae=()=>e.jsx("svg",{width:"13",height:"12",viewBox:"0 0 17 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("g",{clipPath:"url(#clip0_3_1393)",children:e.jsx("path",{d:"M11.5 8C11.5 8.69375 11.4625 9.3625 11.3969 10H5.60313C5.53438 9.3625 5.5 8.69375 5.5 8C5.5 7.30625 5.5375 6.6375 5.60313 6H11.3969C11.4656 6.6375 11.5 7.30625 11.5 8ZM12.4 6H16.2469C16.4125 6.64062 16.5 7.30937 16.5 8C16.5 8.69063 16.4125 9.35938 16.2469 10H12.4C12.4656 9.35625 12.5 8.6875 12.5 8C12.5 7.3125 12.4656 6.64375 12.4 6ZM15.9187 5H12.2719C11.9594 3.00312 11.3406 1.33125 10.5437 0.2625C12.9906 0.909375 14.9812 2.68438 15.9156 5H15.9187ZM11.2594 5H5.74062C5.93125 3.8625 6.225 2.85625 6.58437 2.04063C6.9125 1.30313 7.27812 0.76875 7.63125 0.43125C7.98125 0.1 8.27187 0 8.5 0C8.72812 0 9.01875 0.1 9.36875 0.43125C9.72187 0.76875 10.0875 1.30313 10.4156 2.04063C10.7781 2.85313 11.0687 3.85938 11.2594 5ZM4.72813 5H1.08125C2.01875 2.68438 4.00625 0.909375 6.45625 0.2625C5.65938 1.33125 5.04063 3.00312 4.72813 5ZM0.753125 6H4.6C4.53437 6.64375 4.5 7.3125 4.5 8C4.5 8.6875 4.53437 9.35625 4.6 10H0.753125C0.5875 9.35938 0.5 8.69063 0.5 8C0.5 7.30937 0.5875 6.64062 0.753125 6ZM6.58437 13.9563C6.22187 13.1438 5.93125 12.1375 5.74062 11H11.2594C11.0687 12.1375 10.775 13.1438 10.4156 13.9563C10.0875 14.6938 9.72187 15.2281 9.36875 15.5656C9.01875 15.9 8.72812 16 8.5 16C8.27187 16 7.98125 15.9 7.63125 15.5688C7.27812 15.2313 6.9125 14.6969 6.58437 13.9594V13.9563ZM4.72813 11C5.04063 12.9969 5.65938 14.6687 6.45625 15.7375C4.00625 15.0906 2.01875 13.3156 1.08125 11H4.72813ZM15.9187 11C14.9812 13.3156 12.9937 15.0906 10.5469 15.7375C11.3438 14.6687 11.9594 12.9969 12.275 11H15.9187Z",fill:"#7DD87D"})})}),Te=()=>e.jsx("svg",{width:"15",height:"8",viewBox:"0 0 19 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.99998 10.9999C9.5531 10.9999 9.99998 10.553 9.99998 9.9999C9.99998 9.44678 9.5531 8.9999 8.99998 8.9999H5.49998C4.94685 8.9999 4.49998 8.55303 4.49998 7.9999V3.9999H5.49998C5.9031 3.9999 6.26873 3.75615 6.42498 3.38115C6.58123 3.00615 6.49373 2.57803 6.20935 2.29053L4.20935 0.290527C3.81873 -0.100098 3.18435 -0.100098 2.79373 0.290527L0.793727 2.29053C0.506227 2.57803 0.421852 3.00615 0.578102 3.38115C0.734352 3.75615 1.09685 3.9999 1.5031 3.9999H2.5031V7.9999C2.5031 9.65615 3.84685 10.9999 5.5031 10.9999H8.99998ZM9.99998 0.999902C9.44685 0.999902 8.99998 1.44678 8.99998 1.9999C8.99998 2.55303 9.44685 2.9999 9.99998 2.9999H13.5C14.0531 2.9999 14.5 3.44678 14.5 3.9999V7.9999H13.5C13.0969 7.9999 12.7312 8.24365 12.575 8.61865C12.4187 8.99365 12.5062 9.42178 12.7906 9.70928L14.7906 11.7093C15.1812 12.0999 15.8156 12.0999 16.2062 11.7093L18.2062 9.70928C18.4937 9.42178 18.5781 8.99365 18.4219 8.61865C18.2656 8.24365 17.9031 7.9999 17.4969 7.9999H16.4969V3.9999C16.4969 2.34365 15.1531 0.999902 13.4969 0.999902H9.99998Z",fill:"#EAEAEA"})}),Ae=m=>m.split(" ").map(v=>v[0]).join("").toUpperCase().slice(0,2),de=({user:m})=>{var v;return(v=m.photo)!=null&&v.value?e.jsx("img",{src:m.photo.value,alt:m.name.value,className:"object-cover w-8 h-8 rounded-full"}):e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white",children:Ae(m.name.value)})},Ee=({referralId:m,onClose:v})=>{var L;const{dispatch:t}=o.useContext(le);xe();const[p,g]=o.useState(!1),[N,y]=o.useState(""),[i,r]=o.useState({first_name:{value:""},last_name:{value:""},email:{value:""},online_accounts:{value:[]},recommendation:{value:[{first_name:"",last_name:"",email:"",user_id:"12"},{first_name:"",last_name:"",email:"",user_id:"12"},{first_name:"",last_name:"",email:"",user_id:""},{first_name:"",last_name:"",email:"",user_id:""},{first_name:"",last_name:"",email:"",user_id:""}]}});o.useEffect(()=>{(async()=>{try{const l=await new D().callRawAPI("/v1/api/dealmaker/user/details",{},"GET");!l.error&&l.model&&r(d=>({...d,first_name:{value:l.model.first_name.value||""},last_name:{value:l.model.last_name.value||""},email:{value:l.model.email.value||""}}))}catch(a){console.error("Failed to fetch user details:",a),y("Failed to fetch user details")}})()},[]),console.log("formData",i);const f=()=>{r(n=>({...n,online_accounts:{value:[...n.online_accounts.value,{platform:"",username:"",url:""}]}}))},x=async n=>{n.preventDefault(),g(!0),y("");const a=i.recommendation.value.filter(l=>l.first_name&&l.last_name&&l.email);if(a.length===0){y("Please fill in at least one recommendation"),g(!1);return}try{const l=new D,d={first_name:{value:i.first_name.value},last_name:{value:i.last_name.value},email:{value:i.email.value},online_accounts:{value:i.online_accounts.value.map(C=>({platform:{value:C.platform},username:{value:C.username},url:{value:C.url},user_id:{value:C.user_id||""}}))},recommendation:{value:a.map(C=>({first_name:{value:C.first_name},last_name:{value:C.last_name},email:{value:C.email},user_id:{value:C.user_id||""}}))}};console.log("Submitting payload:",d);const F=await l.callRawAPI(`/v1/api/dealmaker/user/referral/${m}/recommend`,d,"POST");F.error?y(F.message):(M(t,"Referral recommendation submitted successfully!",5e3,"success"),v())}catch(l){y(l.message||"Failed to submit referral recommendation")}finally{g(!1)}};return e.jsx("div",{style:{width:"700px",backgroundColor:"#161616",border:"1px solid #363636",margin:"auto",borderRadius:"10px",padding:"20px"},className:"mb-6 rounded-lg border border-[#363636] bg-[#1e1e1e] p-6",children:e.jsxs("form",{onSubmit:x,className:"space-y-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:"Your Name *"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name"}),e.jsx("input",{type:"text",value:i.first_name.value,onChange:n=>r(a=>({...a,first_name:{value:n.target.value}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name"}),e.jsx("input",{type:"text",value:i.last_name.value,onChange:n=>r(a=>({...a,last_name:{value:n.target.value}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Email *"}),e.jsx("input",{type:"email",value:i.email.value,onChange:n=>r(a=>({...a,email:{value:n.target.value}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Add your website or social links"}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("input",{type:"url",value:((L=i.online_accounts.value[0])==null?void 0:L.url)||"",onChange:n=>r(a=>({...a,online_accounts:{value:[{platform:"",username:"",url:n.target.value,user_id:""},...a.online_accounts.value.slice(1)]}})),placeholder:"https://",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"}),e.jsx("button",{type:"button",onClick:f,className:"flex h-10 items-center justify-center rounded-lg border border-[#363636] bg-[#161616] px-4 text-sm text-[#eaeaea] hover:bg-[#242424]",children:"+ Add More"})]}),i.online_accounts.value.slice(1).map((n,a)=>e.jsx("div",{className:"mt-2",children:e.jsx("input",{type:"url",value:n.url,onChange:l=>{const d=[...i.online_accounts.value];d[a+1]={platform:"",username:"",url:l.target.value,user_id:""},r(F=>({...F,online_accounts:{value:d}}))},placeholder:"https://",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})},a))]})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:"Recommendation #1 *"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name"}),e.jsx("input",{type:"text",value:i.recommendation.value[0].first_name,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===0?{...l,first_name:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name"}),e.jsx("input",{type:"text",value:i.recommendation.value[0].last_name,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===0?{...l,last_name:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Email"}),e.jsx("input",{type:"email",value:i.recommendation.value[0].email,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===0?{...l,email:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:"Recommendation #2"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name"}),e.jsx("input",{type:"text",value:i.recommendation.value[1].first_name,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===1?{...l,first_name:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name"}),e.jsx("input",{type:"text",value:i.recommendation.value[1].last_name,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===1?{...l,last_name:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Recommendation #2 Email"}),e.jsx("input",{type:"email",value:i.recommendation.value[1].email,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===1?{...l,email:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:"Recommendation #3"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name"}),e.jsx("input",{type:"text",value:i.recommendation.value[2].first_name,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===2?{...l,first_name:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name"}),e.jsx("input",{type:"text",value:i.recommendation.value[2].last_name,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===2?{...l,last_name:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Recommendation #3 Email"}),e.jsx("input",{type:"email",value:i.recommendation.value[2].email,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===2?{...l,email:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:"Recommendation #4"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name"}),e.jsx("input",{type:"text",value:i.recommendation.value[3].first_name,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===3?{...l,first_name:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name"}),e.jsx("input",{type:"text",value:i.recommendation.value[3].last_name,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===3?{...l,last_name:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Recommendation #4 Email"}),e.jsx("input",{type:"email",value:i.recommendation.value[3].email,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===3?{...l,email:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:"Recommendation #5"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name"}),e.jsx("input",{type:"text",value:i.recommendation.value[4].first_name,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===4?{...l,first_name:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name"}),e.jsx("input",{type:"text",value:i.recommendation.value[4].last_name,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===4?{...l,last_name:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Recommendation #5 Email"}),e.jsx("input",{type:"email",value:i.recommendation.value[4].email,onChange:n=>r(a=>({...a,recommendation:{value:a.recommendation.value.map((l,d)=>d===4?{...l,email:n.target.value}:l)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]})]}),e.jsxs("div",{style:{padding:"20px"},className:"mt-6 rounded-lg bg-[#242424] text-sm text-[#7dd87d]",children:[e.jsx("p",{className:"mb-2 font-medium",children:"Please note:"}),e.jsx("p",{className:"text-xs text-white",children:"RainmakerOS is not for everyone. This is a vetted premium network designed to connect you with givers, matchers and other business rainmakers."})]}),N&&e.jsx("p",{className:"text-sm text-red-500",children:N}),e.jsxs("div",{className:"flex justify-center gap-4 mt-6",children:[e.jsx("button",{type:"button",onClick:v,className:"rounded-lg bg-[#363636] px-6 py-3 text-sm text-white hover:bg-[#404040]",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:p,className:"rounded-lg bg-[#2e7d32] px-6 py-3 text-sm text-white hover:bg-[#1b5e20] disabled:opacity-50",children:"Submit Referral"})]})]})})},Fe=({isOpen:m,onClose:v,recommendations:t})=>(console.log(m,t,onclose),m?e.jsxs("div",{className:"flex fixed inset-0 z-50 justify-center items-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:v}),e.jsxs("div",{className:"relative z-50 w-1/2 max-w-3xl rounded-lg bg-[#161616] p-6 shadow-xl",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-[#eaeaea]",children:"Recommendations"}),e.jsx("button",{onClick:v,className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:e.jsx("svg",{className:"w-5 h-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),e.jsx("div",{className:"space-y-4",children:t.map((p,g)=>e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:[e.jsx("div",{className:"flex justify-between items-center mb-4",children:e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx(de,{user:p.user}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:["Recommended on"," ",new Date(p.created_at.value).toLocaleDateString()]})})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Candidate Details"}),e.jsx("div",{className:"space-y-2 text-sm",children:e.jsx("p",{className:"text-[#b5b5b5]",children:e.jsx("span",{className:"text-[#eaeaea]",children:p.recommendation.value.map((N,y)=>e.jsxs("div",{className:"mb-4",children:[e.jsxs("p",{className:"text-[#eaeaea]",children:["Name:"," "," ",N.first_name.value," ",N.last_name.value]}),e.jsxs("p",{className:"text-[#b5b5b5]",children:["Email: ",e.jsx("span",{className:"text-[#eaeaea]",children:N.email.value})]})]},y))})})})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Recommended By"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("p",{className:"text-[#b5b5b5]",children:["Name:"," ",e.jsxs("span",{className:"text-[#eaeaea]",children:[p.candidate.first_name.value," ",p.candidate.last_name.value]})]}),e.jsxs("p",{className:"text-[#b5b5b5]",children:["Email:"," ",e.jsx("span",{className:"text-[#eaeaea]",children:p.candidate.email.value})]}),p.candidate.online_accounts.value.length>0&&e.jsxs("div",{children:[e.jsx("p",{className:"text-[#b5b5b5]",children:"Online Accounts:"}),e.jsx("ul",{className:"ml-4 list-disc",children:p.candidate.online_accounts.value.map((N,y)=>{var i,r;return e.jsx("li",{children:e.jsx("a",{href:(i=N==null?void 0:N.url)==null?void 0:i.value,target:"_blank",rel:"noopener noreferrer",className:"text-[#7dd87d] hover:underline",children:(r=N==null?void 0:N.url)==null?void 0:r.value})},y)})})]})]})]})]})]},p.id.value))})]})]}):null),Be=({isOpen:m,onClose:v,referralId:t,communities:p,referrals:g})=>{if(!m)return null;const{dispatch:N}=o.useContext(le),[y,i]=o.useState(""),[r,f]=o.useState(!1),x=g.find(n=>n.id.value===t),L=async()=>{if(y){f(!0);try{await new D().RepostReferral({referral_id:t,community_id:y}),M(N,"Referral reposted successfully!",5e3,"success"),v(!0)}catch(n){M(N,n.message||"Failed to repost referral",5e3,"error")}finally{f(!1)}}};return e.jsxs("div",{className:"flex fixed inset-0 z-50 justify-center items-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:()=>!r&&v()}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-lg bg-[#161616] p-6 shadow-xl",children:[x&&e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex gap-3 items-center mb-4",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-[#242424] text-[#eaeaea]",children:x.creator.name.value.charAt(0)}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-[#eaeaea]",children:x.title.value}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[x.creator.name.value," -"," ",G(x.created_at.value)]})]})]})}),e.jsx("h3",{className:"mb-4 text-lg font-medium text-[#eaeaea]",children:"Select Community you want to Repost this Referral in."}),e.jsxs("select",{value:y,onChange:n=>i(n.target.value),className:"mb-6 h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none",required:!0,children:[e.jsx("option",{value:"",children:"Select a community"}),p.map(n=>e.jsx("option",{value:n.id.value,children:n.title.value},n.id.value))]}),e.jsxs("div",{className:"flex gap-4 justify-end",children:[e.jsx("button",{onClick:()=>!r&&v(),disabled:r,className:"rounded-lg border border-[#363636] px-6 py-2 text-sm text-[#eaeaea] hover:bg-[#242424] disabled:opacity-50",children:"Cancel"}),e.jsx("button",{onClick:L,disabled:r||!y,className:"rounded-lg bg-[#2e7d32] px-6 py-2 text-sm text-white hover:bg-[#1b5e20] disabled:opacity-50",children:"Repost"})]})]})]})},he=({text:m="",maxWords:v=100})=>{const[t,p]=o.useState(!1);if(!m)return e.jsx("p",{className:"whitespace-pre-line text-[#b5b5b5]",children:"No content available"});const g=m.split(/\s+/),N=g.length>v,y=t?m:g.slice(0,v).join(" ")+(N?"...":"");return e.jsx("div",{children:e.jsxs("p",{className:"whitespace-pre-line text-[#b5b5b5]",children:[y,N&&e.jsx("button",{onClick:()=>p(!t),className:"ml-2 text-[#7dd87d] hover:underline",children:t?"Read Less":"Read More"})]})})},ze=({isOpen:m,onClose:v,referral:t})=>{var u,h,B,W,J,Y,Q,K,X,ee;const[p,g]=o.useState("details"),[N,y]=o.useState([]),[i,r]=o.useState([]),[f,x]=o.useState(!1),[L,n]=o.useState(!1),[a,l]=o.useState(""),[d,F]=o.useState({content:"",due_date:""}),{dispatch:C}=o.useContext(le);o.useEffect(()=>{m&&t&&($(),I())},[m,t]);const $=async()=>{try{const _=await new D().callRawAPI(`/v1/api/dealmaker/user/notes?referral_id=${t.id.value}`,{},"GET");if(!_.error){const S=(_.list||[]).sort((T,A)=>new Date(A.created_at)-new Date(T.created_at));y(S)}}catch(c){console.error("Failed to load notes:",c)}},I=async()=>{try{const _=await new D().callRawAPI(`/v1/api/dealmaker/user/tasks?referral_id=${t.id.value}`,{},"GET");if(!_.error){const S=(_.list||[]).sort((T,A)=>new Date(T.due_date)-new Date(A.due_date));r(S)}}catch(c){console.error("Failed to load tasks:",c)}},z=async()=>{var c,_;if(!(!d.content.trim()||!d.due_date))try{const T=await new D().callRawAPI("/v1/api/dealmaker/user/tasks",{content:{value:d.content},due_date:{value:d.due_date},referral_id:{value:(c=t==null?void 0:t.id)==null?void 0:c.value}},"POST");if(T.error)throw new Error(T.message||"Failed to add task");{const A={id:((_=T.model)==null?void 0:_.id)||Date.now(),description:d.content,due_date:d.due_date,created_at:new Date().toISOString(),title:`Task added on ${new Date().toLocaleDateString()}`};r(V=>[...V,A]),F({content:"",due_date:""}),n(!1),I(),M(C,"Task added successfully!",5e3,"success")}}catch(S){console.error("Error adding task:",S),M(C,S.message||"Failed to add task",5e3,"error")}},O=async c=>{try{const S=await new D().callRawAPI(`/v1/api/dealmaker/user/tasks/${c}`,{},"DELETE");if(!S.error)r(T=>T.filter(A=>A.id!==c)),M(C,"Task deleted successfully!",5e3,"success");else throw new Error(S.message||"Failed to delete task")}catch(_){console.error("Error deleting task:",_),M(C,_.message||"Failed to delete task",5e3,"error")}},H=async()=>{var c,_,S,T,A;if(a.trim())try{const Z=await new D().callRawAPI("/v1/api/dealmaker/user/notes",{content:{value:a},referral_id:{value:(c=t==null?void 0:t.id)==null?void 0:c.value}},"POST");if(Z.error)throw new Error(Z.message||"Failed to add note");{const ne=new Date().toISOString(),te={id:((S=(_=Z.model)==null?void 0:_.id)==null?void 0:S.value)||Date.now(),description:a,created_at:((A=(T=Z.model)==null?void 0:T.created_at)==null?void 0:A.value)||ne};y(se=>[te,...se]),l(""),x(!1),$(),M(C,"Note added successfully!",5e3,"success")}}catch(V){console.error("Error adding note:",V),M(C,V.message||"Failed to add note",5e3,"error")}},P=async c=>{try{const S=await new D().callRawAPI(`/v1/api/dealmaker/user/notes/${c}`,{},"DELETE");if(!S.error)y(T=>T.filter(A=>A.id!==c)),M(C,"Note deleted successfully!",5e3,"success");else throw new Error(S.message||"Failed to delete note")}catch(_){console.error("Error deleting note:",_),M(C,_.message||"Failed to delete note",5e3,"error")}};return!m||!t?null:e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-full items-center justify-center p-4",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:v,"aria-hidden":"true"}),e.jsx("div",{className:"relative z-50 w-full max-w-3xl rounded-lg bg-[#161616] shadow-xl",children:e.jsxs("div",{className:"flex flex-col h-[85vh]",children:[e.jsxs("div",{className:"sticky top-0 z-50 flex justify-between items-center bg-[#161616] p-6 border-b border-[#363636]",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx(de,{user:t.creator}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-[#eaeaea]",children:((u=t==null?void 0:t.title)==null?void 0:u.value)||"No Title"}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[((B=(h=t==null?void 0:t.creator)==null?void 0:h.name)==null?void 0:B.value)||"Unknown"," -"," ",G((W=t==null?void 0:t.created_at)==null?void 0:W.value)]})]})]}),e.jsx("button",{onClick:v,className:"text-[#b5b5b5] hover:text-[#eaeaea] p-2",children:e.jsx("svg",{className:"w-5 h-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),e.jsx("div",{className:"border-b border-[#363636] px-6",children:e.jsxs("div",{className:"flex gap-4",children:[e.jsx("button",{onClick:()=>g("details"),className:`border-b-2 py-2 px-1 text-sm ${p==="details"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Details"}),e.jsx("button",{onClick:()=>g("notes"),className:`border-b-2 py-2 px-1 text-sm ${p==="notes"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Notes"}),e.jsx("button",{onClick:()=>g("tasks"),className:`border-b-2 py-2 px-1 text-sm ${p==="tasks"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Tasks"})]})}),e.jsxs("div",{className:"flex-1 overflow-y-auto p-6",children:[e.jsx("div",{className:`h-full ${p==="details"?"block":"hidden"}`,children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Type"}),e.jsx("p",{className:"text-[#b5b5b5]",children:(J=t==null?void 0:t.type)!=null&&J.value?t.type.value==="full_time"?"Full time":t.type.value:"N/A"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Description"}),e.jsx(he,{text:(Y=t==null?void 0:t.description)==null?void 0:Y.value,maxWords:1e3})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Deal Size"}),e.jsxs("p",{className:"text-[#b5b5b5]",children:["$",((Q=t==null?void 0:t.deal_size)==null?void 0:Q.value)||"N/A"]})]}),((K=t==null?void 0:t.expiration_date)==null?void 0:K.value)&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Expiration Date"}),e.jsx("p",{className:"text-[#b5b5b5]",children:G(t.expiration_date.value)})]}),((X=t==null?void 0:t.description_image)==null?void 0:X.value)&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Attached Image"}),e.jsx("img",{src:t.description_image.value,alt:"Description",className:"max-h-96 rounded-lg object-contain"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Status"}),e.jsx("span",{className:"rounded-full bg-[#2e7d3233] px-3 py-1 text-sm text-[#7dd87d]",children:((ee=t==null?void 0:t.status)==null?void 0:ee.value)||"Unknown"})]})]})}),e.jsx("div",{className:`h-full ${p==="notes"?"block":"hidden"}`,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h4",{className:"text-lg font-medium text-[#eaeaea]",children:"Notes"}),e.jsxs("button",{onClick:()=>x(!0),className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})}),"Add Note"]})]}),f&&e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:[e.jsx("textarea",{value:a,onChange:c=>l(c.target.value),placeholder:"Write your note...",className:"mb-4 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-[#eaeaea]",rows:3}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx("button",{onClick:()=>x(!1),className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{onClick:H,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Add Note"})]})]}),e.jsx("div",{className:"space-y-2",children:N.map(c=>e.jsx("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#eaeaea]",children:c.description}),e.jsx("p",{className:"mt-2 text-sm text-[#b5b5b5]",children:G(c.created_at)})]}),e.jsx("button",{onClick:()=>P(c.id),className:"text-[#b5b5b5] hover:text-[#dc3545]",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})},c.id))})]})}),e.jsx("div",{className:`h-full ${p==="tasks"?"block":"hidden"}`,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h4",{className:"text-lg font-medium text-[#eaeaea]",children:"Tasks"}),e.jsxs("button",{onClick:()=>n(!0),className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})}),"Add Task"]})]}),L&&e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Task Title"}),e.jsx("input",{type:"text",value:d.content,onChange:c=>F({...d,content:c.target.value}),placeholder:"Enter task title...",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Due Date"}),e.jsx("input",{type:"date",value:d.due_date,onChange:c=>F({...d,due_date:c.target.value}),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx("button",{onClick:()=>n(!1),className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{onClick:z,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Add Task"})]})]}),e.jsx("div",{className:"space-y-2",children:i.length===0?e.jsx("p",{className:"text-center text-[#b5b5b5] py-4",children:"No tasks yet. Create your first task!"}):i.map(c=>e.jsx("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#eaeaea] font-medium",children:c.description}),e.jsxs("p",{className:"mt-1 text-sm text-[#b5b5b5]",children:["Due: ",G(c.due_date)]})]}),e.jsx("button",{onClick:()=>O(c.id),className:"text-[#b5b5b5] hover:text-[#dc3545]",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})},c.id))})]})})]})]})})]})})},it=()=>{var me;const m=xe(),v=we(),{dispatch:t}=o.useContext(le),[p,g]=o.useState(""),[N,y]=o.useState(!0),[i,r]=o.useState(((me=v.state)==null?void 0:me.activeTab)||"referrals-feed"),[f,x]=o.useState([]),[L,n]=o.useState([]),[a,l]=o.useState(""),[d,F]=o.useState(!1),[C,$]=o.useState(null),[I,z]=o.useState(!1),[O,H]=o.useState(null),[P,u]=o.useState(!1),[h,B]=o.useState(null),[W,J]=o.useState(!1),[Y,Q]=o.useState([]),[K,X]=o.useState([]),[ee,c]=o.useState(!1),[_,S]=o.useState(null),[T,A]=o.useState([]),[V,Z]=o.useState(!1),[ne,te]=o.useState(null),se=s=>{te(s),Z(!0)};o.useEffect(()=>{(async()=>{try{const j=await new D().GetJoinedCommunities();j.error||A(j.list||[])}catch(b){console.error("Failed to load communities:",b)}})()},[]),o.useEffect(()=>{a.trim()&&l(""),ie()},[i]),o.useEffect(()=>{L.length>0&&oe()},[a,L]);const oe=()=>{if(!a.trim()||!L.length){x(L);return}const s=a.toLowerCase(),b=L.filter(j=>{var E,q;const k=j.title.value.toLowerCase().includes(s),w=(E=j.description)!=null&&E.value?j.description.value.toLowerCase().includes(s):!1,R=(q=j.requirements)!=null&&q.value?j.requirements.value.toLowerCase().includes(s):!1;return(k||w||R)&&(j._searchMatches={title:k,description:w,requirements:R}),k||w||R});console.log(`Filtered ${L.length} referrals to ${b.length} results for query "${s}"`),x(b)},re=s=>{const b=s.target.value;l(b),!b.trim()&&L.length>0&&x(L)},ie=async()=>{try{y(!0);const s=new D,[b,j]=await Promise.all([i==="referrals-feed"?s.GetReferralsFeed():i==="my-referrals"?s.GetMyReferrals():s.GetArchivedReferrals(),s.callRawAPI("/v1/api/dealmaker/user/earnings/activity",{},"GET")]);if(b.error)g(b.message);else{const k={};!j.error&&j.list&&j.list.forEach(R=>{var q,U;const E=(q=R.referral_id)==null?void 0:q.value;E&&(k[E]=(U=R.status)==null?void 0:U.value)});const w=b.list.map(R=>{var E;return{...R,payment_status:{value:k[R.id.value]||((E=R.payment_status)==null?void 0:E.value)||"pending"}}});n(w),a.trim()?oe():x(w),X(j.list||[])}}catch(s){console.error("Failed to load referrals:",s),g(s.message||"Failed to load referrals")}finally{y(!1)}},be=async s=>{var b,j;try{const k=new D,w=f.find(E=>E.id.value===s),R=(j=(b=w.recommendations.value[0])==null?void 0:b.user)==null?void 0:j.id.value;if(console.log("referral",w),console.log("userid",R),R||M(t,"No recommendation found",5e3,"error"),R){const E=await k.callRawAPI(`/v1/api/dealmaker/user/referral/${s}/complete`,{user_id:{value:R}},"POST");E.error?(g(E.message),M(t,E.message,5e3,"error")):(x(q=>q.map(U=>U.id.value===s?{...U,status:{value:"completed"},payment_status:{value:"pending"}}:U)),M(t,"Referral marked as complete! Please proceed with payment.",5e3,"success"),m(`/member/referrals/${w.id.value}/payment`,{state:{referral:w}}))}}catch(k){g(k.message||"Failed to update referral"),M(t,k.message||"Failed to update referral",5e3,"error")}},ce=async s=>{H(s),z(!0)},pe=async()=>{try{(await new D().UpdateReferralStatus(O,"deleted")).error||(x(j=>j.filter(k=>k.id.value!==O)),M(t,"Referral deleted successfully!",5e3,"success"))}catch(s){g(s.message||"Failed to update referral")}finally{z(!1),H(null)}},ve=async s=>{try{(await new D().UpdateReferralStatus(s,"archived")).error||(x(k=>k.filter(w=>w.id.value!==s)),M(t,"Referral archived successfully!",5e3,"success"))}catch(b){g(b.message||"Failed to update referral")}},je=async s=>{try{(await new D().UpdateReferralStatus(s,"completed")).error||(x(k=>k.filter(w=>w.id.value!==s)),M(t,"Referral unarchived successfully!",5e3,"success"))}catch(b){g(b.message||"Failed to update referral")}},ge=s=>{B(s),u(!0)},fe=s=>{S(s),c(!0)},Ne=()=>i==="my-referrals"?e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-[#eaeaea]",children:"My Opportunities"}),e.jsx("p",{className:"text-[#b5b5b5]",children:"Create, Share, and track your Open and Direct Opportunities here."})]}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsxs("div",{className:"flex relative items-center",children:[e.jsx("input",{type:"text",value:a,onChange:re,placeholder:"Search your Opportunity",className:"h-10 w-64 rounded-lg border border-[#363636] bg-[#161616] pl-4 pr-10 text-[#eaeaea]"}),a?e.jsx("button",{onClick:()=>{l(""),x(L)},className:"absolute right-3 cursor-pointer",title:"Clear search",children:e.jsx("svg",{className:"h-4 w-4 text-[#b5b5b5] hover:text-[#eaeaea]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}):e.jsx("div",{className:"absolute right-3",children:e.jsx("svg",{className:"h-4 w-4 text-[#b5b5b5]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]}),e.jsxs(Ce,{to:"/member/referrals/add",children:[" ",e.jsxs("button",{className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("span",{children:"+"})," Post a New Opportunity"]})]})]})]}):i==="archived"?e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-[#eaeaea]",children:"Archived Opportunities"}),e.jsx("p",{className:"text-[#b5b5b5]",children:"Archive or unarchive your Open or Direct Opportunities"})]}),e.jsx("div",{className:"flex gap-4 items-center",children:e.jsxs("div",{className:"flex relative items-center",children:[e.jsx("input",{type:"text",value:a,onChange:re,placeholder:"Search archived opportunities...",className:"h-10 w-64 rounded-lg border border-[#363636] bg-[#161616] pl-4 pr-10 text-[#eaeaea] placeholder-[#b5b5b5]"}),a?e.jsx("button",{onClick:()=>{l(""),x(L)},className:"absolute right-3 cursor-pointer",title:"Clear search",children:e.jsx("svg",{className:"h-4 w-4 text-[#b5b5b5] hover:text-[#eaeaea]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}):e.jsx("div",{className:"absolute right-3",children:e.jsx("svg",{className:"h-4 w-4 text-[#b5b5b5]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})})]}):null,ye=s=>{var b,j,k,w;if(!s.referral_type||!s.referral_type.value)return e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx(ae,{}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Referral"})]});if(s.referral_type.value==="reposted")return e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx(Te,{}),e.jsxs("span",{className:"text-sm text-[#eaeaea]",children:["Reposted by ",((j=(b=s.reposted_by)==null?void 0:b.name)==null?void 0:j.value)||"Unknown"]})]});if(s.referral_type.value==="open referral")return e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx(ae,{}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Open Referral"})]});if(s.referral_type.value==="community referral")return e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx(ae,{}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Community Referral"})]});if(s.referral_type.value==="direct referral"){const R=((w=(k=s.referred_to)==null?void 0:k.name)==null?void 0:w.value)||"Someone";return e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx(De,{}),e.jsxs("span",{className:"text-sm text-[#eaeaea]",children:["Direct Referral To ",R]})]})}return e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx(ae,{}),e.jsx("span",{className:"text-sm text-[#eaeaea] capitalize",children:s.referral_type.value})]})},ke=s=>{var k;if(!s||!s.id||!s.status)return e.jsx("div",{className:"flex gap-3 items-center",children:e.jsxs("button",{onClick:()=>se(s),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})}),"View Details"]})});const b=K.find(w=>{var R;return((R=w==null?void 0:w.id)==null?void 0:R.value)===s.id.value}),j=((k=b==null?void 0:b.status)==null?void 0:k.value)==="completed";return e.jsx("div",{className:"flex gap-3 items-center",children:s.status.value==="completed"?e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>ve(s.id.value),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 7l-8 8-4-4"})}),"Archive"]}),s.owner.value?e.jsxs("button",{onClick:()=>{m(`/member/referrals/${s.id.value}/payment`,{state:{referral:s}})},className:`flex items-center gap-2 rounded-lg px-4 py-2 text-sm ${j?"border border-[#2e7d32] bg-[#2e7d3233] text-[#7dd87d]":"bg-[#1976d2] text-white"}`,disabled:j,children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})}),j?"Payment Complete":"Make Payment"]}):e.jsxs("button",{onClick:()=>m(`/member/referrals/${s.id.value}/payment-status`),className:"flex items-center gap-2 rounded-lg border border-[#2e7d32] bg-[#2e7d3233] px-4 py-2 text-sm text-[#7dd87d]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})}),"View Commission Status"]}),e.jsxs("button",{onClick:()=>ce(s.id.value),className:"flex items-center gap-2 rounded-lg bg-[#dc3545] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})]}):s.status.value==="archived"?e.jsx(e.Fragment,{children:e.jsx("button",{onClick:()=>je(s.id.value),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#2e7d3233] px-4 py-2 text-sm text-[#7dd87d]",children:"Unarchive"})}):s.owner.value?e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>m(`/member/referrals/edit/${s.id.value}`),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Edit"]}),e.jsxs("button",{onClick:()=>ce(s.id.value),className:"flex items-center gap-2 rounded-lg bg-[#dc3545] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]}),e.jsxs("button",{onClick:()=>be(s.id.value),className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Mark Complete"]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>ge(s.id.value),className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"})}),"Refer"]}),e.jsxs("button",{onClick:()=>m(`/member/chat/${s.id.value}`),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),"Chat"]}),e.jsxs("button",{onClick:()=>fe(s.id.value),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Repost"]})]})})};return e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-4",children:[p&&e.jsx(ue,{message:p,type:p.includes("success")?"success":"error"}),Ne(),e.jsxs("div",{style:i==="referrals-feed"&&P?{margin:"auto",width:"700px",paddingBottom:"20px"}:{},className:"mb-6 border-b border-[#363636]",children:[e.jsx("button",{onClick:()=>r("referrals-feed"),className:`mr-6 border-b-2 pb-2 text-sm ${i==="referrals-feed"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Opportunities Feed"}),e.jsx("button",{onClick:()=>r("my-referrals"),className:`mr-6 border-b-2 pb-2 text-sm ${i==="my-referrals"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"My Opportunities"}),e.jsx("button",{onClick:()=>r("archived"),className:`border-b-2 pb-2 text-sm ${i==="archived"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Archived"})]}),a.trim()&&f.length>0&&e.jsxs("div",{className:"mb-4 p-2 bg-[#1e1e1e] border border-[#363636] rounded-lg",children:[e.jsxs("p",{className:"text-[#eaeaea]",children:["Found ",e.jsx("span",{className:"font-semibold text-[#7dd87d]",children:f.length}),i==="archived"?" archived":"",' opportunities matching "',a,'"']}),e.jsx("p",{className:"text-[#b5b5b5] text-sm mt-1",children:"Searching in title, description, and requirements fields"})]}),e.jsxs("div",{className:"space-y-4",children:[i==="referrals-feed"&&P&&e.jsx(Ee,{referralId:h,onClose:()=>{u(!1),B(null)}}),N?[...Array(2)].map((s,b)=>e.jsx(_e,{className:"h-48 w-full rounded-lg"},b)):i==="referrals-feed"&&P?e.jsx(e.Fragment,{}):f.length===0&&a.trim().length>0?e.jsxs("div",{className:"flex flex-col items-center justify-center p-8 text-center",children:[e.jsx("svg",{className:"w-16 h-16 mb-4 text-[#363636]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),e.jsx("h3",{className:"text-xl font-medium text-[#eaeaea] mb-2",children:"No results found"}),e.jsxs("p",{className:"text-[#b5b5b5]",children:["No ",i==="archived"?"archived ":"",'opportunities matching "',a,'" were found in titles, descriptions, or requirements. Try a different search term.']})]}):f.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center p-8 text-center",children:[e.jsx("h3",{className:"text-xl font-medium text-[#eaeaea] mb-2",children:"No opportunities available"}),e.jsx("p",{className:"text-[#b5b5b5]",children:i==="my-referrals"?"You haven't created any opportunities yet.":i==="archived"?"You don't have any archived opportunities.":"There are no opportunities in your feed."})]}):f.map(s=>{var b,j,k,w;return e.jsxs("div",{className:"flex flex-col gap-2 rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsx(de,{user:s.creator}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold text-[#eaeaea] flex items-center",children:[s.title.value,a.trim()&&((b=s._searchMatches)==null?void 0:b.title)&&e.jsx("span",{className:"ml-2 text-xs text-[#7dd87d] bg-[#2e7d3233] px-2 py-1 rounded-full",children:"Title match"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[s.creator.name.value," -"," ",G(s.created_at.value)]}),(i==="my-referrals"||i==="referrals-feed")&&ye(s)]})]})]}),e.jsxs("div",{className:"flex gap-2",children:[i==="my-referrals"&&e.jsxs("span",{className:"rounded-full flex justify-center items-center bg-[#2e7d3233] px-3 py-1 text-sm text-[#7dd87d]",children:["Status: ",s.status.value]}),e.jsxs("button",{onClick:()=>se(s),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#242424]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})}),"View Details"]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:["Description",a.trim()&&((j=s._searchMatches)==null?void 0:j.description)&&e.jsx("span",{className:"ml-2 text-xs text-[#7dd87d] bg-[#2e7d3233] px-2 py-1 rounded-full",children:"Match found"})]}),e.jsx(he,{text:s.description.value})]}),e.jsx("div",{className:"flex gap-4 items-center",children:((w=(k=s.recommendations)==null?void 0:k.value)==null?void 0:w.length)>0&&e.jsxs("button",{onClick:()=>{Q(s.recommendations.value),J(!0)},className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})}),"View Recommendations (",s.recommendations.value.length,")"]})}),ke(s)]},s.id.value)})]}),e.jsx(Se,{isOpen:d||C!==null,onClose:s=>{F(!1),$(null),s&&ie()},referral:C}),e.jsx(Me,{isOpen:I,onClose:()=>{z(!1),H(null)},onConfirm:pe,title:"Delete Referral",message:"Are you sure you want to delete this referral? This action cannot be undone."}),e.jsx(Fe,{isOpen:W,onClose:()=>J(!1),recommendations:Y}),e.jsx(Be,{isOpen:ee,onClose:s=>{c(!1),S(null),s&&ie()},referralId:_,communities:T,referrals:f}),e.jsx(ze,{isOpen:V,onClose:()=>{Z(!1),te(null)},referral:ne})]})};export{it as default};
