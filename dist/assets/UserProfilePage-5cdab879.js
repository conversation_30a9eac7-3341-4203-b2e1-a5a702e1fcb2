import{j as e}from"./@react-google-maps/api-211df1ae.js";import{d as w,r as n}from"./vendor-1c28ea83.js";import{G as _,M as E,s as g}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const f=[{id:1,name:"Agriculture & Farming"},{id:2,name:"Construction"},{id:3,name:"Education & Training"},{id:4,name:"Energy & Utilities"},{id:5,name:"Financial Services"},{id:6,name:"Government & Public Sector"},{id:7,name:"Healthcare & Life Sciences"},{id:8,name:"Hospitality & Tourism"},{id:9,name:"Information Technology & Software"},{id:10,name:"Legal Services"},{id:11,name:"Logistics & Transportation"},{id:12,name:"Manufacturing"},{id:13,name:"Marketing & Advertising"},{id:14,name:"Media & Entertainment"},{id:15,name:"Non-Profit & Charities"},{id:16,name:"Professional Services"},{id:17,name:"Real Estate & Property Management"},{id:18,name:"Retail & E-Commerce"},{id:19,name:"Telecommunications"},{id:20,name:"Wholesale & Distribution"}],Q=()=>{var l,o,c,m,x,u,h,p,b;const v=w(),[s,j]=n.useState(null),[N,y]=n.useState(!0),{dispatch:d}=n.useContext(_);n.useEffect(()=>{P()},[]);const P=async()=>{try{const i=await new E().callRawAPI("/v1/api/dealmaker/user/details",{},"GET");i.error?g(d,"Failed to fetch profile details",5e3,"error"):j(i.model)}catch(t){console.error("Error fetching profile:",t),g(d,"Failed to fetch profile details",5e3,"error")}finally{y(!1)}};return N?e.jsx("div",{className:"flex min-h-screen items-center justify-center bg-[#1e1e1e]",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#2e7d32]"})}):e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-4 md:p-6",children:[e.jsxs("div",{className:"mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:"My Profile"}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"View and manage your profile information"})]}),e.jsx("button",{onClick:()=>v("/member/profile/edit"),className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea] hover:bg-[#1b5e20] transition-colors",children:"Edit Profile"})]}),e.jsx("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-[5fr_1fr]",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#252525] p-6",children:[e.jsxs("div",{className:"mb-6 flex items-center gap-4",children:[e.jsx("div",{className:"h-20 w-20 rounded-full bg-[#2e7d32] flex items-center justify-center",children:(l=s==null?void 0:s.photo)!=null&&l.value?e.jsx("img",{src:s.photo.value,alt:"Profile",className:"h-full w-full rounded-full object-cover"}):e.jsx("div",{className:"text-2xl font-bold text-white",children:(c=(o=s==null?void 0:s.first_name)==null?void 0:o.value)==null?void 0:c.charAt(0)})}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-semibold text-[#eaeaea]",children:[(m=s==null?void 0:s.first_name)==null?void 0:m.value," ",(x=s==null?void 0:s.last_name)==null?void 0:x.value]}),e.jsxs("p",{className:"text-[#b5b5b5]",children:["ID: ",(u=s==null?void 0:s.id)==null?void 0:u.value]})]})]}),e.jsx("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium text-[#b5b5b5]",children:"Contact Information"}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Email"}),e.jsx("p",{className:"text-[#eaeaea]",children:((h=s==null?void 0:s.email)==null?void 0:h.value)||"Not provided"})]})})]})})]}),e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#252525] p-6",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-[#eaeaea]",children:"Professional Information"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 mb-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Industry"}),e.jsx("p",{className:"text-[#eaeaea]",children:(()=>{var a,r;const t=typeof((a=s==null?void 0:s.industry_id)==null?void 0:a.value)=="string"?parseInt(s.industry_id.value,10):(r=s==null?void 0:s.industry_id)==null?void 0:r.value,i=f.find(I=>I.id===t);return i?i.name:"Not specified"})()})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Payout Method"}),e.jsx("p",{className:"text-[#eaeaea]",children:((p=s==null?void 0:s.payout_method)==null?void 0:p.value)||"Not specified"})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-[#b5b5b5] mb-2",children:"Industries I'm Interested In"}),(b=s==null?void 0:s.interested_industries)!=null&&b.value&&Array.isArray(s.interested_industries.value)&&s.interested_industries.value.length>0?e.jsx("div",{className:"flex flex-wrap gap-2",children:s.interested_industries.value.map(t=>{const i=typeof t=="string"?parseInt(t,10):t,a=f.find(r=>r.id===i);return a?e.jsx("span",{className:"inline-block rounded-full bg-[#2e7d3233] px-3 py-1 text-sm text-[#7dd87d]",children:a.name},a.id):null})}):e.jsx("p",{className:"text-[#eaeaea]",children:"No industries specified"})]})]})]})})]})};export{Q as default};
