import{j as s}from"./@react-google-maps/api-211df1ae.js";import{r as o,L as t}from"./vendor-1c28ea83.js";import{G as d,A as x,a0 as u,at as a,au as h,L as j,a8 as b}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const q=({})=>{const{state:{recent_tabs:g,path:n},dispatch:c}=o.useContext(d),{state:v,dispatch:l}=o.useContext(x),{profile:e}=u(),i=[{name:"Profile",value:"profile",link:`/${e==null?void 0:e.role}/profile`,roles:["admin","user","manager","company"],icon:s.jsx(a,{})},{name:"Help Page",value:"help_page",link:`/${e==null?void 0:e.role}/help_page`,roles:["user","company"],icon:s.jsx(a,{})}],m=()=>(e==null?void 0:e.role)==="user"&&e!=null&&e.is_company?e==null?void 0:e.company_name:e==null?void 0:e.first_name;return s.jsx("div",{className:"non_print_section flex items-center gap-3",children:s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"peer flex items-center",children:(e==null?void 0:e.photo)&&s.jsx("img",{className:"h-[1.5rem] w-[1.5rem] rounded-full object-cover",src:e==null?void 0:e.photo})}),s.jsxs("button",{className:"peer flex cursor-pointer items-center gap-2 capitalize text-white",children:[m(),s.jsx("span",{children:s.jsx(h,{})})]}),s.jsxs("ul",{className:"absolute right-5 top-[80%] z-20 hidden rounded-lg border border-[#a8a8a8] bg-white p-2 text-sm text-[#525252] shadow-md hover:block focus:block peer-focus:block peer-focus-visible:block",children:[i.map((r,p)=>{if(r.roles.includes(e==null?void 0:e.role))return s.jsx("li",{children:s.jsxs(t,{className:`hover:text[#262626] flex cursor-pointer items-center rounded-md px-4 py-3 hover:bg-[#F4F4F4] ${n===r.value?"bg-[#F4F4F4]":""}`,to:r==null?void 0:r.link,children:[r!=null&&r.icon?s.jsx("span",{className:"mr-2",children:s.jsx(j,{children:r.icon})}):null,s.jsx("span",{children:r==null?void 0:r.name})]})},p)}),s.jsx("li",{className:"w-full",children:s.jsx(t,{className:"hover:text[#262626] group flex w-full cursor-pointer items-center rounded-md px-4 py-3 hover:bg-[#F4F4F4] hover:text-red-500",to:`/${e==null?void 0:e.role}/login`,onClick:()=>{l({type:"LOGOUT"}),b(c,null,"dashboard_image")},children:s.jsx("span",{children:"Logout"})})})]})]})})};export{q as default};
