import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as n,d as K}from"./vendor-1c28ea83.js";import{M as q,G as J,A as Q,o as W,t as v,a6 as u,s as N}from"./index-b3edd152.js";import{u as X}from"./react-hook-form-eec8b32f.js";import{c as Y,a as d}from"./yup-1b5612ec.js";import{P as Z}from"./index-7ba88dde.js";import"./index-e2604cb4.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let S=new q;const P=[{header:"Row",accessor:"row"},{header:"Customer",accessor:"userEmail"},{header:"Plan",accessor:"planName"},{header:"Starts",accessor:"currentPeriodStart",type:"timestamp"},{header:"Ends",accessor:"currentPeriodEnd",type:"timestamp"},{header:"type",accessor:"planType",mapping:{recurring:"Recurring",life_time:"Lifetime"}},{header:"Price",accessor:"planAmount",type:"currency"},{header:"Has Trial",accessor:"trialDays"},{header:"Status",accessor:"status"},{header:"Action",accessor:""}],Pe=()=>{var b,w;const{dispatch:p}=n.useContext(J),{dispatch:x}=n.useContext(Q);n.useState("");const[k,_]=n.useState([]),[o,g]=n.useState(10),[f,C]=n.useState(0),[ee,R]=n.useState(0),[c,E]=n.useState(0),[T,D]=n.useState(!1),[A,L]=n.useState(!1);K();const z=Y({customer_email:d(),plan_name:d(),sub_status:d(),plan_type:d()}),{register:l,handleSubmit:V,formState:{errors:y}}=X({resolver:W(z)}),$=[{key:"",value:"All"},{key:"active",value:"Active"},{key:"trialing",value:"Trialing"},{key:"canceled",value:"Canceled"}],F=[{key:"",value:"All"},{key:"recurring",value:"Recurring"},{key:"lifetime",value:"Lifetime"}];function G(t){(async function(){g(t),await i(1,t)})()}function H(){(async function(){await i(c-1>1?c-1:1,o)})()}function I(){(async function(){await i(c+1<=f?c+1:1,o)})()}async function i(t,r,a){try{const s=await S.getStripeSubscriptions({page:t,limit:r},a),{list:m,total:U,limit:B,num_pages:j,page:h}=s;_(m),g(+B),C(+j),E(+h),R(+U),D(+h>1),L(+h+1<=+j)}catch(s){console.log("ERROR",s),v(x,s.message)}}const M=t=>{const r=u(t.customer_email),a=u(t.plan_name),s=u(t.sub_status),m=u(t.plan_type);i(1,o,{customer_email:r,plan_name:a,sub_status:s,plan_type:m})},O=async t=>{console.log(t);try{const r=await S.adminCancelStripeSubscription(t,{});N(p,r.message,3e3),i(1,o)}catch(r){console.log("ERROR",r),N(p,r.message),v(x,r.message)}};return n.useEffect(()=>{p({type:"SETPATH",payload:{path:"subscriptions"}}),async function(){await i(1,o)}()},[]),e.jsxs(e.Fragment,{children:[e.jsxs("form",{className:"mb-10 rounded bg-white p-5 shadow",onSubmit:V(M),children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Search"}),e.jsxs("div",{className:"filter-form-holder mt-10 flex flex-wrap",children:[e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Customer"}),e.jsx("input",{type:"text",placeholder:"Email",...l("customer_email"),className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(b=y.customer_email)==null?void 0:b.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Plan"}),e.jsx("input",{type:"text",placeholder:"Plan Name",...l("plan_name"),className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(w=y.plan_name)==null?void 0:w.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("plan_type"),children:F.map(t=>e.jsx("option",{value:t.key,defaultValue:"",children:t.value},t.key))}),e.jsx("p",{className:"text-xs italic text-red-500"})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("sub_status"),children:$.map(t=>e.jsx("option",{value:t.key,defaultValue:"",children:t.value},t.key))}),e.jsx("p",{className:"text-xs italic text-red-500"})]})]}),e.jsxs("div",{className:"search-buttons pl-2",children:[e.jsx("button",{type:"submit",className:"mr-2 inline-block rounded bg-blue-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:"Search"}),e.jsx("button",{type:"reset",onClick:()=>i(1,o),className:"inline-block rounded bg-gray-800 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-gray-900 hover:shadow-lg focus:bg-gray-900 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-gray-900 active:shadow-lg",children:"Reset"})]})]}),e.jsxs("div",{className:"mt-[100px] overflow-x-auto rounded bg-white p-5 shadow",children:[e.jsx("div",{className:"mb-3 flex w-full justify-between text-center",children:e.jsx("h4",{className:"text-2xl font-medium",children:"Subscriptions "})}),e.jsx("div",{className:"overflow-x-auto border-b border-gray-200 shadow",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:P.map((t,r)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},r))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:k.map((t,r)=>e.jsx("tr",{children:P.map((a,s)=>{if(a.accessor=="")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t.status!=="canceled"?e.jsx("button",{onClick:()=>O(t.subId),type:"button",className:"mx-1 inline-block rounded-full bg-red-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-red-700 hover:shadow-lg focus:bg-red-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-red-800 active:shadow-lg",children:"Cancel"}):""},s);if(a.mapping)return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.mapping[t[a.accessor]]},s);if(t.planType==="recurring"&&a.type==="timestamp")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(t[a.accessor]*1e3).toLocaleDateString("en-US",{dateStyle:"medium"})},s);if(t.planType==="lifetime"&&a.type==="timestamp"){if(a.accessor==="currentPeriodStart")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(t.createdAt*1e3).toLocaleDateString("en-US",{dateStyle:"medium"})},s);if(a.accessor==="currentPeriodEnd")return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:"Infinity"},s)}else if(a.type=="currency")return e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:["$",+(t[a.accessor]??0)]},s);return e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[a.accessor]},s)})},r))})]})})]}),e.jsx(Z,{currentPage:c,pageCount:f,pageSize:o,canPreviousPage:T,canNextPage:A,updatePageSize:G,previousPage:H,nextPage:I})]})};export{Pe as default};
