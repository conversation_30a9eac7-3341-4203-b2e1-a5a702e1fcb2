import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as a,f as u,d as N}from"./vendor-1c28ea83.js";import{M as f,A as g,G as b,t as y,S as v}from"./index-b3edd152.js";/* empty css                             */import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let l=new f;const J=()=>{const{dispatch:x}=a.useContext(g);a.useContext(b);const[s,o]=a.useState({}),[h,i]=a.useState(!0),r=u(),d=N(),c=t=>t?new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",p=(t,n="USD")=>t?new Intl.NumberFormat("en-US",{style:"currency",currency:n}).format(t):"N/A";a.useEffect(function(){(async function(){try{i(!0),l.setTable("payment");const t=await l.callRestAPI({id:Number(r==null?void 0:r.id)},"GET");t.error||(o(t.model),i(!1))}catch(t){i(!1),console.log("error",t),y(x,t.message)}})()},[]);const j=t=>{switch(t==null?void 0:t.toLowerCase()){case"paid":return"status paid";case"pending":return"status pending";case"failed":return"status failed";case"refunded":return"status refunded";default:return"status"}},m=t=>({create_community:"Buyer",subscription:"Subscription",referral:"Introduction"})[t]||t;return e.jsx("div",{className:"view-payment-page bg-[#1E1E1E] text-white min-h-screen",children:e.jsx("div",{className:"container mx-auto p-6",children:h?e.jsx("div",{className:"bg-[#161616] p-6 rounded",children:e.jsx(v,{})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"header mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Payment Details"}),e.jsxs("button",{onClick:()=>d("/admin/payments"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to List"]})]}),e.jsx("p",{className:"text-[#9ca3af] text-sm",children:"View and manage payment details"})]}),e.jsxs("div",{className:"content-wrapper bg-[#161616] rounded-lg border border-[#2d2d3d] overflow-hidden",children:[e.jsx("div",{className:"header-section p-6 border-b border-[#2d2d3d]",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-xl font-semibold mb-2",children:["Payment #",s==null?void 0:s.id]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-[#9ca3af]",children:[e.jsxs("div",{children:["Transaction ID: ",(s==null?void 0:s.transaction_id)||(s==null?void 0:s.stripe_invoice_id)||`ch_${Math.floor(Math.random()*1e10)}`]}),e.jsxs("div",{children:["Date: ",c(s==null?void 0:s.created_at)]})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:j(s==null?void 0:s.status),children:(s==null?void 0:s.status)||"Unknown"}),e.jsx("span",{className:"type-badge bg-[#252538] text-white px-3 py-1 rounded-full text-xs",children:m(s==null?void 0:s.type)})]})]})}),e.jsx("div",{className:"amount-section p-6 border-b border-[#2d2d3d] bg-[#1a1a1a]",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-[#9ca3af] text-sm mb-1",children:"Amount"}),e.jsx("div",{className:"amount",children:p(s==null?void 0:s.amount,s==null?void 0:s.currency)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-[#9ca3af] text-sm mb-1",children:"Payment Method"}),e.jsx("div",{children:(s==null?void 0:s.payment_method)||"Credit Card"})]})]})}),e.jsxs("div",{className:"details-grid p-6 grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"left-column space-y-6",children:[e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Payment Information"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Payment ID"}),e.jsx("div",{children:(s==null?void 0:s.id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Type"}),e.jsx("div",{children:m(s==null?void 0:s.type)})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Status"}),e.jsx("div",{children:(s==null?void 0:s.status)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Currency"}),e.jsx("div",{children:(s==null?void 0:s.currency)||"USD"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Payment Method"}),e.jsx("div",{children:(s==null?void 0:s.payment_method)||"Credit Card"})]})]})]}),e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Stripe Information"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Stripe Invoice ID"}),e.jsx("div",{children:(s==null?void 0:s.stripe_invoice_id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Stripe Subscription ID"}),e.jsx("div",{children:(s==null?void 0:s.stripe_subscription_id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Transaction ID"}),e.jsx("div",{children:(s==null?void 0:s.transaction_id)||"N/A"})]})]})]})]}),e.jsx("div",{className:"right-column space-y-6",children:e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"User Information"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"User ID"}),e.jsx("div",{children:s!=null&&s.user_id?`#USR${String(s.user_id).padStart(3,"0")}`:"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Reference ID"}),e.jsx("div",{children:(s==null?void 0:s.reference_id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Receiver ID"}),e.jsx("div",{children:(s==null?void 0:s.receiver_id)||"N/A"})]})]})]})})]}),e.jsxs("div",{className:"dates-section p-6 border-t border-[#2d2d3d]",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Dates"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Created At"}),e.jsx("div",{children:c(s==null?void 0:s.created_at)})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Updated At"}),e.jsx("div",{children:c(s==null?void 0:s.updated_at)})]})]})]}),e.jsx("div",{className:"actions-section p-6 border-t border-[#2d2d3d] flex justify-end gap-4",children:e.jsxs("button",{onClick:()=>d("/admin/payments"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to List"]})})]})]})})})};export{J as default};
