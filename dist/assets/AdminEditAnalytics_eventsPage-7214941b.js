import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as a,d as _,f as j}from"./vendor-1c28ea83.js";import{u as A}from"./react-hook-form-eec8b32f.js";import{M as I,A as N,G as T,t as h,S as w,o as R,s as D}from"./index-b3edd152.js";import{c as P,a as p}from"./yup-1b5612ec.js";import{M as f}from"./MkdInput-67f7082d.js";import{I as k}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let i=new I;const de=r=>{const{dispatch:y}=a.useContext(N),g=P({details:p().required(),user_id:p().required(),type:p().required()}).required(),{dispatch:o}=a.useContext(T),[x,l]=a.useState(!1),[b,d]=a.useState(!1),v=_(),{register:n,handleSubmit:E,setError:q,setValue:m,formState:{errors:c}}=A({resolver:R(g)}),s=j();a.useEffect(function(){(async function(){try{d(!0),i.setTable("analytics_events");const e=await i.callRestAPI({id:r.activeId?r.activeId:Number(s==null?void 0:s.id)},"GET");e.error||(m("details",e.model.details),m("user_id",e.model.user_id),m("type",e.model.type),d(!1))}catch(e){d(!1),console.log("error",e),h(y,e.message)}})()},[]);const S=async e=>{l(!0);try{i.setTable("analytics_events"),(await i.callRestAPI({id:r.activeId?r.activeId:Number(s==null?void 0:s.id),details:e.details,user_id:e.user_id,type:e.type},"PUT")).error||(D(o,"Updated"),v("/admin/analytics_events"),r.setSidebar(!1),o({type:"REFRESH_DATA",payload:{refreshData:!0}})),l(!1)}catch(u){l(!1),console.log("Error",u),h(y,u.message)}};return a.useEffect(()=>{o({type:"SETPATH",payload:{path:"analytics_events"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Analytics_events"}),b?t.jsx(w,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:E(S),children:[t.jsx(f,{type:"text",page:"edit",name:"details",errors:c,label:"Details",placeholder:"Details",register:n,className:""}),t.jsx(f,{type:"text",page:"edit",name:"user_id",errors:c,label:"User_id",placeholder:"User_id",register:n,className:""}),t.jsx(f,{type:"text",page:"edit",name:"type",errors:c,label:"Type",placeholder:"Type",register:n,className:""}),t.jsx(k,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{de as default};
