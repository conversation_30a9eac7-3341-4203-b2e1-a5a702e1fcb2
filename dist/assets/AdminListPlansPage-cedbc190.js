import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as n,d as m,r as u}from"./vendor-1c28ea83.js";import{M as o,A as f,G as h,L as _,E as g,c as x}from"./index-b3edd152.js";import{M as b}from"./index-db36e1ef.js";/* empty css                              */import{A as v}from"./index.esm-2d1feecf.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";new o;const E=[{header:"Plan Name",accessor:"name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Description",accessor:"description",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:t=>t&&t.length>50?t.substring(0,50)+"...":t},{header:"Amount",accessor:"price",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:(t,s)=>{const l=parseFloat(t).toFixed(2),r=s.billing_cycle||"monthly";return`$${l}/${r.replace("ly","")}`}},{header:"Created At",accessor:"created_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:t=>{const s=new Date(t);return`${s.getFullYear()}-${String(s.getMonth()+1).padStart(2,"0")}-${String(s.getDate()).padStart(2,"0")}`}},{header:"Status",accessor:"is_active",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:t=>t===1?'<span class="status active">Active</span>':'<span class="status deleted">Inactive</span>'}],X=()=>{n.useContext(f),n.useContext(h);const t=m();n.useState("");const s=u.useRef(null),l=a=>{console.log("View plan with ID:",a[0]),t(`/admin/view-plan/${a[0]}`)},r=a=>{console.log("Edit plan with ID:",a[0])},c=async a=>{try{console.log("Deleting plan with ID:",a[0]);const i=new o;i.setTable("plan");const d=await i.callRestAPI({id:a[0]},"DELETE");console.log("Delete result:",d),s.current&&s.current.click()}catch(i){console.error("Error deleting plan:",i)}},p=[{id:1,name:"Basic Plan",description:"Essential features for starters",price:"29.00",billing_cycle:"monthly",features:"[]",is_popular:0,is_trial_available:0,trial_days:null,is_active:1,stripe_product_id:"prod_SBtWL2UUf6XHVc",stripe_price_id:"price_1RHVjOBgOlWo0lDU1DvRplWK",created_at:"2025-01-15T19:46:46.000Z",updated_at:"2025-01-15T19:46:46.000Z"},{id:2,name:"Premium Plan",description:"Advanced features for professionals",price:"99.00",billing_cycle:"monthly",features:"[]",is_popular:1,is_trial_available:0,trial_days:null,is_active:1,stripe_product_id:"prod_SBtWL2UUf6XHVc",stripe_price_id:"price_1RHVjOBgOlWo0lDU1DvRplWK",created_at:"2025-01-10T19:46:46.000Z",updated_at:"2025-01-10T19:46:46.000Z"},{id:3,name:"Enterprise Plan",description:"Complete solution for large teams",price:"299.00",billing_cycle:"monthly",features:"[]",is_popular:0,is_trial_available:0,trial_days:null,is_active:1,stripe_product_id:"prod_SBtWL2UUf6XHVc",stripe_price_id:"price_1RHVjOBgOlWo0lDU1DvRplWK",created_at:"2025-01-05T19:46:46.000Z",updated_at:"2025-01-05T19:46:46.000Z"}];return e.jsx(e.Fragment,{children:e.jsx("div",{className:"opportunities-dashboard bg-[#1E1E1E]",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"header",children:[e.jsx("h1",{children:"Plans"}),e.jsx("p",{children:"Manage and configure subscription plans"})]}),e.jsx("div",{className:"search-add"}),e.jsx("div",{className:"table-container",children:e.jsx(_,{children:e.jsx(b,{columns:E,tableRole:"admin",table:"plan",actionId:"id",searchField:"name",actions:{view:{show:!0,action:l,multiple:!1,locations:["buttons"],showChildren:!1,children:"View",icon:e.jsx(v,{className:"text-blue-500"})},edit:{show:!1,multiple:!1,action:r,locations:["buttons"],showChildren:!1,children:"Edit",icon:e.jsx(g,{stroke:"#4CAF50"})},delete:{show:!1,action:c,multiple:!1,locations:["buttons"],showChildren:!1,children:"Delete",icon:e.jsx(x,{fill:"#E53E3E"})},select:{show:!1,action:null,multiple:!1},add:{show:!1,action:null,multiple:!1},export:{show:!1,action:null,multiple:!1}},actionPosition:["buttons"],refreshRef:s,externalData:{use:!0,data:p,loading:!1,page:1,limit:10,pages:1,total:3}})})}),e.jsxs("div",{className:"pagination",children:[e.jsx("div",{className:"pagination-info",children:"Showing 1 to 3 of 3 entries"}),e.jsxs("div",{className:"pagination-buttons",children:[e.jsx("button",{className:"pagination-button prev",children:"Previous"}),e.jsx("button",{className:"pagination-button next",children:"Next"})]})]})]})})})};export{X as default};
