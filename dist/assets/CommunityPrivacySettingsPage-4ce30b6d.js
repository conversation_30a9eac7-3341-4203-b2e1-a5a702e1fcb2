import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import"./index-b3edd152.js";import"./pdf-lib-623decea.js";import"./react-toggle-58b0879a.js";/* empty css                 */import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./@uppy/dashboard-3a4b1704.js";const S=()=>e.jsxs("div",{className:"space-y-6 p-4 md:p-6",children:[e.jsx("h1",{className:"text-3xl font-bold text-[#eaeaea]",children:"Create Your Community and Start Your Journey"}),e.jsxs("div",{className:"flex border-b border-[#363636]",children:[e.jsx("button",{className:"px-6 py-2 text-[#b5b5b5]",children:"Basic Information"}),e.jsx("button",{className:"px-6 py-2 text-[#b5b5b5]",children:"Community Description"}),e.jsx("button",{className:"px-6 py-2 text-[#b5b5b5]",children:"Community Guidelines"}),e.jsx("button",{className:"border-b-2 border-[#7dd87d] px-6 py-2 text-[#7dd87d]",children:"Privacy Settings"})]}),e.jsx("div",{className:"rounded-xl bg-[#252525] p-8",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[#b5b5b5]",children:"Content Moderation Settings"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between rounded-lg border border-[#363636] bg-[#161616] p-3",children:[e.jsx("span",{className:"text-[#eaeaea]",children:"Auto-moderate all content"}),e.jsx("button",{className:"h-6 w-12 rounded-full bg-[#2e7d32]",children:e.jsx("span",{className:"block h-5 w-5 translate-x-6 rounded-full bg-white transition-transform"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-[#b5b5b5]",children:"Membership Fee (Optional)"}),e.jsxs("div",{className:"relative mt-2",children:[e.jsx("span",{className:"absolute left-3 top-[15px] text-[#b5b5b5]",children:"$"}),e.jsx("input",{type:"number",className:"h-[50px] w-full rounded-lg border border-[#363636] bg-[#161616] pl-8 pr-4 text-[#eaeaea]",placeholder:"0.00"})]})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{className:"rounded-lg border border-[#363636] px-6 py-3 text-[#eaeaea]",children:"Back"}),e.jsx("button",{className:"rounded-lg bg-[#2e7d32] px-6 py-3 text-[#eaeaea]",children:"Create Community"})]})]})})]});export{S as default};
