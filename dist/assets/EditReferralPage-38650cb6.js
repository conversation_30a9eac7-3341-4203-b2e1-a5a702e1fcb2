import{j as e}from"./@react-google-maps/api-211df1ae.js";import{f as ye,d as ve,r as o}from"./vendor-1c28ea83.js";import{G as je,S as _,T as Ie,M as I,s as V}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const Je=()=>{console.log("%cEditReferralPage Rendering","color: #00ff00; font-weight: bold");const{id:N}=ye();console.log("%cReferral ID:","color: #00ff00; font-weight: bold",N);const U=ve(),{dispatch:Q}=o.useContext(je),[q,m]=o.useState(""),[re,ie]=o.useState(!0),[$,E]=o.useState(!1),[J,K]=o.useState([]),[Ne,se]=o.useState(""),[_e,le]=o.useState(""),[ne,ee]=o.useState([]),[oe,W]=o.useState(!1),[y,te]=o.useState(null),[B,ae]=o.useState(!1),[de,Y]=o.useState(""),[ce,F]=o.useState(""),[O,me]=o.useState([]),[i,g]=o.useState({title:"",type:"",industry:"",description:"",know_client:"",deal_size:"",referral_fee:"",payment_method:"",referral_type:"",community:"",direct_person:"",additional_notes:"",description_image:"",requirements:"",expiration_date:""}),ue=async()=>{try{const r=await new I().callRawAPI("/v1/api/dealmaker/industries",{},"GET");return!r.error&&r.data?(me(r.data),r.data):(m(r.message||"Failed to load industries"),[])}catch(a){return m(a.message||"Failed to load industries"),[]}};o.useEffect(()=>{(async()=>{var r,n,u,h,v,w,D,C,S,k,Z,T,H,L,M,R,z,A,j;if(console.log("%cInitializing Data","color: #ff00ff; font-weight: bold"),!N){console.log("No ID provided");return}try{console.log("%cFetching Referral Data","color: #ff00ff; font-weight: bold");const s=new I,P=await ue(),l=await s.GetReferralDetail(N);if(console.log("%cAPI Response:","color: #ff00ff; font-weight: bold",l),!l.error&&l.model){const t=l.model;console.log("%cReferral Model:","color: #ff00ff; font-weight: bold",t);const x=((r=t.industry_name)==null?void 0:r.value)||"";let f="";if(x&&P.length>0){const p=P.find(G=>G.name===x);f=p?p.id:""}const c={title:((n=t.title)==null?void 0:n.value)||"",type:((u=t.type)==null?void 0:u.value)||"",industry:f||"",description:((h=t.description)==null?void 0:h.value)||"",know_client:((v=t.know_client)==null?void 0:v.value)||"",deal_size:((w=t.deal_size)==null?void 0:w.value)||"",referral_fee:((D=t.referral_fee)==null?void 0:D.value)||"",payment_method:((C=t.payment_method)==null?void 0:C.value)||"",referral_type:((S=t.referral_type)==null?void 0:S.value)||"",community:((Z=(k=t.community)==null?void 0:k.id)==null?void 0:Z.value)||"",direct_person:((H=(T=t.direct_person)==null?void 0:T.id)==null?void 0:H.value)||"",additional_notes:((L=t.additional_notes)==null?void 0:L.value)||"",description_image:((M=t.description_image)==null?void 0:M.value)||t.description_image||"",requirements:((R=t.requirements)==null?void 0:R.value)||"",expiration_date:((z=t.expiration_date)==null?void 0:z.value)||""};console.log("%cSetting Form Data:","color: #ff00ff; font-weight: bold",c),g(c),te(c);const d=((A=t.description_image)==null?void 0:A.value)||t.description_image;d&&typeof d=="string"&&(console.log("%cSetting Image Preview:","color: #ff00ff; font-weight: bold",d),Y(d),F(d.split("/").pop())),await pe(),((j=t.referral_type)==null?void 0:j.value)==="direct"&&await X()}}catch(s){console.error("%cError Loading Data:","color: #ff0000; font-weight: bold",s),m(s.message||"Failed to load referral data")}finally{ie(!1)}})()},[N]),o.useEffect(()=>{J.length===0&&K([{id:{value:1},title:{value:"Rain Maker LLC"}}])},[J]);const pe=async()=>{var a;try{const n=await new I().GetJoinedCommunities();n.error||(K(n.list||[]),((a=n.list)==null?void 0:a.length)===0&&K([{id:{value:1},title:{value:"Rain Maker LLC"}}]))}catch(r){console.error("Failed to load communities:",r)}},ge=async a=>{try{const n=await new I().GetCommunityUsers(a);n.error||ee(n.list||[])}catch(r){console.error("Failed to fetch users:",r),m(r.message||"Failed to fetch users")}},be=a=>{const r=a.target.value;le(r),i.community=r,i.referral_type==="direct referral"&&(W(!0),ge(r),W(!1))},X=async()=>{W(!0);try{const r=await new I().GetCommunityMembers(i.community);r.error||ee(r.list||[])}catch(a){console.error("Failed to load users:",a)}finally{W(!1)}},b=a=>{const{name:r,value:n}=a.target;g(u=>({...u,[r]:n})),r==="referral_type"&&n==="direct"?i.community&&X():r==="community"&&i.referral_type==="direct"&&X()},he=async a=>{var r,n,u,h,v,w,D,C,S,k,Z,T,H,L,M,R,z,A;a.preventDefault(),E(!0),m("");try{if(!i.title){m("Title is required"),E(!1);return}if(i.expiration_date){const l=new Date(i.expiration_date),t=new Date;if(t.setHours(0,0,0,0),l<t){m("Expiration date cannot be in the past"),E(!1);return}}const j=new I,s={};if(Object.entries(i).forEach(([l,t])=>{var x,f;if(l==="description_image"){const c=((x=y[l])==null?void 0:x.value)||y[l];t!==c&&(s[l]=t)}else{const c=((f=y[l])==null?void 0:f.value)||y[l];if(l==="industry"){const d=parseInt(t,10),p=parseInt(c,10);if(!isNaN(d)&&!isNaN(p))d!==p&&(s[l]=t);else{const G=String(t||""),fe=String(c||"");G!==fe&&(s[l]=t)}}else{const d=String(t||""),p=String(c||"");d!==p&&(s[l]=t)}}}),console.log("Original Data:",y),console.log("Form Data:",i),console.log("Update Data:",s),console.log("Industry Debug:"),console.log("- Original industry:",y.industry),console.log("- Current industry:",i.industry),console.log("- Industry in updateData:",s.industry),Object.keys(s).length===0&&(i.industry&&i.industry!==""&&i.industry!==y.industry&&(s.industry=i.industry,console.log("Fallback: Added industry to updateData:",i.industry)),Object.keys(s).length===0)){U("/member/referrals",{state:{activeTab:"my-referrals"}});return}console.log("Checking if updateData.industry exists:",s.industry),console.log("updateData before conversion:",JSON.stringify(s)),s.industry?(s.industry_id=parseInt(s.industry,10),delete s.industry,console.log("Converted industry to industry_id:",s.industry_id),console.log("Final updateData after conversion:",JSON.stringify(s))):console.log("No industry field found in updateData"),console.log("Final updateData before API call:",s);const P=await j.UpdateReferral(N,s);if(P.error)m(P.message),V(Q,"Failed to update referral",5e3,"error");else{const l=await j.GetReferralDetail(N);if(!l.error&&l.model){const t=l.model,x=((r=t.industry_name)==null?void 0:r.value)||"";let f="";if(x&&O.length>0){const p=O.find(G=>G.name===x);f=p?p.id:""}const c={title:((n=t.title)==null?void 0:n.value)||"",type:((u=t.type)==null?void 0:u.value)||"",industry:f||"",description:((h=t.description)==null?void 0:h.value)||"",know_client:((v=t.know_client)==null?void 0:v.value)||"",deal_size:((w=t.deal_size)==null?void 0:w.value)||"",referral_fee:((D=t.referral_fee)==null?void 0:D.value)||"",payment_method:((C=t.payment_method)==null?void 0:C.value)||"",referral_type:((S=t.referral_type)==null?void 0:S.value)||"",community:((Z=(k=t.community)==null?void 0:k.id)==null?void 0:Z.value)||"",direct_person:((H=(T=t.direct_person)==null?void 0:T.id)==null?void 0:H.value)||"",additional_notes:((L=t.additional_notes)==null?void 0:L.value)||"",description_image:((M=t.description_image)==null?void 0:M.value)||t.description_image||"",requirements:((R=t.requirements)==null?void 0:R.value)||"",expiration_date:((z=t.expiration_date)==null?void 0:z.value)||""};g(c),te(c);const d=((A=t.description_image)==null?void 0:A.value)||t.description_image;d&&typeof d=="string"&&(Y(d),F(d.split("/").pop()))}V(Q,"Referral updated successfully!",5e3,"success"),U("/member/referrals",{state:{activeTab:"my-referrals"}})}}catch(j){m(j.message||"Failed to update referral"),V(Q,"Failed to update referral",5e3,"error")}finally{E(!1)}},xe=async a=>{const r=a.target.files[0];if(r)try{ae(!0);const n=new I,u=new FormData;u.append("file",r);const h=await n.uploadImage(u);h.url&&(g(v=>({...v,description_image:h.url})),Y(URL.createObjectURL(r)),F(r.name))}catch(n){console.error("Error uploading attachment:",n),m("Failed to upload attachment")}finally{ae(!1)}};return e.jsxs("div",{style:{width:"600px",margin:"20px auto"},className:"min-h-screen bg-[#161616] p-6",children:[e.jsx("style",{children:`
          input[type="date"]::-webkit-calendar-picker-indicator {
            filter: invert(1);
            cursor: pointer;
          }
          input[type="date"]::-webkit-calendar-picker-indicator:hover {
            filter: invert(1) brightness(1.2);
          }
        `}),e.jsxs("div",{className:"mx-auto max-w-3xl",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold text-[#eaeaea]",children:"Edit Referral"}),e.jsxs("div",{className:"bg-[#161616] rounded-xl p-6 mb-8",children:[re?e.jsxs("div",{className:"space-y-4",children:[e.jsx(_,{height:10,width:"30%"}),e.jsx(_,{height:40}),e.jsx(_,{height:10,width:"30%"}),e.jsx(_,{height:40}),e.jsx(_,{height:10,width:"30%"}),e.jsx(_,{height:120})]}):e.jsxs("form",{onSubmit:he,className:"space-y-5",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Title *"}),e.jsx("input",{type:"text",name:"title",value:i.title,onChange:b,placeholder:"Enter referral title",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Type of Opportunity"}),e.jsxs("select",{value:i.type,onChange:a=>g(r=>({...r,type:a.target.value})),className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select type"}),e.jsx("option",{value:"looking_for_service",children:"Looking for Service"}),e.jsx("option",{value:"looking_for_product",children:"Looking for Product"}),e.jsx("option",{value:"looking_for_buyer",children:"Looking for Buyer"}),e.jsx("option",{value:"looking_for_investor",children:"Looking for Investor"}),e.jsx("option",{value:"looking_for_partner",children:"Looking for Partner"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Industry"}),e.jsxs("select",{value:i.industry,onChange:a=>g(r=>({...r,industry:a.target.value})),className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select industry"}),O.map(a=>e.jsx("option",{value:a.id,children:a.name},a.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Description"}),e.jsxs("div",{className:"relative",children:[e.jsx("textarea",{name:"description",value:i.description,onChange:b,placeholder:"Provide details about the opportunity",rows:4,className:"w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea] placeholder-[#666]"}),e.jsxs("div",{className:"absolute bottom-4 right-4 flex items-center gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"file",onChange:xe,className:"hidden",id:"attachment-input",accept:"image/*,.pdf,.doc,.docx",disabled:B}),e.jsx("button",{type:"button",onClick:()=>document.getElementById("attachment-input").click(),className:`text-[#b5b5b5] hover:text-[#eaeaea] ${B?"opacity-50 cursor-not-allowed":""}`,disabled:B,children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M17.5 9.375L11.25 15.625C10.2083 16.6667 8.89583 17.1875 7.3125 17.1875C5.72917 17.1875 4.41667 16.6667 3.375 15.625C2.33333 14.5833 1.8125 13.2708 1.8125 11.6875C1.8125 10.1042 2.33333 8.79167 3.375 7.75L10.9375 0.1875C11.6458 -0.520833 12.5104 -0.875 13.5312 -0.875C14.5521 -0.875 15.4167 -0.520833 16.125 0.1875C16.8333 0.895833 17.1875 1.76042 17.1875 2.78125C17.1875 3.80208 16.8333 4.66667 16.125 5.375L8.5625 12.9375C8.20833 13.2917 7.78125 13.4688 7.28125 13.4688C6.78125 13.4688 6.35417 13.2917 6 12.9375C5.64583 12.5833 5.46875 12.1562 5.46875 11.6562C5.46875 11.1562 5.64583 10.7292 6 10.375L12.8125 3.5625",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),B&&e.jsxs("div",{className:"absolute -top-1 -right-1 h-2 w-2",children:[e.jsx("div",{className:"animate-ping absolute h-full w-full rounded-full bg-[#7dd87d] opacity-75"}),e.jsx("div",{className:"rounded-full h-full w-full bg-[#7dd87d]"})]})]}),e.jsx("button",{type:"button",className:"text-[#b5b5b5] hover:text-[#eaeaea]",onClick:()=>{}})]})]}),de&&e.jsxs("div",{className:"mt-2 flex items-center gap-2 rounded-lg border border-[#363636] bg-[#1a1a1a] p-2",children:[e.jsx("div",{className:"flex-1 truncate text-sm text-[#eaeaea]",children:ce}),e.jsx("button",{type:"button",onClick:()=>{g(a=>({...a,description_image:""})),Y(""),F("")},className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),e.jsx("div",{className:"grid grid-cols-1 gap-4",children:e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Estimated Deal Size"}),e.jsx("input",{type:"text",name:"deal_size",value:i.deal_size,onChange:b,placeholder:"e.g., $500",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]"})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Referral Fee (%)"}),e.jsx("input",{type:"text",name:"referral_fee",value:i.referral_fee,onChange:b,placeholder:"Enter percentage",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Payment Method"}),e.jsxs("select",{name:"payment_method",value:i.payment_method,onChange:b,className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select method"}),e.jsx("option",{value:"bank",children:"Bank Transfer"}),e.jsx("option",{value:"bank",children:"Bank Card"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Referral Type"}),e.jsxs("select",{value:i.referral_type,onChange:a=>(se(a.target.value),g(r=>({...r,referral_type:a.target.value}))),className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select type"}),e.jsx("option",{value:"open referral",children:"Open Referral"}),e.jsx("option",{value:"community referral",children:"Community Referral"}),e.jsx("option",{value:"direct referral",children:"Direct Referral"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx("div",{children:(i.referral_type==="community referral"||i.referral_type==="direct referral")&&e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Community"}),e.jsxs("select",{name:"community",value:i.community,onChange:a=>be(a),className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]",required:!0,children:[e.jsx("option",{value:"",disabled:!0,children:"Select community"}),J.map(a=>e.jsx("option",{value:a.id.value,children:a.title.value},a.id.value))]})]})}),i.referral_type==="direct referral"&&e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Direct Person"}),oe?e.jsx("div",{className:"h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] placeholder-[#666]",children:e.jsx("div",{className:"flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-[#eaeaea]",style:{marginTop:"8px"}})})}):e.jsxs("select",{name:"direct_person",value:i.direct_person,onChange:b,className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"",children:"Select person"}),ne.map(a=>e.jsx("option",{value:a.id.value,children:a.name.value},a.id.value))]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Expiration Date"}),e.jsx("input",{type:"date",name:"expiration_date",value:i.expiration_date,onChange:b,className:"h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]",min:new Date().toISOString().split("T")[0]}),e.jsx("p",{className:"mt-1 text-xs text-[#666]",children:"The opportunity will expire on this date. Leave blank for no expiration."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Additional Notes"}),e.jsx("textarea",{name:"additional_notes",value:i.additional_notes,onChange:b,placeholder:"Any other information that might be helpful",rows:3,className:"w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea] placeholder-[#666]"})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{type:"button",onClick:()=>U("/member/referrals",{state:{activeTab:"my-referrals"}}),className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#363636]",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:$,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#2e7d32]/90",children:$?"Saving...":"Save Changes"})]})]}),q&&e.jsx(Ie,{message:q})]})]})]})};export{Je as default};
