import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as o,d as D}from"./vendor-1c28ea83.js";import{M as b,A as M,G as A,L as x,E as C,c as R,J as I,K as L}from"./index-b3edd152.js";import{M as _}from"./index-0cdf3a8c.js";import{M as P}from"./index-db36e1ef.js";/* empty css                              */import{A as F}from"./index.esm-2d1feecf.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let j=new b;const Z=[{header:"ID",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Opportunity Title",accessor:"title",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Date Posted",accessor:"created_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Posted By",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:d=>({full_time:"full_time",part_time:"part_time",contract:"contract",freelance:"contract",internship:"part_time"})[d]||d},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:d=>`<span class="${{completed:"status completed",active:"status active",deleted:"status deleted"}[d]||"status"}">${d}</span>`},{header:"Action",accessor:""}],me=()=>{o.useContext(M),o.useContext(A);const d=D(),[u,h]=o.useState(!1),[v,p]=o.useState(!1),[S,E]=o.useState();o.useState("");const w=o.useRef(null),[y,N]=o.useState([]),[f,T]=o.useState({total:0,active:0,completed:0}),n=async()=>{try{const s=await new b().callRawAPI("/v1/api/dealmaker/super_admin/dashboard",{},"GET");!s.error&&s.model&&T({total:s.model.total_referrals||0,active:s.model.total_active_referrals||0,completed:s.model.total_completed_referrals||0})}catch(t){console.error("Error fetching dashboard stats:",t)}};o.useEffect(()=>{n()},[]),o.useEffect(()=>{const t=s=>{var a;((a=s.detail)==null?void 0:a.type)==="REFRESH_DATA"&&w.current&&(console.log("Refreshing table data...",s.detail),r(i=>({...i,loading:!0})),(async()=>{try{console.log("Fetching fresh data..."),await new Promise(i=>setTimeout(i,500)),r(i=>({...i,loading:!1})),n(),console.log("Data refreshed successfully")}catch(i){console.error("Error refreshing data:",i),r(g=>({...g,loading:!1}))}})())};return window.addEventListener("globalStateChange",t),()=>{window.removeEventListener("globalStateChange",t)}},[]);const[c,r]=o.useState({data:[{id:91,title:"Umer_test_test",created_at:"2025-05-06T18:02:27.000Z",user_id:2,type:"full_time",status:"completed"},{id:90,title:"ffff",created_at:"2025-05-06T17:51:03.000Z",user_id:27,type:"contract",status:"active"},{id:89,title:"umer_123",created_at:"2025-05-05T21:08:51.000Z",user_id:2,type:"part_time",status:"completed"},{id:88,title:"test_Umer_Testttt",created_at:"2025-05-05T18:45:48.000Z",user_id:2,type:"full_time",status:"completed"},{id:87,title:"test_Umer_Test",created_at:"2025-05-05T18:39:33.000Z",user_id:2,type:"full_time",status:"completed"},{id:86,title:"asdf",created_at:"2025-05-01T20:10:37.000Z",user_id:2,type:"full_time",status:"active"},{id:85,title:"umer test",created_at:"2025-04-29T15:53:11.000Z",user_id:2,type:"full_time",status:"active"},{id:84,title:"referal to test",created_at:"2025-04-25T18:15:37.000Z",user_id:32,type:"full_time",status:"active"},{id:83,title:"Adulthood na scam!",created_at:"2025-04-24T19:32:31.000Z",user_id:38,type:"contract",status:"active"},{id:82,title:"Marketing Internship 1",created_at:"2025-04-24T19:16:32.000Z",user_id:32,type:"part_time",status:"deleted"}],loading:!1,page:1,limit:10,pages:26,total:256}),m=async(t,s,a=[])=>{switch(t){case"add":h(s);break;case"edit":p(s),E(a[0]);break;case"delete":await k(a);break}},k=async t=>{try{console.log("Deleting item with ID:",t[0]),r(a=>({...a,loading:!0})),j.setTable("referral");const s=await j.callRestAPI({id:t[0]},"DELETE");console.log("Delete result:",s),s!=null&&s.error?r(a=>({...a,loading:!1})):(r(a=>({...a,data:a.data.filter(l=>l.id!==t[0]),loading:!1})),n())}catch(s){console.error("Error deleting item:",s),r(a=>({...a,loading:!1}))}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"opportunities-dashboard bg-[#1E1E1E]",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"header",children:[e.jsx("h1",{children:"Opportunities Dashboard"}),e.jsx("p",{children:"Track and manage your opportunities in real-time"})]}),e.jsx("div",{className:"search-add",children:e.jsxs("button",{onClick:()=>m("add",!0),className:"add-button ml-auto",children:[e.jsx("span",{children:"+"})," New Opportunity"]})}),e.jsxs("div",{className:"stats-grid",children:[e.jsx("div",{className:"stat-card",children:e.jsxs("div",{className:"stat-card-content",children:[e.jsxs("div",{children:[e.jsx("div",{className:"stat-card-title",children:"Total Opportunities"}),e.jsx("div",{className:"stat-card-value",children:f.total})]}),e.jsx("div",{className:"stat-card-icon",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})})]})}),e.jsx("div",{className:"stat-card",children:e.jsxs("div",{className:"stat-card-content",children:[e.jsxs("div",{children:[e.jsx("div",{className:"stat-card-title",children:"Active Opportunities"}),e.jsx("div",{className:"stat-card-value active",children:f.active})]}),e.jsx("div",{className:"stat-card-icon",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})]})}),e.jsx("div",{className:"stat-card",children:e.jsxs("div",{className:"stat-card-content",children:[e.jsxs("div",{children:[e.jsx("div",{className:"stat-card-title",children:"Completed Opportunities"}),e.jsx("div",{className:"stat-card-value",children:f.completed})]}),e.jsx("div",{className:"stat-card-icon",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})})]})})]}),e.jsx("div",{className:"table-container",children:e.jsx(x,{children:e.jsx(P,{columns:Z,tableRole:"admin",table:"referral",actionId:"id",searchField:"title",actions:{view:{show:!0,action:t=>{console.log("View item with ID:",t[0]),d(`/admin/view-referral/${t[0]}`)},multiple:!1,locations:["buttons"],showChildren:!1,children:"View",icon:e.jsx(F,{className:"text-blue-500"})},edit:{show:!0,multiple:!1,action:t=>m("edit",!0,t),locations:["buttons"],showChildren:!1,children:"Edit",icon:e.jsx(C,{stroke:"#4CAF50"})},delete:{show:!0,action:t=>m("delete",!0,t),multiple:!1,locations:["buttons"],showChildren:!1,children:"Delete",icon:e.jsx(R,{fill:"#E53E3E"})},select:{show:!1,action:null,multiple:!1},add:{show:!0,action:()=>m("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},selectedItems:y,setSelectedItems:N,refreshRef:w,actionPosition:["buttons"],allowSortColumns:!1,externalData:{use:!0,data:c.data,loading:c.loading,page:c.page,limit:c.limit,pages:c.pages,total:c.total,fetch:async(t,s,a)=>{console.log("Fetch called with page:",t,"limit:",s,"filter:",a),r(l=>({...l,loading:!0}));try{await new Promise(l=>setTimeout(l,500)),r(l=>({...l,loading:!1})),n(),console.log("Table data refreshed successfully")}catch(l){console.error("Error refreshing table data:",l),r(i=>({...i,loading:!1}))}},search:async(t,s,a,l)=>{console.log("Search called with:",t,l),r(i=>({...i,loading:!0}));try{await new Promise(i=>setTimeout(i,500)),r(i=>({...i,loading:!1})),n(),console.log("Search completed successfully")}catch(i){console.error("Error during search:",i),r(g=>({...g,loading:!1}))}}}})})}),e.jsxs("div",{className:"pagination",children:[e.jsx("div",{className:"pagination-info",children:"Showing 1 to 10 of 256 entries"}),e.jsxs("div",{className:"pagination-buttons",children:[e.jsx("button",{className:"pagination-button prev",children:"Previous"}),e.jsx("button",{className:"pagination-button next",children:"Next"})]})]})]})}),e.jsx(x,{children:e.jsx(_,{isModalActive:u,closeModalFn:()=>h(!1),children:e.jsx(I,{setSidebar:h})})}),v&&e.jsx(x,{children:e.jsx(_,{isModalActive:v,closeModalFn:()=>p(!1),children:e.jsx(L,{activeId:S,setSidebar:p})})})]})};export{me as default};
