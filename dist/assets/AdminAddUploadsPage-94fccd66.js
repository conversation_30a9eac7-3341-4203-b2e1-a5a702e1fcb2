import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as l,d as f}from"./vendor-1c28ea83.js";import{u as y}from"./react-hook-form-eec8b32f.js";import{G as b,A as j,o as w,M as S,s as N,t as A}from"./index-b3edd152.js";import{c as E,a as o}from"./yup-1b5612ec.js";import{M as r}from"./MkdInput-67f7082d.js";import{I as T}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const ae=({setSidebar:n})=>{const{dispatch:p}=l.useContext(b),c=E({url:o().required(),caption:o(),user_id:o(),width:o(),height:o(),type:o().required()}).required(),{dispatch:u}=l.useContext(j),[m,d]=l.useState(!1),h=f(),{register:t,handleSubmit:x,setError:C,formState:{errors:a}}=y({resolver:w(c)}),g=async s=>{d(!0);try{let i=new S;i.setTable("uploads"),(await i.callRestAPI({url:s.url,caption:s.caption,user_id:s.user_id,width:s.width,height:s.height,type:s.type},"POST")).error||(N(p,"Added"),h("/admin/uploads"),n(!1),p({type:"REFRESH_DATA",payload:{refreshData:!0}})),d(!1)}catch(i){d(!1),console.log("Error",i),A(u,i.message)}};return l.useEffect(()=>{p({type:"SETPATH",payload:{path:"uploads"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Uploads"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:x(g),children:[e.jsx(r,{type:"text",page:"add",name:"url",errors:a,label:"Url",placeholder:"Url",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"caption",errors:a,label:"Caption",placeholder:"Caption",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"user_id",errors:a,label:"User_id",placeholder:"User_id",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"width",errors:a,label:"Width",placeholder:"Width",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"height",errors:a,label:"Height",placeholder:"Height",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"type",errors:a,label:"Type",placeholder:"Type",register:t,className:""}),e.jsx(T,{type:"submit",loading:m,disabled:m,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ae as default};
