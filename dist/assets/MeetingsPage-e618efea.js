import{j as e}from"./@react-google-maps/api-211df1ae.js";import{d as Y,r as l}from"./vendor-1c28ea83.js";import{G as q,M as f,T as ee,s as N}from"./index-b3edd152.js";import{d as te}from"./@uppy/aws-s3-c23f5c86.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const w=({selected:n,logo:b,name:h,onClick:v})=>e.jsx("div",{onClick:v,className:`mb-2 mr-4 flex items-center ${n?"border-[#2e7d32] ":"border-[#363636]"} cursor-pointer transition-colors hover:border-[#2e7d32]`,children:e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx("div",{className:`h-4 w-4 rounded-full border-2 ${n?"border-[#2e7d32]":""} flex items-center justify-center`,children:n&&e.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})}),h==="Google Meet"?e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",className:"w-5 h-5",children:e.jsx("path",{d:"M15.25 8.18125C15.25 12.6031 12.2219 15.75 7.75 15.75C3.4625 15.75 0 12.2875 0 8C0 3.7125 3.4625 0.25 7.75 0.25C9.8375 0.25 11.5938 1.01562 12.9469 2.27813L10.8375 4.30625C8.07812 1.64375 2.94688 3.64375 2.94688 8C2.94688 10.7031 5.10625 12.8938 7.75 12.8938C10.8188 12.8938 11.9688 10.6938 12.15 9.55313H7.75V6.8875H15.1281C15.2 7.28437 15.25 7.66562 15.25 8.18125Z",fill:"#EAEAEA"})}):h==="Zoom"?e.jsxs("svg",{className:"w-5 h-5",xmlns:"http://www.w3.org/2000/svg","aria-label":"Zoom",role:"img",viewBox:"0 0 512 512",children:[e.jsx("rect",{width:"512",height:"512",rx:"15%",fill:"#2D8CFF"}),e.jsx("path",{fill:"#ffffff",d:"M428 357c8 2 15-2 19-8 2-3 2-8 2-19V179c0-11 0-15-2-19-3-8-11-11-19-8-21 14-67 55-68 72-.8 3-.8 8-.8 15v38c0 8 0 11 .8 15 1 8 4 15 8 19 12 9 52 45 61 45zM64 187c0-15 0-23 3-27 2-4 8-8 11-11 4-3 11-3 27-3h129c38 0 57 0 72 8 11 8 23 15 30 30 8 15 8 34 8 72v68c0 15 0 23-3 27-2 4-8 8-11 11-4 3-11 3-27 3H174c-38 0-57 0-72-8-11-8-23-15-30-30-8-15-8-34-8-72z"})]}):e.jsxs("svg",{className:"w-5 h-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 2228.833 2073.333",children:[e.jsx("path",{fill:"#EAEAEA",d:"M1554.637,777.5h575.713c54.391,0,98.483,44.092,98.483,98.483c0,0,0,0,0,0v524.398 c0,54.391-44.092,98.483-98.483,98.483c0,0,0,0,0,0h-575.713c-54.391,0-98.483-44.092-98.483-98.483c0,0,0,0,0,0V875.983 C1456.154,821.592,1500.246,777.5,1554.637,777.5z M1943.783,1016.499l-283.33,186.265v-372.53L1943.783,1016.499z"}),e.jsx("path",{fill:"#EAEAEA",d:"M1014.637,777.5h575.713c54.391,0,98.483,44.092,98.483,98.483c0,0,0,0,0,0v524.398 c0,54.391-44.092,98.483-98.483,98.483c0,0,0,0,0,0h-575.713c-54.391,0-98.483-44.092-98.483-98.483c0,0,0,0,0,0V875.983 C916.154,821.592,960.246,777.5,1014.637,777.5z"})]}),e.jsx("span",{className:"text-[#eaeaea]",children:h})]})}),se=({email:n,onRemove:b})=>(console.log(n,"Adding Attendee"),e.jsxs("div",{className:"inline-flex items-center gap-1 rounded bg-[#2e7d32] px-2 py-1 text-sm text-white",children:[n,e.jsx("button",{onClick:b,className:"text-white hover:text-red-300",children:"×"})]})),De=()=>{Y();const[n,b]=l.useState(""),[h,v]=l.useState(!0),[m,L]=l.useState([]),[ae,F]=l.useState([]),[le,z]=l.useState([]),[ie,V]=l.useState([]),[Z,y]=l.useState(!1),{dispatch:j}=l.useContext(q),[i,d]=l.useState({calendar:"Google Calendar",platform:"Google Meet",title:"",date:"",time:"",attendees:[]}),[$,k]=l.useState(!1),[C,M]=l.useState(""),[o,A]=l.useState([]),[S,H]=l.useState([]),[I,D]=l.useState(!1),[p,P]=l.useState(5),[R,E]=l.useState(null),[B,T]=l.useState(!1);l.useEffect(()=>{U()},[]);const U=async()=>{try{const t=new f,[s,a,c]=await Promise.all([t.GetUpcomingMeetings(),t.GetDashboardStats(),t.GetCommunityUpdates()]);if(!s.error&&!a.error&&!c.error){const{notes:x=[],activities:r=[]}=a.data;L(s.list||[]),z(x),F(r.slice(0,5)),V(c.updates.slice(0,5)||[])}}catch(t){b(t.message||"Failed to load data")}finally{v(!1)}},u=(t,s=!1)=>{if(!t)return"";const a=new Date(t);if(s)return a.toLocaleString("default",{month:"short",day:"numeric"}).replace(/(\d{2}) (\d{2})/,"$1/$2").split(" ").reverse().join(" ");if(typeof t=="object"&&t.date&&t.time)return new Date(`${t.date.value}T${t.time.value}`).toLocaleString("default",{weekday:"short",month:"short",day:"numeric",hour:"numeric",minute:"numeric",hour12:!0});if(t.includes("T"))return a.toLocaleString("default",{weekday:"short",month:"short",day:"numeric",hour:"numeric",minute:"numeric",hour12:!0});const c=i.time;if(c){const[x,r]=c.split(":");a.setHours(parseInt(x,10),parseInt(r,10))}return a.toLocaleString("default",{weekday:"short",month:"short",day:"numeric",hour:"numeric",minute:"numeric",hour12:!0})},O=()=>{y(!0)},J=(t,s)=>{console.log(t,"Adding Attendee 1"),!o.some(a=>a.email===t)&&t.trim()!==""&&(A([...o,{email:t,id:s}]),console.log(o,"Adding Attendee")),M(""),k(!1)},K=t=>{A(o.filter(s=>s.email!==t))},W=l.useCallback(te(async t=>{if(!(t.trim().length<2))try{D(!0);const a=await new f().SearchUsers(t);a.error||(H(a.list||[]),D(!1))}catch(s){console.error("Failed to search users:",s)}finally{}},300),[]),Q=async t=>{t.preventDefault();const{date:s,time:a}=i;let c=null;if(s&&a){const[r,g]=a.split(":"),_=new Date(s);_.setHours(parseInt(r,10),parseInt(g,10)),c=_.toISOString()}console.log("Form submitted with:",{...i,dateTime:c,attendees:o});const x=new f;try{const r=await x.CreateMeeting({...i,dateTime:c,attendees:o.map(g=>g.id)});r.error?N(j,r.error,5e3,"error"):(y(!1),U(),N(j,r.message,5e3,"success"))}catch(r){console.error("Failed to create meeting:",r),N(j,r.message,5e3,"error")}},G=()=>{P(t=>t+5)},X=({meeting:t,onClose:s})=>t?e.jsx("div",{className:"flex fixed inset-0 z-50 justify-center items-center w-full h-full bg-black/50",children:e.jsxs("div",{className:"w-full max-w-2xl rounded-xl bg-[#161616] p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:t.title.value}),e.jsx("button",{onClick:s,className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:"×"})]}),e.jsxs("div",{className:"mt-4 mb-4 space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-[#242424] p-4",children:[e.jsx("h3",{className:"mb-4 font-medium text-[#eaeaea]",children:"Meeting Details"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-[#b5b5b5]",children:"Date & Time"}),e.jsx("span",{className:"text-[#eaeaea]",children:u({date:t.date,time:t.time})})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-[#b5b5b5]",children:"Platform"}),e.jsx("span",{className:"text-[#eaeaea]",children:t.platform.value==="google_meet"?"Google Meet":"Zoom"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-[#b5b5b5]",children:"Status"}),e.jsx("span",{className:"rounded-full bg-[#2e7d32]/20 px-2 py-1 text-xs text-[#7dd87d]",children:t.status.value})]})]})]}),e.jsxs("div",{className:"mb-4 mt-4 rounded-lg bg-[#242424] p-4",children:[e.jsx("h3",{className:"mb-4 font-medium text-[#eaeaea]",children:"Created By"}),e.jsxs("div",{className:"flex gap-3 items-center",children:[t.creator.avatar.value?e.jsx("img",{src:t.creator.avatar.value,alt:"",className:"w-10 h-10 rounded-full"}):e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-[#2e7d32] text-white",children:t.creator.name.value.charAt(0)}),e.jsx("div",{children:e.jsx("p",{className:"text-[#eaeaea]",children:t.creator.name.value})})]})]}),e.jsxs("div",{className:"rounded-lg bg-[#242424] p-4",children:[e.jsxs("h3",{className:"mb-4 font-medium text-[#eaeaea]",children:["Attendees (",t.attendees.length,")"]}),e.jsx("div",{className:"space-y-3",children:t.attendees.map(a=>e.jsxs("div",{className:"flex gap-3 items-center",children:[a.avatar.value?e.jsx("img",{src:a.avatar.value,alt:"",className:"w-8 h-8 rounded-full"}):e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white",children:a.name.value.charAt(0)}),e.jsx("p",{className:"text-[#eaeaea]",children:a.name.value})]},a.id.value))})]})]}),e.jsxs("div",{className:"flex gap-3 justify-end mt-4 mt-6 mb-4",children:[e.jsx("button",{onClick:s,className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#242424]",children:"Close"}),t.join_url.value&&e.jsx("button",{onClick:()=>window.open(t.join_url.value,"_blank"),className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Join Meeting"})]})]})}):null;return e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-6",children:[e.jsx("style",{children:`
          input[type="date"]::-webkit-calendar-picker-indicator {
            filter: invert(1);
            cursor: pointer;
          }
          input[type="date"]::-webkit-calendar-picker-indicator:hover {
            filter: invert(1) brightness(1.2);
          }
          input[type="time"]::-webkit-calendar-picker-indicator {
            filter: invert(1);
            cursor: pointer;
          }
          input[type="time"]::-webkit-calendar-picker-indicator:hover {
            filter: invert(1) brightness(1.2);
          }
        `}),Z?e.jsxs("div",{className:"",children:[e.jsx("h1",{className:"mb-4 text-2xl font-semibold text-[#eaeaea]",children:"Manage Meetings"}),e.jsx("p",{className:"mb-6 text-[#b5b5b5]",children:"Schedule and manage your meetings"}),e.jsxs("div",{className:"flex gap-6",children:[e.jsx("div",{style:{width:"65%"},className:"flex-1",children:e.jsxs("div",{className:"rounded-xl bg-[#161616] p-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold text-[#eaeaea]",children:"Schedule Meeting"}),e.jsxs("form",{onSubmit:Q,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Calendar"}),e.jsxs("select",{value:i.calendar,onChange:t=>d({...i,calendar:t.target.value}),className:"w-full rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-[#eaeaea]",children:[e.jsx("option",{value:"google_calendar",children:"Google Calendar"}),e.jsx("option",{value:"outlook_calendar",children:"Outlook Calendar"}),e.jsx("option",{value:"highlevel",children:"HighLevel"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 mt-4 block text-sm text-[#b5b5b5]",children:"Platform"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(w,{selected:i.platform==="Google Meet",name:"Google Meet",onClick:()=>d({...i,platform:"Google Meet"})}),e.jsx(w,{selected:i.platform==="Zoom",name:"Zoom",onClick:()=>d({...i,platform:"Zoom"})}),e.jsx(w,{selected:i.platform==="Microsoft Teams",name:"Microsoft Teams",onClick:()=>d({...i,platform:"Microsoft Teams"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-4 mt-4 block text-sm text-[#b5b5b5]",children:"Meeting Title"}),e.jsx("input",{type:"text",value:i.title,onChange:t=>d({...i,title:t.target.value}),placeholder:"Write title of meeting",className:"w-full rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-[#eaeaea]"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-4 mt-4 block text-sm text-[#b5b5b5]",children:"Date"}),e.jsx("input",{type:"date",value:i.date,onChange:t=>d({...i,date:t.target.value}),className:"w-full rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-[#eaeaea]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-4 mt-4 block text-sm text-[#b5b5b5]",children:"Time"}),e.jsx("input",{type:"time",value:i.time,onChange:t=>d({...i,time:t.target.value}),className:"w-full rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-[#eaeaea]"})]})]}),e.jsxs("div",{className:"relative",children:[e.jsx("label",{className:"mb-4 mt-4 block text-sm text-[#b5b5b5]",children:"Attendees"}),e.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:o.map(t=>e.jsx(se,{email:t.email,id:t.id,onRemove:()=>K(t.email)},t.email))}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"email",value:C,onChange:t=>{const s=t.target.value;M(s),k(!0),W(s)},placeholder:"Add attendee email",className:"w-full rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-[#eaeaea]"}),$&&C.trim()!==""&&e.jsx("div",{className:"absolute z-10 mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] py-2 shadow-lg",children:I?e.jsx("div",{className:"px-4 py-2 text-[#b5b5b5]",children:"Searching..."}):S.length>0?S.map(t=>e.jsx("div",{onClick:()=>J(t.email.value,t.id.value),className:"cursor-pointer px-4 py-2 text-[#eaeaea] hover:bg-[#2e7d32]",children:e.jsxs("div",{className:"flex gap-2 items-center",children:[t.avatar.value?e.jsx("img",{src:t.avatar.value,alt:"",className:"w-6 h-6 rounded-full"}):e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white",children:t.name.value.charAt(0)}),e.jsxs("div",{children:[e.jsx("div",{children:t.name.value}),e.jsx("div",{className:"text-sm text-[#b5b5b5]",children:t.email.value})]})]})},t.id.value)):e.jsx("div",{className:"px-4 py-2 text-[#b5b5b5]",children:"No users found"})})]})]}),e.jsx("button",{type:"submit",className:"mt-4 w-full rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Send Invite"})]})]})}),e.jsx("div",{style:{width:"35%"},className:"",children:e.jsxs("div",{className:"rounded-xl bg-[#161616] p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:"Upcoming Meetings"}),e.jsxs("label",{className:"text-xs text-[#b5b5b5]",children:[e.jsx("input",{type:"checkbox",className:"mr-2 h-3 w-3 text-[#b5b5b5]"}),"Next 30 days"]})]}),e.jsx("div",{className:"space-y-4",children:m.slice(0,p).map(t=>e.jsxs("div",{className:"flex items-center justify-between rounded-lg bg-[#242424] p-4",children:[e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-[#2e7d32] text-white",children:t.title.value.charAt(0)}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-[#eaeaea]",children:t.title.value}),e.jsx("p",{className:"text-xs text-[#b5b5b5]",children:u({date:t.date,time:t.time})})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsx("button",{onClick:()=>window.open(t.join_url.value,"_blank"),className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Join"})})]},t.id.value))}),m.length>p&&e.jsx("button",{onClick:G,className:"mt-4 w-full rounded-lg border border-[#363636] py-2 text-sm text-[#eaeaea] hover:bg-[#242424]",children:"View More"})]})})]})]}):e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-semibold text-[#eaeaea]",children:"Upcoming Events"}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:["Upcoming: ",u(new Date,!0)," -"," ",u(new Date(new Date().setMonth(new Date().getMonth()+1)),!0)," ","· ",m.length," meetings"]}),e.jsx("p",{className:"text-sm text-[#7dd87d]",children:"Check 1 month of meetings from today"})]}),e.jsx("button",{onClick:O,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"+ New Meeting"})]}),e.jsxs("div",{className:"flex gap-6",children:[e.jsx("div",{style:{width:"65%"},className:"flex-1",children:e.jsx("div",{className:"mb-6 rounded-xl bg-[#161616] p-6",children:h?e.jsx("div",{className:"space-y-4",children:[...Array(3)].map((t,s)=>e.jsxs("div",{className:"animate-pulse rounded-lg bg-[#242424] p-4",children:[e.jsx("div",{className:"h-6 w-48 rounded bg-[#363636]"}),e.jsx("div",{className:"mt-2 h-4 w-32 rounded bg-[#363636]"})]},s))}):m.length>0?e.jsxs("div",{className:"space-y-4",children:[m.slice(0,p).map(t=>e.jsxs("div",{className:"flex cursor-pointer items-center justify-between rounded-lg bg-[#242424] p-4 hover:bg-[#2e2e2e]",onClick:()=>{E(t),T(!0)},children:[e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-[#2e7d32] text-white",children:t.title.value.charAt(0)}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm font-medium text-[#eaeaea]",children:[t.title.value," ",t.platform&&t.platform.value&&`(${t.platform.value})`]}),e.jsx("p",{className:"text-xs text-[#b5b5b5]",children:u({date:t.date,time:t.time})})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsx("button",{onClick:s=>{s.stopPropagation(),window.open(t.join_url.value,"_blank")},className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Join"})})]},t.id.value)),m.length>p&&e.jsx("button",{onClick:G,className:"mt-4 w-full rounded-lg border border-[#363636] py-2 text-sm text-[#eaeaea] hover:bg-[#242424]",children:"View More"})]}):e.jsx("p",{className:"text-center text-[#b5b5b5]",children:"No upcoming meetings"})})}),e.jsx("div",{style:{width:"35%"},className:"space-y-6 w-80"})]})]}),n&&e.jsx(ee,{message:n}),B&&e.jsx(X,{meeting:R,onClose:()=>{T(!1),E(null)}})]})};export{De as default};
