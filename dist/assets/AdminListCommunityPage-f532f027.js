import{j as e}from"./@react-google-maps/api-211df1ae.js";import{d as v,R as c,r as w}from"./vendor-1c28ea83.js";import{L as f,E as S,c as j,u as E,v as A,M as D}from"./index-b3edd152.js";import{M as g}from"./index-0cdf3a8c.js";import{M}from"./index-db36e1ef.js";import{A as N}from"./index.esm-2d1feecf.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const C=[{header:"ID",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Community Name",accessor:"title",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0},{header:"Created By",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:(o,l)=>{try{if(l.user_details){const n=JSON.parse(l.user_details);return`${n.first_name} ${n.last_name}`}}catch(n){console.error("Error parsing user details",n)}return`User ${o}`}},{header:"Admins",accessor:"admin_count",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:()=>Math.floor(Math.random()*5)+1},{header:"Moderators",accessor:"moderator_count",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:()=>Math.floor(Math.random()*8)+1},{header:"Monthly Fee",accessor:"subscription_fee",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:o=>parseFloat(o)>0?`$${parseFloat(o).toFixed(2)}`:"Free"},{header:"Affiliate",accessor:"privacy_settings",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:o=>{try{if(o)return JSON.parse(o).enable_affiliate===!0?'<span class="yes-text">Yes</span>':'<span class="no-text">No</span>'}catch(l){console.error("Error parsing privacy settings",l)}return'<span class="no-text">No</span>'}}],ae=()=>{const o=v(),[l,n]=c.useState(!1),[h,m]=c.useState(!1),[b,y]=c.useState();c.useState("");const x=w.useRef(null);c.useState({total:156,public:89,private:67});const[r,d]=c.useState({data:[{id:56,title:"test_test",description:"asdfasfd",description_image:null,logo:null,industry_id:2,has_paid:1,guidelines:"<p>asdfasddf</p>",metadata:null,privacy:"private",user_id:2,user_details:'{"first_name":"Ad","last_name":"Smith","linkedin_profile":"","website_url":"","additional_info":""}',created_at:"2025-05-01T13:37:59.000Z",updated_at:"2025-05-01T13:37:59.000Z",privacy_settings:'{"who_can_invite":"everyone","activity_visibility":"public","who_can_post":"everyone","view_list":"members_only","who_can_find":"hidden","who_can_join":"invite_only","who_can_see_content":"members_only","content_moderation":"admin_approval","enable_affiliate":false,"subscription_fee":"01234"}',subscription_fee:"1234.00"},{id:59,title:"Deal Maker LLC",description:"A community for deal makers",description_image:null,logo:null,industry_id:3,has_paid:1,guidelines:"<p>Follow the rules</p>",metadata:null,privacy:"private",user_id:28,user_details:'{"first_name":"John","last_name":"Doe","linkedin_profile":"","website_url":"","additional_info":""}',created_at:"2025-05-05T14:22:18.000Z",updated_at:"2025-05-05T14:22:18.000Z",privacy_settings:'{"who_can_invite":"everyone","activity_visibility":"public","who_can_post":"everyone","view_list":"everyone","who_can_find":"everyone","who_can_join":"anyone","who_can_see_content":"everyone","content_moderation":"admin_approval","enable_affiliate":true,"subscription_fee":"99.99"}',subscription_fee:"99.99"}],loading:!1,page:1,limit:10,pages:16,total:156}),p=async(t,s,a=[])=>{switch(t){case"add":n(s);break;case"edit":m(s),y(a[0]);break;case"delete":await _(a);break}},_=async t=>{try{console.log("Deleting community with ID:",t[0]),d(i=>({...i,loading:!0}));const s=new D;s.setTable("community");const a=await s.callRestAPI({id:t[0]},"DELETE");console.log("Delete result:",a),a!=null&&a.error?d(i=>({...i,loading:!1})):d(i=>({...i,data:i.data.filter(u=>u.id!==t[0]),loading:!1}))}catch(s){console.error("Error deleting community:",s),d(a=>({...a,loading:!1}))}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"communities-dashboard bg-[#1E1E1E]",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"header",children:[e.jsx("h1",{children:"Communities Dashboard"}),e.jsx("p",{children:"Monitor, manage, and track community engagement"})]}),e.jsx("div",{className:"search-add",children:e.jsxs("button",{onClick:()=>p("add",!0),className:"add-button ml-auto",children:[e.jsx("span",{children:"+"})," Add Community"]})}),e.jsx("div",{className:"table-container",children:e.jsx(f,{children:e.jsx(M,{columns:C,tableRole:"admin",table:"community",actionId:"id",searchField:"title",actions:{view:{show:!0,action:t=>{o(`/admin/view-community/${t[0]}`)},multiple:!1,locations:["buttons"],showChildren:!1,children:"View",icon:e.jsx(N,{className:"text-blue-500"})},edit:{show:!0,multiple:!1,action:t=>p("edit",!0,t),locations:["buttons"],showChildren:!1,children:"Edit",icon:e.jsx(S,{stroke:"#4CAF50"})},delete:{show:!0,action:_,multiple:!1,locations:["buttons"],showChildren:!1,children:"Delete",icon:e.jsx(j,{fill:"#E53E3E"})},select:{show:!1,action:null,multiple:!1},add:{show:!0,action:()=>p("add",!0),multiple:!1,children:"Add Community",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPosition:["buttons"],refreshRef:x,externalData:{use:!0,data:r.data,loading:r.loading,page:r.page,limit:r.limit,pages:r.pages,total:r.total,fetch:(t,s,a)=>{console.log("Fetch called with page:",t,"limit:",s,"filter:",a),setTimeout(()=>{d(i=>({...i,loading:!1}))},500)},search:(t,s,a,i)=>{console.log("Search called with:",t,i),setTimeout(()=>{d(u=>({...u,loading:!1}))},500)}}})})}),e.jsxs("div",{className:"pagination",children:[e.jsx("div",{className:"pagination-info",children:"Showing 1-2 of 156 communities"}),e.jsxs("div",{className:"pagination-buttons",children:[e.jsx("button",{className:"pagination-button prev",children:"Previous"}),e.jsx("button",{className:"pagination-button next",children:"Next"})]})]})]})}),e.jsx(f,{children:e.jsx(g,{isModalActive:l,closeModalFn:()=>n(!1),children:e.jsx(E,{setSidebar:n})})}),h&&e.jsx(f,{children:e.jsx(g,{isModalActive:h,closeModalFn:()=>m(!1),children:e.jsx(A,{activeId:b,setSidebar:m})})})]})};export{ae as default};
