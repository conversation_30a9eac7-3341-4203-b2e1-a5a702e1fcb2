import{j as s}from"./@react-google-maps/api-211df1ae.js";import{R as l,f as n}from"./vendor-1c28ea83.js";import"./yup-1b5612ec.js";import{M as h,G as c,t as j,S as N}from"./index-b3edd152.js";import"./pdf-lib-623decea.js";import"./react-toggle-58b0879a.js";/* empty css                 */import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./@uppy/dashboard-3a4b1704.js";let r=new h;const O=()=>{l.useContext(c);const{dispatch:x}=l.useContext(c),[e,d]=l.useState({}),[t,i]=l.useState(!0),m=n();return l.useEffect(function(){(async function(){try{i(!0),r.setTable("user");const a=await r.callRestAPI({id:Number(m==null?void 0:m.id),join:""},"GET");a.error||(d(a.model),i(!1))}catch(a){i(!1),console.log("error",a),j(x,a.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:t?s.jsx(N,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View User"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Oauth"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.oauth})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Role"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.role})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"First Name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.first_name})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Last Name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.last_name})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Email"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.email})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Password"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.password})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Type"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.type})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Verify"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.verify})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Phone"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.phone})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Photo"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.photo})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Refer"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.refer})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Stripe Uid"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.stripe_uid})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Paypal Uid"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.paypal_uid})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Two Factor Authentication"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.two_factor_authentication})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})})]})})};export{O as default};
