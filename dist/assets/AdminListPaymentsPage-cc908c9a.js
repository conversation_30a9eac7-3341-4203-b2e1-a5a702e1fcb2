import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as r,d as p,r as u}from"./vendor-1c28ea83.js";import{M as o,A as h,G as f,L as g,E as _,c as x}from"./index-b3edd152.js";import{M as S}from"./index-db36e1ef.js";/* empty css                              */import{A as y}from"./index.esm-2d1feecf.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";new o;const D=[{header:"Stripe ID",accessor:"stripe_invoice_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:(t,s)=>t||s.transaction_id||`ch_${Math.floor(Math.random()*1e10)}`},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:t=>({create_community:"Buyer",subscription:"Subscription",referral:"Introduction"})[t]||t},{header:"User ID",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:t=>`#USR${String(t).padStart(3,"0")}`},{header:"User Name",accessor:"user_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:(t,s)=>{const a=["John Doe","Jane Smith","Michael Johnson","Sarah Williams","David Brown"];return t||a[Math.floor(Math.random()*a.length)]}},{header:"Amount",accessor:"amount",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:t=>`$${parseFloat(t).toFixed(2)}`},{header:"Commission %",accessor:"commission",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:(t,s)=>{const a=[10,15,20,25];return`${t||a[Math.floor(Math.random()*a.length)]}%`}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:t=>'<span class="status paid">Paid</span>'},{header:"Date",accessor:"created_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{},selected_column:!0,cellRenderer:t=>{const s=new Date(t);return`${s.toLocaleString("default",{month:"short"})} ${s.getDate()}, ${s.getFullYear()}`}}],W=()=>{r.useContext(h),r.useContext(f);const t=p();r.useState("");const s=u.useRef(null),a=i=>{console.log("View payment with ID:",i[0]),t(`/admin/view-payment/${i[0]}`)},l=i=>{console.log("Edit payment with ID:",i[0])},c=async i=>{try{console.log("Deleting payment with ID:",i[0]);const n=new o;n.setTable("payment");const m=await n.callRestAPI({id:i[0]},"DELETE");console.log("Delete result:",m),s.current&&s.current.click()}catch(n){console.error("Error deleting payment:",n)}},d=[{id:131,user_id:27,reference_id:52,receiver_id:null,type:"create_community",amount:"1200.00",currency:"USD",stripe_invoice_id:"ch_1234567890",stripe_subscription_id:null,data:null,status:"paid",payment_method:null,transaction_id:null,created_at:"2025-11-12T13:29:07.000Z",updated_at:"2025-11-12T13:29:07.000Z"},{id:130,user_id:2,reference_id:51,receiver_id:null,type:"referral",amount:"800.00",currency:"USD",stripe_invoice_id:null,stripe_subscription_id:null,data:null,status:"paid",payment_method:null,transaction_id:"ch_0987654321",created_at:"2025-11-11T13:29:07.000Z",updated_at:"2025-11-11T13:29:07.000Z"}];return e.jsx(e.Fragment,{children:e.jsx("div",{className:"opportunities-dashboard bg-[#1E1E1E]",children:e.jsxs("div",{className:"container",children:[e.jsxs("div",{className:"header",children:[e.jsx("h1",{children:"Payments"}),e.jsx("p",{children:"View all commission payouts and transactions"})]}),e.jsx("div",{className:"search-add"}),e.jsx("div",{className:"table-container",children:e.jsx(g,{children:e.jsx(S,{columns:D,tableRole:"admin",table:"payment",actionId:"id",searchField:"type",actions:{view:{show:!0,action:a,multiple:!1,locations:["buttons"],showChildren:!1,children:"View",icon:e.jsx(y,{className:"text-blue-500"})},edit:{show:!1,multiple:!1,action:l,locations:["buttons"],showChildren:!1,children:"Edit",icon:e.jsx(_,{stroke:"#4CAF50"})},delete:{show:!1,action:c,multiple:!1,locations:["buttons"],showChildren:!1,children:"Delete",icon:e.jsx(x,{fill:"#E53E3E"})},select:{show:!1,action:null,multiple:!1},add:{show:!1,action:null,multiple:!1},export:{show:!1,action:null,multiple:!1}},actionPosition:["buttons"],refreshRef:s,externalData:{use:!0,data:d,loading:!1,page:1,limit:10,pages:1,total:2}})})}),e.jsxs("div",{className:"pagination",children:[e.jsx("div",{className:"pagination-info",children:"Showing 1 to 2 of 2 entries"}),e.jsxs("div",{className:"pagination-buttons",children:[e.jsx("button",{className:"pagination-button prev",children:"Previous"}),e.jsx("button",{className:"pagination-button next",children:"Next"})]})]})]})})})};export{W as default};
