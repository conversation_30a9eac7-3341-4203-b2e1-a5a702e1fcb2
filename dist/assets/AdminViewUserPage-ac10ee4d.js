import{j as s}from"./@react-google-maps/api-211df1ae.js";import{R as d,f as g,d as b}from"./vendor-1c28ea83.js";import{M as y,A,G as k,t as C,S as E}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let o=new y;const Q=()=>{const{dispatch:h}=d.useContext(A);d.useContext(k);const[e,j]=d.useState({}),[t,N]=d.useState({}),[p,c]=d.useState(!0),r=g(),n=b(),m=i=>i?new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",f=i=>{if(!i)return{};try{return JSON.parse(i)}catch(a){return console.error("Error parsing user data:",a),{}}};d.useEffect(function(){(async function(){try{c(!0),o.setTable("user");const i=await o.callRestAPI({id:Number(r==null?void 0:r.id)},"GET");i.error||(j(i.model),N(f(i.model.data)),c(!1))}catch(i){c(!1),console.log("error",i),C(h,i.message)}})()},[]);const l=(i,a)=>a===1||a==="1"?{text:"Active",className:"status active"}:{text:"Inactive",className:"status inactive"},x=i=>({admin:"Admin",member:"Member",user:"User",editor:"Editor",viewer:"Viewer"})[i]||i,v=()=>{const i=(t==null?void 0:t.first_name)||"",a=(t==null?void 0:t.last_name)||"";return`${i.charAt(0)}${a.charAt(0)}`.toUpperCase()},u=()=>{const i=["#4F46E5","#22C55E","#EF4444","#F59E0B","#3B82F6"],a=(e==null?void 0:e.id)%i.length;return i[a]||i[0]};return s.jsx("div",{className:"view-user-page bg-[#1E1E1E] text-white min-h-screen",children:s.jsx("div",{className:"container mx-auto p-6",children:p?s.jsx("div",{className:"bg-[#161616] p-6 rounded",children:s.jsx(E,{})}):s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"header mb-6",children:[s.jsxs("div",{className:"flex justify-between items-center mb-4",children:[s.jsx("h1",{className:"text-2xl font-semibold",children:"User Details"}),s.jsxs("button",{onClick:()=>n("/admin/user"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to List"]})]}),s.jsx("p",{className:"text-[#9ca3af] text-sm",children:"View and manage user details"})]}),s.jsxs("div",{className:"content-wrapper bg-[#161616] rounded-lg border border-[#2d2d3d] overflow-hidden",children:[s.jsx("div",{className:"header-section p-6 border-b border-[#2d2d3d]",children:s.jsxs("div",{className:"flex justify-between items-start",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"user-avatar",style:{backgroundColor:u()},children:v()}),s.jsxs("div",{children:[s.jsxs("h2",{className:"text-xl font-semibold mb-2",children:[(t==null?void 0:t.first_name)||""," ",(t==null?void 0:t.last_name)||""]}),s.jsxs("div",{className:"flex items-center gap-4 text-sm text-[#9ca3af]",children:[s.jsxs("div",{children:["ID: ",e==null?void 0:e.id]}),s.jsxs("div",{children:["Email: ",e==null?void 0:e.email]})]})]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("span",{className:l(e==null?void 0:e.status,e==null?void 0:e.verify).className,children:l(e==null?void 0:e.status,e==null?void 0:e.verify).text}),s.jsx("span",{className:"role-badge bg-[#252538] text-white px-3 py-1 rounded-full text-xs",children:x(e==null?void 0:e.role_id)})]})]})}),s.jsxs("div",{className:"details-grid p-6 grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx("div",{className:"left-column space-y-6",children:s.jsxs("div",{className:"detail-section",children:[s.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Account Information"}),s.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Email"}),s.jsx("div",{children:(e==null?void 0:e.email)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Role"}),s.jsx("div",{children:x(e==null?void 0:e.role_id)})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Login Type"}),s.jsx("div",{children:(e==null?void 0:e.login_type)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Status"}),s.jsx("div",{children:l(e==null?void 0:e.status,e==null?void 0:e.verify).text})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Verification"}),s.jsx("div",{children:(e==null?void 0:e.verify)===1?"Verified":"Not Verified"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Two-Factor Authentication"}),s.jsx("div",{children:(e==null?void 0:e.two_factor_authentication)===1?"Enabled":"Disabled"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Company ID"}),s.jsx("div",{children:(e==null?void 0:e.company_id)||"N/A"})]})]})]})}),s.jsxs("div",{className:"right-column space-y-6",children:[s.jsxs("div",{className:"detail-section",children:[s.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Personal Information"}),s.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"First Name"}),s.jsx("div",{children:(t==null?void 0:t.first_name)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Last Name"}),s.jsx("div",{children:(t==null?void 0:t.last_name)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Phone"}),s.jsx("div",{children:(t==null?void 0:t.phone)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Industry"}),s.jsx("div",{children:(t==null?void 0:t.industry_id)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Payout Method"}),s.jsx("div",{children:(t==null?void 0:t.payout_method)||"N/A"})]})]})]}),s.jsxs("div",{className:"detail-section",children:[s.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Address Information"}),s.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Address"}),s.jsx("div",{children:(t==null?void 0:t.address)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"City"}),s.jsx("div",{children:(t==null?void 0:t.city)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"State"}),s.jsx("div",{children:(t==null?void 0:t.state)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Zip"}),s.jsx("div",{children:(t==null?void 0:t.zip)||"N/A"})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Country"}),s.jsx("div",{children:(t==null?void 0:t.country)||"N/A"})]})]})]})]})]}),s.jsxs("div",{className:"dates-section p-6 border-t border-[#2d2d3d]",children:[s.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Dates"}),s.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Created At"}),s.jsx("div",{children:m(e==null?void 0:e.created_at)})]}),s.jsxs("div",{className:"detail-item",children:[s.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Updated At"}),s.jsx("div",{children:m(e==null?void 0:e.updated_at)})]})]})]}),s.jsxs("div",{className:"actions-section p-6 border-t border-[#2d2d3d] flex justify-end gap-4",children:[s.jsxs("button",{onClick:()=>n(`/admin/edit-user/${e==null?void 0:e.id}`),className:"edit-button bg-[#22c55e] text-white px-4 py-2 rounded flex items-center",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Edit User"]}),s.jsxs("button",{onClick:()=>n("/admin/user"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to List"]})]})]})]})})})};export{Q as default};
