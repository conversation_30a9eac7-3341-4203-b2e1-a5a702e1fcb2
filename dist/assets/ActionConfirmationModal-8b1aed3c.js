import{j as o}from"./@react-google-maps/api-211df1ae.js";import{r as h}from"./vendor-1c28ea83.js";import{L as i}from"./index-b3edd152.js";import{M as j}from"./index-2d6e6aa7.js";import{_ as E}from"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const L=h.lazy(()=>E(()=>import("./ActionConfirmation-0fb9890d.js"),["assets/ActionConfirmation-0fb9890d.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/react-hook-form-eec8b32f.js","assets/index-b3edd152.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/@craftjs/core-a5d68af1.js","assets/@hookform/resolvers-2530002b.js","assets/yup-1b5612ec.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-d6d1c22a.js","assets/@react-pdf-viewer/core-75a73120.js","assets/react-calendar-eace60fa.js","assets/index-98f942e2.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/MkdInput-67f7082d.js","assets/react-toggle-58b0879a.js","assets/@uppy/dashboard-3a4b1704.js","assets/MkdInput-3e37c863.css","assets/index-632d14e3.js","assets/index-e2604cb4.js"])),O=({data:p={id:null},options:a={endpoint:null,method:"GET"},onSuccess:l,onClose:r,multiple:e=!1,action:s="",mode:d="create",table:n="",title:f="",input:u="input",isOpen:t=!1,inputConfirmation:c=!0,disableCancel:m=!1,modalClasses:x={modalDialog:"max-h-[90%] min-h-[12rem] overflow-y-auto !w-full md:!w-[29.0625rem]",modal:"h-full"},customMessage:_=""})=>o.jsx(i,{children:o.jsx(j,{isOpen:t,modalCloseClick:r,title:f,modalHeader:!0,classes:x,disableCancel:m,children:t&&o.jsx(i,{children:o.jsx(L,{data:p,mode:d,input:u,table:n,action:s,onClose:r,options:a,multiple:e,onSuccess:l,inputConfirmation:c,disableCancel:m,customMessage:_})})})});export{O as ActionConfirmationModal,O as default};
