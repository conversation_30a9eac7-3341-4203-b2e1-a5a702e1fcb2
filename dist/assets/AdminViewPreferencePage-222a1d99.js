import{j as s}from"./@react-google-maps/api-211df1ae.js";import{R as r,f as d}from"./vendor-1c28ea83.js";import{M as x,A as o,G as h,t as f,S as j}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let t=new x;const K=()=>{const{dispatch:l}=r.useContext(o);r.useContext(h);const[e,c]=r.useState({}),[n,i]=r.useState(!0),m=d();return r.useEffect(function(){(async function(){try{i(!0),t.setTable("preference");const a=await t.callRestAPI({id:Number(m==null?void 0:m.id),join:""},"GET");a.error||(c(a.model),i(!1))}catch(a){i(!1),console.log("error",a),f(l,a.message)}})()},[]),s.jsx("div",{className:"shadow-md rounded mx-auto p-5",children:n?s.jsx(j,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Preference"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"First_name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.first_name})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Last_name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.last_name})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Phone"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.phone})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Photo"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.photo})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"User_id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.user_id})]})})]})})};export{K as default};
