import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as o,d as j,f as N}from"./vendor-1c28ea83.js";import{u as P}from"./react-hook-form-eec8b32f.js";import{M as I,A as v,G as w,t as g,S as A,o as T,s as R}from"./index-b3edd152.js";import{c as L,a as m}from"./yup-1b5612ec.js";import{M as l}from"./MkdInput-67f7082d.js";import{I as k}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let d=new I;const me=s=>{const{dispatch:h}=o.useContext(v),_=L({first_name:m(),last_name:m(),phone:m(),photo:m(),user_id:m().required()}).required(),{dispatch:c}=o.useContext(w),[x,p]=o.useState(!1),[b,u]=o.useState(!1),y=j(),{register:r,handleSubmit:E,setError:C,setValue:i,formState:{errors:n}}=P({resolver:T(_)}),a=N();o.useEffect(function(){(async function(){try{u(!0),d.setTable("preference");const e=await d.callRestAPI({id:s.activeId?s.activeId:Number(a==null?void 0:a.id)},"GET");e.error||(i("first_name",e.model.first_name),i("last_name",e.model.last_name),i("phone",e.model.phone),i("photo",e.model.photo),i("user_id",e.model.user_id),u(!1))}catch(e){u(!1),console.log("error",e),g(h,e.message)}})()},[]);const S=async e=>{p(!0);try{d.setTable("preference"),(await d.callRestAPI({id:s.activeId?s.activeId:Number(a==null?void 0:a.id),first_name:e.first_name,last_name:e.last_name,phone:e.phone,photo:e.photo,user_id:e.user_id},"PUT")).error||(R(c,"Updated"),y("/admin/preference"),s.setSidebar(!1),c({type:"REFRESH_DATA",payload:{refreshData:!0}})),p(!1)}catch(f){p(!1),console.log("Error",f),g(h,f.message)}};return o.useEffect(()=>{c({type:"SETPATH",payload:{path:"preference"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Preference"}),b?t.jsx(A,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:E(S),children:[t.jsx(l,{type:"text",page:"edit",name:"first_name",errors:n,label:"First_name",placeholder:"First_name",register:r,className:""}),t.jsx(l,{type:"text",page:"edit",name:"last_name",errors:n,label:"Last_name",placeholder:"Last_name",register:r,className:""}),t.jsx(l,{type:"text",page:"edit",name:"phone",errors:n,label:"Phone",placeholder:"Phone",register:r,className:""}),t.jsx(l,{type:"text",page:"edit",name:"photo",errors:n,label:"Photo",placeholder:"Photo",register:r,className:""}),t.jsx(l,{type:"text",page:"edit",name:"user_id",errors:n,label:"User_id",placeholder:"User_id",register:r,className:""}),t.jsx(k,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{me as default};
