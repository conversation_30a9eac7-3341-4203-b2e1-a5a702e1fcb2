import{j as r}from"./@react-google-maps/api-211df1ae.js";import{R as i,d as I,f as R}from"./vendor-1c28ea83.js";import{u as j}from"./react-hook-form-eec8b32f.js";import{M as N,A as v,G as w,t as x,S as A,o as T,s as C}from"./index-b3edd152.js";import{c as P,a as p}from"./yup-1b5612ec.js";import{M as f}from"./MkdInput-67f7082d.js";import{I as k}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let o=new N;const le=a=>{const{dispatch:y}=i.useContext(v),h=P({referral_id:p().required(),community_id:p().required(),is_primary:p().required()}).required(),{dispatch:s}=i.useContext(w),[_,m]=i.useState(!1),[g,l]=i.useState(!1),b=I(),{register:d,handleSubmit:E,setError:q,setValue:n,formState:{errors:c}}=j({resolver:T(h)}),t=R();i.useEffect(function(){(async function(){try{l(!0),o.setTable("referral_communities");const e=await o.callRestAPI({id:a.activeId?a.activeId:Number(t==null?void 0:t.id)},"GET");e.error||(n("referral_id",e.model.referral_id),n("community_id",e.model.community_id),n("is_primary",e.model.is_primary),l(!1))}catch(e){l(!1),console.log("error",e),x(y,e.message)}})()},[]);const S=async e=>{m(!0);try{o.setTable("referral_communities"),(await o.callRestAPI({id:a.activeId?a.activeId:Number(t==null?void 0:t.id),referral_id:e.referral_id,community_id:e.community_id,is_primary:e.is_primary},"PUT")).error||(C(s,"Updated"),b("/admin/referral_communities"),a.setSidebar(!1),s({type:"REFRESH_DATA",payload:{refreshData:!0}})),m(!1)}catch(u){m(!1),console.log("Error",u),x(y,u.message)}};return i.useEffect(()=>{s({type:"SETPATH",payload:{path:"referral_communities"}})},[]),r.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[r.jsx("h4",{className:"text-2xl font-medium",children:"Edit Referral_communities"}),g?r.jsx(A,{}):r.jsxs("form",{className:"w-full max-w-lg",onSubmit:E(S),children:[r.jsx(f,{type:"text",page:"edit",name:"referral_id",errors:c,label:"Referral_id",placeholder:"Referral_id",register:d,className:""}),r.jsx(f,{type:"text",page:"edit",name:"community_id",errors:c,label:"Community_id",placeholder:"Community_id",register:d,className:""}),r.jsx(f,{type:"text",page:"edit",name:"is_primary",errors:c,label:"Is_primary",placeholder:"Is_primary",register:d,className:""}),r.jsx(k,{type:"submit",loading:_,disabled:_,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{le as default};
