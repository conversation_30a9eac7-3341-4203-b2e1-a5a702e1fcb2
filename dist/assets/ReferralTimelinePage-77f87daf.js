import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{L as m}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const r=({image:t,title:i,date:a,isLast:s})=>e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(m,{children:e.jsx("img",{className:"h-10 w-10 rounded-full",src:t,alt:""})}),!s&&e.jsx("div",{className:"absolute bottom-0 left-1/2 top-12 w-0.5 -translate-x-1/2 bg-[#363636]"})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold text-[#eaeaea]",children:i}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:a})]})]}),k=()=>{const t=[{image:"profile1.jpg",title:"Day Jack Submitted the Recommendation",date:"Nov 5, 2025"},{image:"profile2.jpg",title:"Ryan Reposted this Referral",date:"Nov 8, 2025"},{image:"profile3.jpg",title:"Bryan Got the Job",date:"Nov 10, 2025",isLast:!0}];return e.jsx("div",{className:"space-y-6 p-4 md:p-6",children:e.jsxs("div",{className:"rounded-xl bg-[#161616] p-6",children:[e.jsx("h2",{className:"mb-8 text-2xl font-bold text-[#eaeaea]",children:"Timeline"}),e.jsx("div",{className:"space-y-8",children:t.map((i,a)=>e.jsx(r,{...i},a))})]})})};export{k as default};
