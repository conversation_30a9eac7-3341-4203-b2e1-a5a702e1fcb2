import{j as l}from"./@react-google-maps/api-211df1ae.js";import{r as c,R as F}from"./vendor-1c28ea83.js";import{M as D}from"./MkdInput-67f7082d.js";import{A as J}from"./index-e2604cb4.js";import{A as ee,G as se,a0 as re,a9 as k,an as ie,ao as H,a4 as ne,ap as te,ad as de,a as le,L as oe}from"./index-b3edd152.js";import{I as fe}from"./index-632d14e3.js";import{_ as ae}from"./qr-scanner-cf010ec4.js";import{M as ue}from"./index-2d6e6aa7.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";import"./@craftjs/core-a5d68af1.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";/* empty css                 */import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const ge=c.lazy(()=>ae(()=>import("./ActionConfirmationModal-8b1aed3c.js"),["assets/ActionConfirmationModal-8b1aed3c.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/index-b3edd152.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/@craftjs/core-a5d68af1.js","assets/react-hook-form-eec8b32f.js","assets/@hookform/resolvers-2530002b.js","assets/yup-1b5612ec.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-d6d1c22a.js","assets/@react-pdf-viewer/core-75a73120.js","assets/react-calendar-eace60fa.js","assets/index-98f942e2.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-2d6e6aa7.js"])),Re=({isOpen:R=!1,onClose:j=null,onUpdate:E=null,onSuccess:h=null,columnModel:b="",columnData:n=null})=>{var $,B,q;const{dispatch:_}=F.useContext(ee),{dispatch:y,state:{updateModel:m,createModel:w}}=F.useContext(se),{profile:p}=re(),[r,g]=c.useState({modal:"",viewName:"",showModal:!1,isDirty:!1}),[N,I]=c.useState(null),[S,O]=c.useState(null),[G,A]=c.useState(!1),[t,T]=c.useState({views:[],data:null,columns:[],columnId:0,...n}),[v,z]=c.useState(($=n==null?void 0:n.data)==null?void 0:$.id),[a,C]=c.useState([...n==null?void 0:n.columns]),W=(e,s)=>{const i=[...a];i.splice(s,1,{...e,selected_column:!(e!=null&&e.selected_column)}),C(()=>[...i]),r.isDirty||g(o=>({...o,isDirty:!0}))},K=e=>{const s=(e==null?void 0:e.columns)&&JSON.parse(e==null?void 0:e.columns);z(e==null?void 0:e.id),T(i=>({...i,columnId:e==null?void 0:e.column_id,columns:s,data:e})),C(()=>[...s]),r.isDirty||g(i=>({...i,isDirty:!0}))},M=async()=>{var e;t&&((e=t==null?void 0:t.data)!=null&&e.user_id)?Q():P()},Q=async()=>{var o;const e=[...a],s=t,i=r!=null&&r.isDirty?await k(y,_,"column_views",v,{columns:JSON.stringify(e),current_view:!0}):{error:!0};if(i!=null&&i.error)h&&h({...s,columns:a});else{const u=await H(y,_,"column_views",v,"GET",null,null,!1),d=(o=s==null?void 0:s.views)==null?void 0:o.find(f=>(f==null?void 0:f.current_view)&&(f==null?void 0:f.id)!==v);d&&await k(y,_,"column_views",d==null?void 0:d.id,{current_view:!1}),r.isDirty&&g(f=>({...f,isDirty:!1})),h&&h({...s,data:u==null?void 0:u.data,columns:a,views:[...s==null?void 0:s.views.filter(f=>(f==null?void 0:f.id)!==v),u==null?void 0:u.data]})}},X=c.useCallback(async(e={name:"",id:null})=>{var u;const s=t,i={...e,model:b,current_view:!0,column_id:t==null?void 0:t.columnId,user_id:p==null?void 0:p.id,columns:JSON.stringify(a)},o=(u=s==null?void 0:s.views)==null?void 0:u.find(d=>d==null?void 0:d.current_view);o&&await k(y,_,"column_views",o==null?void 0:o.id,{current_view:!1}),T(d=>({...d,data:i,columnId:t==null?void 0:t.columnId,views:[...d==null?void 0:d.views,i]})),z(e==null?void 0:e.id),E&&E({...s,data:i,columns:a,views:[...s==null?void 0:s.views,i]}),r.isDirty&&g(d=>({...d,isDirty:!1}))},[r,h,a,t,b,E]),P=c.useCallback(async e=>{var u;const s=[...a],i=t;if(!(r!=null&&r.viewName)||!e)return g(d=>({...d,showModal:!0,modal:"enter_name"}));const o=await ie(y,_,"column_views",{model:b,current_view:!0,column_id:t==null?void 0:t.columnId,user_id:p==null?void 0:p.id,name:(r==null?void 0:r.viewName)||e,columns:JSON.stringify(s)});if(!(o!=null&&o.error)){const d=await H(y,_,"column_views",o==null?void 0:o.data,"GET",null,null,!1),f=(u=i==null?void 0:i.views)==null?void 0:u.find(x=>(x==null?void 0:x.current_view)&&(x==null?void 0:x.id)!==v);f&&await k(y,_,"column_views",f==null?void 0:f.id,{current_view:!1}),r.isDirty&&g(x=>({...x,isDirty:!1})),h&&h({...i,data:d==null?void 0:d.data,columns:a,views:[...i==null?void 0:i.views,d==null?void 0:d.data]})}},[r,h,a,t,b]),Y=(e,s)=>{O(s),A(!0)},Z=e=>{if(e.preventDefault(),S&&N&&S!=N){const s=[...a],i=s[S];s.splice(S,1),s.splice(N,0,i),C(()=>[...s]),r.isDirty||g(o=>({...o,isDirty:!0}))}I(null),O(null),A(!1)},L=(e,s)=>{e.preventDefault(),I(s)},U=e=>{e.preventDefault(),I(null),O(null),A(!1)},V=e=>{e.preventDefault(),I(null)};return c.useEffect(()=>{var e,s;(e=n==null?void 0:n.data)!=null&&e.name&&g(i=>{var o;return{...i,viewName:(o=n==null?void 0:n.data)==null?void 0:o.name}}),n!=null&&n.columns&&C(()=>[...n==null?void 0:n.columns]),T(()=>n),z((s=n==null?void 0:n.data)==null?void 0:s.id)},[(B=n==null?void 0:n.data)==null?void 0:B.name,n==null?void 0:n.columns]),l.jsxs(l.Fragment,{children:[l.jsx(ue,{isOpen:R,modalCloseClick:()=>j&&j(),customMinWidthInTw:"!w-[23rem]",modalHeader:!0,title:l.jsxs("div",{className:"flex items-center gap-2 font-inter text-[1.125rem] font-bold leading-[1.5rem] text-[#18181B]",children:[l.jsx(ne,{})," Columns"]}),classes:{modalDialog:"!grid grid-rows-[auto_90%] !gap-0 !w-full !px-0 md:!w-[30.375rem] md:min-h-[70%] md:h-[70%] md:max-h-[70%] max-h-[70%] min-h-[70%]",modalContent:"!z-10 !mt-0 !px-0 overflow-hidden !pt-0",modal:"h-full"},children:l.jsxs("div",{className:"relative mx-auto grid h-full max-h-full min-h-full w-full grow grid-cols-1 grid-rows-[auto_1fr_auto] gap-5 rounded px-5 text-start !font-inter leading-snug tracking-wide",children:[l.jsxs("div",{className:"grid w-full min-w-full max-w-full grid-cols-[1fr_auto] grid-rows-1 items-center justify-start gap-2 py-1",children:[l.jsx("div",{className:"scrollbar-hide flex w-full min-w-full max-w-full items-center justify-start gap-2 overflow-auto",children:(q=t==null?void 0:t.views)!=null&&q.length?t==null?void 0:t.views.map((e,s)=>l.jsx(J,{type:"button",anination:!1,onClick:()=>K(e),className:`flex h-full w-fit min-w-fit max-w-fit items-center justify-between rounded-md !py-0 ${(e==null?void 0:e.id)===v?"!bg-primary !text-white":"!border-gray-200 !bg-transparent !text-sub-500"}`,children:e==null?void 0:e.name},s)):null}),l.jsx(J,{type:"button",onClick:()=>{g(e=>({...e,showModal:!0,modal:"new_view"}))},className:"flex h-full w-fit items-center justify-between rounded-md !border-0 !bg-white !py-0",children:l.jsx(te,{className:"h-5 w-5"})})]}),l.jsx("div",{className:"space-y-5 overflow-auto",children:l.jsx("div",{className:"h-full max-h-full min-h-full w-full overflow-y-auto",children:a.map((e,s)=>{if(!["Row","Action"].includes(e==null?void 0:e.header))return l.jsxs("div",{draggable:!0,onDragStart:i=>Y(i,s),onDragEnd:U,onDragOver:i=>L(i,s),onDragLeave:i=>V(i),onDrop:i=>Z(i),className:`${G?"cursor-grabbing":"cursor-grab"} flex h-[2.5rem] w-full items-center justify-between rounded-md p-2 ${N==s?"bg-primary-light text-white":""}`,children:[l.jsx(de,{className:`${G?"cursor-grabbing":"cursor-grab"}`}),l.jsx("div",{className:"grow px-5 text-left !capitalize",children:le(e==null?void 0:e.header,{casetype:"capitalize",separator:" "})}),l.jsx("div",{children:l.jsx(D,{type:"toggle",onChange:i=>W(e,s),value:e==null?void 0:e.selected_column})})]},s)})})}),l.jsxs("div",{className:"relative flex gap-5",children:[l.jsx("div",{className:"w-1/2",children:l.jsx(J,{onClick:()=>j&&j(),disabled:(m==null?void 0:m.loading)||(w==null?void 0:w.loading),className:"!w-full !grow !border-none !bg-soft-200 !text-sub-500",children:"Cancel"})}),l.jsx(fe,{type:"button",disabled:(m==null?void 0:m.loading)||(w==null?void 0:w.loading),loading:(m==null?void 0:m.loading)||(w==null?void 0:w.loading),onClick:()=>{M()},className:"w-1/2",children:"Save and Close"})]})]})}),l.jsx(oe,{children:l.jsx(ge,{isOpen:(r==null?void 0:r.showModal)&&["enter_name","new_view"].includes(r==null?void 0:r.modal),title:"Save View",modalClasses:{modalDialog:"max-h-[90%] min-h-[12rem] overflow-y-auto !w-full md:!w-[29.0625rem]",modal:"h-full"},onClose:()=>{g(e=>({...e,modal:"",viewName:"",showModal:!1}))},onSuccess:e=>{["new_view"].includes(r==null?void 0:r.modal)&&X(e),["enter_name"].includes(r==null?void 0:r.modal)&&P(e==null?void 0:e.name),g(s=>({...s,modal:"",viewName:e==null?void 0:e.name,showModal:!1}))},data:{model:b,current_view:!0,column_id:t==null?void 0:t.columnId,user_id:p==null?void 0:p.id,name:r==null?void 0:r.viewName,columns:JSON.stringify(a)},action:["new_view"].includes(r==null?void 0:r.modal)?"create":"save",mode:["new_view"].includes(r==null?void 0:r.modal)?"input_create":"input",input:"name",customMessage:"Enter View Name",multiple:!1,table:"column_views"})})]})};export{Re as default};
