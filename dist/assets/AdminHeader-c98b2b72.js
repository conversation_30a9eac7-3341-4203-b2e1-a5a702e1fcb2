import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as n,u as N,k as L,R as y,L as p}from"./vendor-1c28ea83.js";import{G as z,A as k,M,t as V}from"./index-b3edd152.js";import{a as S}from"./index.esm-1ac45320.js";import{G as i}from"./react-icons-5238c8a8.js";import{T as E,U as H}from"./lucide-react-0f29fa6b.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";function R(l){return i({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M436 480h-20V24c0-13.255-10.745-24-24-24H56C42.745 0 32 10.745 32 24v456H12c-6.627 0-12 5.373-12 12v20h448v-20c0-6.627-5.373-12-12-12zM128 76c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12V76zm0 96c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40zm52 148h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40c0 6.627-5.373 12-12 12zm76 160h-64v-84c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v84zm64-172c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40zm0-96c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12v-40c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40zm0-96c0 6.627-5.373 12-12 12h-40c-6.627 0-12-5.373-12-12V76c0-6.627 5.373-12 12-12h40c6.627 0 12 5.373 12 12v40z"}}]})(l)}function B(l){return i({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M487.4 315.7l-42.6-24.6c4.3-23.2 4.3-47 0-70.2l42.6-24.6c4.9-2.8 7.1-8.6 5.5-14-11.1-35.6-30-67.8-54.7-94.6-3.8-4.1-10-5.1-14.8-2.3L380.8 110c-17.9-15.4-38.5-27.3-60.8-35.1V25.8c0-5.6-3.9-10.5-9.4-11.7-36.7-8.2-74.3-7.8-109.2 0-5.5 1.2-9.4 6.1-9.4 11.7V75c-22.2 7.9-42.8 19.8-60.8 35.1L88.7 85.5c-4.9-2.8-11-1.9-14.8 2.3-24.7 26.7-43.6 58.9-54.7 94.6-1.7 5.4.6 11.2 5.5 14L67.3 221c-4.3 23.2-4.3 47 0 70.2l-42.6 24.6c-4.9 2.8-7.1 8.6-5.5 14 11.1 35.6 30 67.8 54.7 94.6 3.8 4.1 10 5.1 14.8 2.3l42.6-24.6c17.9 15.4 38.5 27.3 60.8 35.1v49.2c0 5.6 3.9 10.5 9.4 11.7 36.7 8.2 74.3 7.8 109.2 0 5.5-1.2 9.4-6.1 9.4-11.7v-49.2c22.2-7.9 42.8-19.8 60.8-35.1l42.6 24.6c4.9 2.8 11 1.9 14.8-2.3 24.7-26.7 43.6-58.9 54.7-94.6 1.5-5.5-.7-11.3-5.6-14.1zM256 336c-44.1 0-80-35.9-80-80s35.9-80 80-80 80 35.9 80 80-35.9 80-80 80z"}}]})(l)}function O(l){return i({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M434.7 64h-85.9c-8 0-15.7 3-21.6 8.4l-98.3 90c-.1.1-.2.3-.3.4-16.6 15.6-16.3 40.5-2.1 56 12.7 13.9 39.4 17.6 56.1 2.7.1-.1.3-.1.4-.2l79.9-73.2c6.5-5.9 16.7-5.5 22.6 1 6 6.5 5.5 16.6-1 22.6l-26.1 23.9L504 313.8c2.9 2.4 5.5 5 7.9 7.7V128l-54.6-54.6c-5.9-6-14.1-9.4-22.6-9.4zM544 128.2v223.9c0 17.7 14.3 32 32 32h64V128.2h-96zm48 223.9c-8.8 0-16-7.2-16-16s7.2-16 16-16 16 7.2 16 16-7.2 16-16 16zM0 384h64c17.7 0 32-14.3 32-32V128.2H0V384zm48-63.9c8.8 0 16 7.2 16 16s-7.2 16-16 16-16-7.2-16-16c0-8.9 7.2-16 16-16zm435.9 18.6L334.6 217.5l-30 27.5c-29.7 27.1-75.2 24.5-101.7-4.4-26.9-29.4-24.8-74.9 4.4-101.7L289.1 64h-83.8c-8.5 0-16.6 3.4-22.6 9.4L128 128v223.9h18.3l90.5 81.9c27.4 22.3 67.7 18.1 90-9.3l.2-.2 17.9 15.5c15.9 13 39.4 10.5 52.3-5.4l31.4-38.6 5.4 4.4c13.7 11.1 33.9 9.1 45-4.7l9.5-11.7c11.2-13.8 9.1-33.9-4.6-45.1z"}}]})(l)}function A(l){return i({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M96 224c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm448 0c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm32 32h-64c-17.6 0-33.5 7.1-45.1 18.6 40.3 22.1 68.9 62 75.1 109.4h66c17.7 0 32-14.3 32-32v-32c0-35.3-28.7-64-64-64zm-256 0c61.9 0 112-50.1 112-112S381.9 32 320 32 208 82.1 208 144s50.1 112 112 112zm76.8 32h-8.3c-20.8 10-43.9 16-68.5 16s-47.6-6-68.5-16h-8.3C179.6 288 128 339.6 128 403.2V432c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48v-28.8c0-63.6-51.6-115.2-115.2-115.2zm-223.7-13.4C161.5 263.1 145.6 256 128 256H64c-35.3 0-64 28.7-64 64v32c0 17.7 14.3 32 32 32h65.9c6.3-47.4 34.9-87.3 75.2-109.4z"}}]})(l)}const u=[{title:"Main",items:[{to:"/admin/referral",text:"Opportunities",icon:e.jsx(O,{className:"text-2xl"}),value:"referral"},{to:"/admin/community",text:"Communities",icon:e.jsx(R,{className:"text-2xl"}),value:"community"},{to:"/admin/commission",text:"Commissions",icon:e.jsx(S,{className:"text-2xl"}),value:"commission"},{to:"/admin/user",text:"Users",icon:e.jsx(A,{className:"text-2xl"}),value:"user"},{to:"/admin/payments",text:"Payments",icon:e.jsx(E,{className:"text-2xl"}),value:"meeting"},{to:"/admin/plans",text:"Plans",icon:e.jsx(H,{className:"text-2xl"}),value:"meeting_attendee"},{to:"/admin/settings",text:"Settings",icon:e.jsx(B,{className:"text-2xl"}),value:"settings"}]}],D=()=>e.jsx("svg",{width:"30",height:"24",viewBox:"0 0 30 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("g",{id:"Frame",children:e.jsx("path",{id:"Vector",d:"M15.1594 3.99375L10.6219 7.66875C9.86719 8.27812 9.72188 9.375 10.2938 10.1578C10.8984 10.9922 12.075 11.1562 12.8859 10.5234L17.5406 6.90469C17.8688 6.65156 18.3375 6.70781 18.5953 7.03594C18.8531 7.36406 18.7922 7.83281 18.4641 8.09062L17.4844 8.85L24 14.85V6H23.9672L23.7844 5.88281L20.3812 3.70312C19.6641 3.24375 18.825 3 17.9719 3C16.95 3 15.9562 3.35156 15.1594 3.99375ZM16.2281 9.825L13.8047 11.7094C12.3281 12.8625 10.1859 12.5625 9.07969 11.0437C8.03906 9.61406 8.30156 7.61719 9.675 6.50625L13.575 3.35156C13.0312 3.12187 12.4453 3.00469 11.85 3.00469C10.9688 3 10.1109 3.2625 9.375 3.75L6 6V16.5H7.32187L11.6063 20.4094C12.525 21.2484 13.9453 21.1828 14.7844 20.2641C15.0422 19.9781 15.2156 19.6453 15.3047 19.2984L16.1016 20.0297C17.0156 20.8687 18.4406 20.8078 19.2797 19.8937C19.4906 19.6641 19.6453 19.3969 19.7438 19.1203C20.6531 19.7297 21.8906 19.6031 22.6547 18.7687C23.4937 17.8547 23.4328 16.4297 22.5187 15.5906L16.2281 9.825ZM0.75 6C0.3375 6 0 6.3375 0 6.75V16.5C0 17.3297 0.670312 18 1.5 18H3C3.82969 18 4.5 17.3297 4.5 16.5V6H0.75ZM2.25 15C2.44891 15 2.63968 15.079 2.78033 15.2197C2.92098 15.3603 3 15.5511 3 15.75C3 15.9489 2.92098 16.1397 2.78033 16.2803C2.63968 16.421 2.44891 16.5 2.25 16.5C2.05109 16.5 1.86032 16.421 1.71967 16.2803C1.57902 16.1397 1.5 15.9489 1.5 15.75C1.5 15.5511 1.57902 15.3603 1.71967 15.2197C1.86032 15.079 2.05109 15 2.25 15ZM25.5 6V16.5C25.5 17.3297 26.1703 18 27 18H28.5C29.3297 18 30 17.3297 30 16.5V6.75C30 6.3375 29.6625 6 29.25 6H25.5ZM27 15.75C27 15.5511 27.079 15.3603 27.2197 15.2197C27.3603 15.079 27.5511 15 27.75 15C27.9489 15 28.1397 15.079 28.2803 15.2197C28.421 15.3603 28.5 15.5511 28.5 15.75C28.5 15.9489 28.421 16.1397 28.2803 16.2803C28.1397 16.421 27.9489 16.5 27.75 16.5C27.5511 16.5 27.3603 16.421 27.2197 16.2803C27.079 16.1397 27 15.9489 27 15.75Z",fill:"#7DD87D"})})}),r1=()=>{var h;const{state:l,dispatch:v}=n.useContext(z),{state:$,dispatch:r}=n.useContext(k),[t,f]=n.useState(!0),[a,g]=n.useState({}),C=N(),[x,m]=n.useState(!1);n.useEffect(()=>{async function o(){try{const s=await new M().getProfile();g(s),r({type:"UPDATE_PROFILE",payload:s})}catch(c){console.log("Error",c),V(r,c.message)}}o()},[]);const j=()=>{f(!t),v({type:"OPEN_SIDEBAR",payload:{isOpen:!t}})},d=()=>{r({type:"LOGOUT"}),localStorage.removeItem("token"),localStorage.removeItem("role"),window.location.href="/admin/login"},w=()=>{const o=C.pathname;for(const c of u)for(const s of c.items)if(o.includes(s.to))return s.text;return"Dashboard"};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:`fixed left-0 top-0 z-50 flex h-screen flex-col transition-all duration-300 ${t?"w-64":"w-20"} border-r border-[#363636] bg-[#161616]`,children:[e.jsxs("div",{className:"flex h-16 items-center justify-between px-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(D,{}),t&&e.jsx("span",{className:"ml-2 text-sm font-medium text-[#7dd87d]",children:"Admin Portal"})]}),e.jsx("button",{onClick:j,className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:t?e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M15.707 4.293a1 1 0 010 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 0z",clipRule:"evenodd"})}):e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M7.293 4.293a1 1 0 011.414 0l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414-1.414L11.586 10 7.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),e.jsxs("div",{className:"flex flex-1 flex-col justify-between overflow-y-auto pt-4",children:[e.jsx("nav",{className:"space-y-6 px-3",children:u.map((o,c)=>e.jsxs("div",{className:"space-y-3",children:[t&&e.jsx("h3",{className:"px-3 text-xs font-medium uppercase text-[#b5b5b5]",children:o.title}),e.jsx("ul",{className:"space-y-2",children:o.items.map(s=>e.jsx("li",{children:e.jsx(L,{to:s.to,className:({isActive:b})=>`
                          flex items-center rounded-md px-4 py-3 text-base font-medium
                          ${b?"bg-[#2e7d32] text-white":"text-[#b5b5b5] hover:bg-[#242424] hover:text-[#eaeaea]"}
                          ${t?"":"justify-center"}
                        `,children:e.jsxs("span",{className:"flex items-center",children:[y.cloneElement(s.icon,{className:`${s.icon.props.className} ${t?"mr-4":"mx-auto"}`}),t&&s.text]})})},s.value))})]},c))}),e.jsx("div",{className:"px-3 pb-6",children:e.jsxs("button",{onClick:d,className:`
                flex w-full items-center rounded-md px-4 py-3 text-base font-medium
                text-[#b5b5b5] hover:bg-[#242424] hover:text-[#eaeaea]
                ${t?"":"justify-center"}
              `,children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",className:`text-2xl ${t?"mr-4":"mx-auto"}`,width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}),e.jsx("polyline",{points:"16 17 21 12 16 7"}),e.jsx("line",{x1:"21",y1:"12",x2:"9",y2:"12"})]}),t&&"Logout"]})})]})]}),e.jsx("header",{className:`fixed top-0 z-40 flex h-16 w-full items-center border-b border-[#363636] bg-[#161616] px-4 transition-all ${t?"ml-64":"ml-20"}`,children:e.jsxs("div",{className:"flex w-full items-center justify-between",children:[e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:w()}),e.jsxs("div",{className:"relative",children:[e.jsxs("button",{onClick:()=>m(!x),className:"flex items-center gap-2 rounded-full focus:outline-none",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-white",children:((h=a==null?void 0:a.first_name)==null?void 0:h[0])||"A"}),e.jsxs("span",{className:"text-sm text-[#eaeaea]",children:[(a==null?void 0:a.first_name)||"Admin"," ",(a==null?void 0:a.last_name)||""]}),e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-[#b5b5b5]",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})]}),x&&e.jsxs("div",{className:"absolute right-0 mt-2 w-48 rounded-md border border-[#363636] bg-[#242424] py-1 shadow-lg",children:[e.jsx(p,{to:"/admin/profile",className:"block px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#2e7d32] hover:text-white",onClick:()=>m(!1),children:"Profile"}),e.jsx(p,{to:"/admin/settings",className:"block px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#2e7d32] hover:text-white",onClick:()=>m(!1),children:"Settings"}),e.jsx("button",{onClick:d,className:"block w-full px-4 py-2 text-left text-sm text-[#eaeaea] hover:bg-[#2e7d32] hover:text-white",children:"Logout"})]})]})]})}),e.jsx("main",{className:`min-h-screen transition-all ${t?"ml-64":"ml-20"} bg-[#1e1e1e] pt-16`})]})};export{r1 as AdminHeader,r1 as default};
