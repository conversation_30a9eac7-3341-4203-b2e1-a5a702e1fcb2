import{j as i}from"./@react-google-maps/api-211df1ae.js";import{r as u,R as y}from"./vendor-1c28ea83.js";import{A as d,G as g,ab as b}from"./index-b3edd152.js";import{M as j}from"./index-dbfe2d0c.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const Z=async(e=[],r=[],a,c)=>{const t=r.filter(m=>m==null?void 0:m.list),s=e.map(async m=>{const l={...m};for(let o of t){const f=o==null?void 0:o.accessor;l[f]&&(l[f]=await C(l[f],o,a,c))}return l});return await Promise.all(s)};function x(e,r){return e}function h(e,r){return e}function p(e=[],r){return e.reduce((c,t,s)=>{var n;return c+Number(t[(n=r==null?void 0:r.action)==null?void 0:n.key])},0)}const _=(e=[],r)=>e.map(a=>{var c;return a[(c=r==null?void 0:r.action)==null?void 0:c.key]});function w(e,r){const{action:a}=r,{init:c,join:t,key:s,table_key:n}=a;switch(c){case"join":return n?`${e[t][s]} - ${e[n]}`:e[t][s];case"table":return n?`${e[n]} - ${e[t][s]}`:e[n];default:return e[t][s]}}async function A(e=[],r,a,c){var t,s,n;if(!e.length)return[];try{const m=await b(a,c,(t=r==null?void 0:r.action)==null?void 0:t.table,e,(s=r==null?void 0:r.action)==null?void 0:s.join);if(!(m!=null&&m.error))return(n=m==null?void 0:m.data)==null?void 0:n.map(l=>{var o;return l[(o=r==null?void 0:r.action)==null?void 0:o.join]?w(l,r):l==null?void 0:l.id})}catch(m){console.error("Error fetching ID array join data:",m)}return e}function N(e,r){var t;if(!(r!=null&&r.action))return"";const a={add:p,list:_},c=a[(t=r==null?void 0:r.action)==null?void 0:t.operation];return a?c(e,r):""}async function k(e,r,a,c){var n;if(!(r!=null&&r.action))return e;const s={add:p,join:async(m,l)=>A(m,l,a,c)}[(n=r==null?void 0:r.action)==null?void 0:n.operation];return s?await s(e,r):""}async function D(e,r,a,c,t){if(!r)return"";const n={object_array:N,id_array:(m,l)=>k(m,l,c,t),number_array:x,string_array:h}[r];return n?n(e,a):""}async function C(e,r,a,c){const[t,s]=r.listType.split("|");if(!t)return"";if(t==="json")try{const n=JSON.parse(e);return D(n,s,r,a,c)}catch{return e}return e}const z=({column:e,data:r,expandRow:a=!1,currentTableData:c=[]})=>(y.useContext(d),y.useContext(g),u.useState(null),i.jsx("div",{className:"flex items-center gap-[.25rem]",children:r?i.jsxs(i.Fragment,{children:[a?i.jsx(i.Fragment,{children:["string","number"].includes(typeof r)?i.jsx("span",{className:"flex w-fit items-center justify-normal gap-[.25rem] rounded-[.375rem] border border-gray-200  p-[.25rem_.5rem_.25rem_.25rem] capitalize",children:r}):typeof r=="object"&&Array.isArray(r)?i.jsx(i.Fragment,{children:r.map((t,s)=>i.jsx("span",{className:"flex w-fit items-center justify-normal gap-[.25rem] rounded-[.375rem] border border-gray-200  p-[.25rem_.5rem_.25rem_.25rem] capitalize",children:t},s))}):null}):null,a?null:i.jsx(i.Fragment,{children:["string","number"].includes(typeof r)?i.jsx("span",{className:"flex w-fit items-center justify-normal gap-[.25rem] rounded-[.375rem] border border-gray-200  p-[.25rem_.5rem_.25rem_.25rem] capitalize",children:r}):typeof r=="object"&&Array.isArray(r)?i.jsxs(i.Fragment,{children:[Array.from({length:(r==null?void 0:r.length)>(e==null?void 0:e.limit)?e==null?void 0:e.limit:r==null?void 0:r.length}).map((t,s)=>i.jsx("span",{className:"flex w-fit items-center justify-normal gap-[.25rem] rounded-[.375rem] border border-gray-200  p-[.25rem_.5rem_.25rem_.25rem] capitalize",children:r[s]},s)),(r==null?void 0:r.length)>(e==null?void 0:e.limit)?i.jsx(j,{display:i.jsxs("span",{className:"flex w-fit items-center justify-normal gap-[.25rem] rounded-[.375rem] border border-gray-200  p-[.25rem_.5rem_.25rem_.25rem] capitalize",children:["+ ",r.length-(e==null?void 0:e.limit)]}),openOnClick:!0,backgroundColor:"#000",children:i.jsx("div",{className:"grid h-fit max-h-[18.75rem] min-h-fit w-[31.25rem] min-w-[31.25rem] max-w-[31.25rem] grid-cols-[repeat(auto-fill,minmax(15rem,1fr))]  items-start  gap-2 overflow-y-auto",children:r==null?void 0:r.map((t,s)=>i.jsx("span",{className:"h-fit rounded-[.375rem] border border-gray-200  p-[.25rem_.5rem_.25rem_.25rem] capitalize text-white",children:t},s))})}):null]}):null})]}):null})),v=u.memo(z);export{v as default,Z as getProcessedTableData};
