import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as r,d as H}from"./vendor-1c28ea83.js";import{M as K,G as U,A as V,o as q,t as I,a6 as w}from"./index-b3edd152.js";import{u as J}from"./react-hook-form-eec8b32f.js";import{c as Q,a as b}from"./yup-1b5612ec.js";import{P as W}from"./index-7ba88dde.js";import"./index-e2604cb4.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const y=[{header:"Row",accessor:"row"},{header:"Product",accessor:"product_name"},{header:"Customer",accessor:"customer"},{header:"Currency",accessor:"currency"},{header:"Amount",accessor:"amount",type:"currency"},{header:"Created at",accessor:"created_at",type:"timestamp"},{header:"Status",accessor:"status"}],Ne=()=>{var x,g;const j=new K,{dispatch:m}=r.useContext(U),{dispatch:N}=r.useContext(V);r.useState("");const[v,S]=r.useState([]),[c,l]=r.useState(10),[u,P]=r.useState(0),[X,C]=r.useState(0),[i,_]=r.useState(0),[k,R]=r.useState(!1),[D,E]=r.useState(!1);H();const T=Q({product_name:b(),customer_email:b()}),{register:p,handleSubmit:A,formState:{errors:h}}=J({resolver:q(T)});function O(t){(async function(){l(t),await n(1,t)})()}function z(){(async function(){await n(i-1>1?i-1:1,c)})()}function F(){(async function(){await n(i+1<=u?i+1:1,c)})()}async function n(t,o,a){try{const{list:s,total:G,limit:L,num_pages:f,page:d,error:M,message:B}=await j.getStripeOrders({page:t,limit:o},a);if(M){showToast(m,B,5e3);return}S(s),l(+L),P(+f),_(+d),C(+G),R(+d>1),E(+d+1<=+f)}catch(s){console.log("ERROR",s),I(N,s.message)}}const $=t=>{const o=w(t.customer_email),a=w(t.product_name);n(1,c,{customer_email:o,product_name:a})};return r.useEffect(()=>{m({type:"SETPATH",payload:{path:"orders"}}),async function(){await n(1,c)}()},[]),e.jsxs(e.Fragment,{children:[e.jsxs("form",{className:"mb-10 rounded bg-white p-5 shadow",onSubmit:A($),children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Search"}),e.jsxs("div",{className:"filter-form-holder mt-10 flex flex-wrap",children:[e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Customer"}),e.jsx("input",{type:"text",placeholder:"Email",...p("customer_email"),className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(x=h.customer_email)==null?void 0:x.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Product"}),e.jsx("input",{type:"text",placeholder:"Product name",...p("product_name"),className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(g=h.product_name)==null?void 0:g.message})]})]}),e.jsxs("div",{className:"search-buttons pl-2",children:[e.jsx("button",{type:"submit",className:"mr-2 inline-block rounded bg-blue-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:"Search"}),e.jsx("button",{type:"reset",onClick:()=>n(1,c),className:"inline-block rounded bg-gray-800 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-gray-900 hover:shadow-lg focus:bg-gray-900 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-gray-900 active:shadow-lg",children:"Reset"})]})]}),e.jsxs("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:[e.jsx("div",{className:"mb-3 flex w-full justify-between text-center  ",children:e.jsx("h4",{className:"text-2xl font-medium",children:"Orders "})}),e.jsx("div",{className:"overflow-x-auto rounded-md border border-gray-200 shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:y.map((t,o)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},o))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:v.map((t,o)=>e.jsx("tr",{children:y.map((a,s)=>a.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4"},s):a.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.mapping[t[a.accessor]]},s):a.type==="timestamp"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(t[a.accessor]*1e3).toLocaleString("en-US")},s):a.type==="currency"?e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:["$",Number(t[a.accessor]/100).toFixed(2)]},s):a.type==="metadata"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[a.pre_accessor][a.accessor]??"n/a"},s):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[a.accessor]},s))},o))})]})})]}),e.jsx(W,{currentPage:i,pageCount:u,pageSize:c,canPreviousPage:k,canNextPage:D,updatePageSize:O,previousPage:z,nextPage:F})]})};export{Ne as default};
