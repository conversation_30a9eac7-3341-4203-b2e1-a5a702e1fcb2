import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as r,d as k,f as E}from"./vendor-1c28ea83.js";import{u as N}from"./react-hook-form-eec8b32f.js";import{M as I,A as M,G as T,t as b,S as v,o as w,s as A}from"./index-b3edd152.js";import{c as q,a as m}from"./yup-1b5612ec.js";import{M as n}from"./MkdInput-67f7082d.js";import{I as R}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let d=new I;const ne=l=>{const{dispatch:f}=r.useContext(M),h=q({status:m().required(),topic:m().required(),description:m().required(),meeting_datetime:m().required(),created_by:m().required(),meeting_link:m().required()}).required(),{dispatch:c}=r.useContext(T),[x,p]=r.useState(!1),[y,u]=r.useState(!1),_=k(),{register:i,handleSubmit:S,setError:C,setValue:s,formState:{errors:a}}=N({resolver:w(h)}),o=E();r.useEffect(function(){(async function(){try{u(!0),d.setTable("meetings");const e=await d.callRestAPI({id:l.activeId?l.activeId:Number(o==null?void 0:o.id)},"GET");e.error||(s("status",e.model.status),s("topic",e.model.topic),s("description",e.model.description),s("meeting_datetime",e.model.meeting_datetime),s("created_by",e.model.created_by),s("meeting_link",e.model.meeting_link),u(!1))}catch(e){u(!1),console.log("error",e),b(f,e.message)}})()},[]);const j=async e=>{p(!0);try{d.setTable("meetings"),(await d.callRestAPI({id:l.activeId?l.activeId:Number(o==null?void 0:o.id),status:e.status,topic:e.topic,description:e.description,meeting_datetime:e.meeting_datetime,created_by:e.created_by,meeting_link:e.meeting_link},"PUT")).error||(A(c,"Updated"),_("/admin/meetings"),l.setSidebar(!1),c({type:"REFRESH_DATA",payload:{refreshData:!0}})),p(!1)}catch(g){p(!1),console.log("Error",g),b(f,g.message)}};return r.useEffect(()=>{c({type:"SETPATH",payload:{path:"meetings"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Meetings"}),y?t.jsx(v,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:S(j),children:[t.jsx(n,{type:"text",page:"edit",name:"status",errors:a,label:"Status",placeholder:"Status",register:i,className:""}),t.jsx(n,{type:"text",page:"edit",name:"topic",errors:a,label:"Topic",placeholder:"Topic",register:i,className:""}),t.jsx(n,{type:"text",page:"edit",name:"description",errors:a,label:"Description",placeholder:"Description",register:i,className:""}),t.jsx(n,{type:"text",page:"edit",name:"meeting_datetime",errors:a,label:"Meeting_datetime",placeholder:"Meeting_datetime",register:i,className:""}),t.jsx(n,{type:"text",page:"edit",name:"created_by",errors:a,label:"Created_by",placeholder:"Created_by",register:i,className:""}),t.jsx(n,{type:"text",page:"edit",name:"meeting_link",errors:a,label:"Meeting_link",placeholder:"Meeting_link",register:i,className:""}),t.jsx(R,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ne as default};
