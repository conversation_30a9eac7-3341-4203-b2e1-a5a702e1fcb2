import{j as a}from"./@react-google-maps/api-211df1ae.js";import{R as s,d as S,f as I}from"./vendor-1c28ea83.js";import{u as N}from"./react-hook-form-eec8b32f.js";import{M as v,A as w,G as j,t as u,S as A,o as T,s as R}from"./index-b3edd152.js";import{c as P,a as k}from"./yup-1b5612ec.js";import{M as C}from"./MkdInput-67f7082d.js";import{I as D}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let o=new v;const nt=r=>{const{dispatch:l}=s.useContext(w),p=P({name:k().required()}).required(),{dispatch:i}=s.useContext(j),[c,m]=s.useState(!1),[f,n]=s.useState(!1),x=S(),{register:h,handleSubmit:y,setError:L,setValue:g,formState:{errors:b}}=N({resolver:T(p)}),e=I();s.useEffect(function(){(async function(){try{n(!0),o.setTable("industry");const t=await o.callRestAPI({id:r.activeId?r.activeId:Number(e==null?void 0:e.id)},"GET");t.error||(g("name",t.model.name),n(!1))}catch(t){n(!1),console.log("error",t),u(l,t.message)}})()},[]);const E=async t=>{m(!0);try{o.setTable("industry"),(await o.callRestAPI({id:r.activeId?r.activeId:Number(e==null?void 0:e.id),name:t.name},"PUT")).error||(R(i,"Updated"),x("/admin/industry"),r.setSidebar(!1),i({type:"REFRESH_DATA",payload:{refreshData:!0}})),m(!1)}catch(d){m(!1),console.log("Error",d),u(l,d.message)}};return s.useEffect(()=>{i({type:"SETPATH",payload:{path:"industry"}})},[]),a.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[a.jsx("h4",{className:"text-2xl font-medium",children:"Edit Industry"}),f?a.jsx(A,{}):a.jsxs("form",{className:"w-full max-w-lg",onSubmit:y(E),children:[a.jsx(C,{type:"text",page:"edit",name:"name",errors:b,label:"Name",placeholder:"Name",register:h,className:""}),a.jsx(D,{type:"submit",loading:c,disabled:c,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{nt as default};
