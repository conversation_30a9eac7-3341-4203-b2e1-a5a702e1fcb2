import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as E,r as u}from"./vendor-1c28ea83.js";import{u as K,C as w}from"./react-hook-form-eec8b32f.js";import{G as V,A as J,M as P,t as R,T as X,o as Z,s as ee}from"./index-b3edd152.js";import{c as te,a as d}from"./yup-1b5612ec.js";import{I as le}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const C=({options:x,value:b,onChange:$,placeholder:k,loading:I})=>{const[h,f]=u.useState(!1),p=E.useRef(null);E.useEffect(()=>{const n=g=>{p.current&&!p.current.contains(g.target)&&f(!1)};return document.addEventListener("mousedown",n),()=>{document.removeEventListener("mousedown",n)}},[]);const y=n=>{$(n),f(!1)},v=x.find(n=>n.value===b);return e.jsxs("div",{className:"relative",ref:p,children:[e.jsxs("button",{type:"button",onClick:()=>f(!h),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-left text-[#eaeaea] flex items-center justify-between",children:[e.jsx("span",{className:`${v?"":"text-[#666]"}`,children:v?v.label:k||"Select an option"}),e.jsx("svg",{className:`h-4 w-4 transition-transform ${h?"rotate-180":""}`,fill:"none",stroke:"#b5b5b5",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),h&&e.jsx("div",{className:"absolute z-10 mt-1 w-full max-h-60 overflow-auto rounded-lg border border-[#363636] bg-[#242424] shadow-lg",children:I?e.jsx("div",{className:"flex items-center justify-center p-4",children:e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-[#eaeaea]"})}):x.length===0?e.jsx("div",{className:"p-4 text-[#b5b5b5] text-center",children:"No options available"}):e.jsx("ul",{className:"py-1",children:x.map(n=>e.jsx("li",{onClick:()=>y(n),className:`px-4 py-2 cursor-pointer hover:bg-[#2a2a2a] text-[#eaeaea] ${n.value===b?"bg-[#2a2a2a]":""}`,children:n.label},n.value))})})]})},Ce=({setSidebar:x})=>{const{dispatch:b}=E.useContext(V),$=te({title:d().required(),type:d().required(),industry:d().required(),description:d().required(),deal_size:d().required(),referral_fee:d().required(),payment_method:d().required(),referral_type:d().required(),community:d(),direct_person:d(),additional_notes:d(),expiration_date:d()}).required(),{dispatch:k}=E.useContext(J),[I,h]=u.useState(!1),[f,p]=u.useState(""),[y,v]=u.useState(""),[n,g]=u.useState([]),[A,T]=u.useState([]),[F,O]=u.useState(!1),[z,U]=u.useState("");u.useEffect(()=>{q()},[]);const q=async()=>{try{console.log("Starting to fetch communities...");const l=new P;let t=!1;try{console.log("Trying community_member table for communities"),l.setTable("community_member");const s=await l.callRestAPI({},"GETALL");if(!s.error&&s.list&&s.list.length>0){console.log(`Found ${s.list.length} community members`);const r=[],c=new Set;if(s.list.forEach(o=>{c.has(o.community_id)||(c.add(o.community_id),r.push({id:o.community_id,title:`Community ${o.community_id}`}))}),console.log(`Extracted ${r.length} unique communities`),r.length>0){try{l.setTable("community");const o=await l.callRestAPI({},"GETALL");!o.error&&o.list&&o.list.length>0&&(console.log(`Found ${o.list.length} communities in community table`),r.forEach(a=>{const m=o.list.find(S=>S.id===a.id);m&&m.title&&(a.title=m.title)}))}catch(o){console.log("Error fetching community details:",o)}g(r),t=!0}}else console.log("Community_member table returned no data or had an error")}catch(s){console.log("Community_member approach failed:",s)}if(!t)try{console.log("Trying community search API");const s=await l.callRawAPI("/v1/api/dealmaker/user/community/search",{},"GET");!s.error&&s.list&&s.list.length>0?(console.log(`Found ${s.list.length} communities with search API`),g(s.list),t=!0):console.log("Community search API returned no communities or had an error")}catch(s){console.log("Community search API failed:",s)}if(!t)try{console.log("Trying TreeQL for communities"),l.setTable("community");const s=await l.callRestAPI({},"GETALL");!s.error&&s.list&&s.list.length>0?(console.log(`Found ${s.list.length} communities with TreeQL`),g(s.list),t=!0):console.log("TreeQL returned no communities or had an error")}catch(s){console.log("TreeQL approach failed:",s)}t||(console.log("No communities found, adding default community"),g([{id:1,title:"Default Community"}]))}catch(l){console.error("Error fetching communities:",l),p(l.message||"Failed to load communities"),R(k,l.message),g([{id:1,title:"Default Community"}])}},M=async l=>{try{O(!0),console.log(`Starting to fetch users for community ${l}...`);const t=new P;t.setTable("community_member");const s=await t.callRestAPI({},"GETALL");if(!s.error&&s.list&&s.list.length>0){console.log(`Found ${s.list.length} total community members`);const r=s.list.filter(c=>c.community_id.toString()===l.toString()&&c.is_active===1);if(console.log(`Found ${r.length} members for community ${l}`),r.length>0){const c=r.map(a=>a.user_id);console.log("User IDs in this community:",c);let o=[];try{t.setTable("user");const a=await t.callRestAPI({},"GETALL");!a.error&&a.list&&a.list.length>0&&(o=a.list.filter(m=>c.includes(m.id)),console.log(`Found ${o.length} user details`))}catch(a){console.log("Error fetching user details:",a)}o.length===0?o=r.map(a=>({id:a.user_id,name:a.first_name&&a.last_name?`${a.first_name} ${a.last_name}`:`User ${a.user_id}`,community_id:a.community_id,role:a.role})):o=o.map(a=>{const m=r.find(S=>S.user_id===a.id);return{...a,community_id:m.community_id,role:m.role,name:a.name||(m.first_name&&m.last_name?`${m.first_name} ${m.last_name}`:a.email||`User ${a.id}`)}}),T(o),console.log("Final user list:",o)}else console.log("No members found for this community"),T([])}else console.log("No community members found or API error"),T([])}catch(t){console.error("Failed to fetch users:",t),p(t.message||"Failed to fetch users"),R(k,t.message),T([])}finally{O(!1)}},B=l=>{const t=l.target.value;v(t),U(""),T([]),j("referral_type",t),j("community",""),j("direct_person","")},H=l=>{const t=l.target.value;if(console.log(`Community changed to: ${t}`),!t){console.log("No community ID provided");return}U(t),j("community",t),j("community_id",t),y==="direct"&&(console.log(`Fetching users for community ${t}`),M(t))},{register:_,handleSubmit:Q,setValue:j,watch:W,control:N,formState:{errors:i}}=K({resolver:Z($)}),L=W("referral_type");u.useEffect(()=>{L&&v(L)},[L]);const Y=async l=>{h(!0),p("");try{let t=new P;t.setTable("referral");const s=new Date().toISOString().split("T")[0]+" 00:00:00";let r;switch(l.referral_type){case"open":r="open referral";break;case"community":r="community referral";break;case"direct":r="direct referral";break;default:r="open referral"}console.log("Form data:",l);const c=l.referral_type==="direct"&&l.direct_person?parseInt(l.direct_person,10):0;let o=0;if(l.referral_type==="direct"){const D=A.find(G=>(G.user_id||G.id).toString()===l.direct_person);D&&D.community_id?(o=parseInt(D.community_id,10),console.log(`Using community_id ${o} from selected user`)):l.community&&(o=parseInt(l.community,10))}else l.referral_type==="community"&&l.community&&(o=parseInt(l.community,10));const a=l.industry?parseInt(l.industry,10):null;console.log("Parsed IDs:",{directPersonId:c,communityId:o,industryId:a});const m={user_id:c||1,reposted_from:0,job_title:l.title,title:l.title,description:l.description,description_image:"",pay:l.referral_fee,referred_to_id:c||0,industry_id:a,is_active:1,type:l.type,client_details:l.additional_notes||"",completed_by:null,status:"active",created_at:s,expiration_date:l.expiration_date?new Date(l.expiration_date).toISOString().split("T")[0]+" 00:00:00":new Date(new Date().setFullYear(new Date().getFullYear()+1)).toISOString().split("T")[0]+" 00:00:00",updated_at:s,deal_size:l.deal_size,payment_method:l.payment_method,referral_type:r,additional_notes:l.additional_notes||null,community_id:o};console.log("API data:",m);const S=await t.callRestAPI(m,"POST");S.error?p(S.message||"Failed to add opportunity"):(ee(b,"Opportunity Added Successfully"),console.log("Dispatching REFRESH_DATA action to refresh the table"),b({type:"REFRESH_DATA",payload:{refreshData:!0,timestamp:new Date().getTime()}}),setTimeout(()=>{x(!1)},100)),h(!1)}catch(t){h(!1),console.log("Error",t),p(t.message||"Failed to add opportunity"),R(k,t.message)}};return E.useEffect(()=>{b({type:"SETPATH",payload:{path:"referral"}})},[]),e.jsxs("div",{className:"w-full rounded-lg bg-[#1e1e1e] p-6",children:[e.jsx("style",{children:`
          input[type="date"]::-webkit-calendar-picker-indicator {
            filter: invert(1);
            cursor: pointer;
          }
          input[type="date"]::-webkit-calendar-picker-indicator:hover {
            filter: invert(1) brightness(1.2);
          }
        `}),e.jsx("h2",{className:"mb-6 text-xl font-bold text-[#eaeaea]",children:"Add Opportunity"}),e.jsxs("form",{onSubmit:Q(Y),className:"space-y-4 w-[60vw] mx-auto",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Title"}),e.jsx("input",{type:"text",..._("title"),placeholder:"Enter referral title",className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"}),i.title&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:i.title.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Type of Opportunity"}),e.jsx(w,{name:"type",control:N,render:({field:l})=>e.jsx(C,{options:[{value:"looking_for_service",label:"Looking for Service"},{value:"looking_for_product",label:"Looking for Product"},{value:"looking_for_buyer",label:"Looking for Buyer"},{value:"looking_for_investor",label:"Looking for Investor"},{value:"looking_for_partner",label:"Looking for Partner"}],value:l.value,onChange:t=>{l.onChange(t.value)},placeholder:"Select type"})}),i.type&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:i.type.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Industry"}),e.jsx(w,{name:"industry",control:N,render:({field:l})=>e.jsx(C,{options:[{value:"1",label:"Agriculture & Farming"},{value:"2",label:"Construction"},{value:"3",label:"Education & Training"},{value:"4",label:"Energy & Utilities"},{value:"5",label:"Financial Services"},{value:"6",label:"Government & Public Sector"},{value:"7",label:"Healthcare & Life Sciences"},{value:"8",label:"Hospitality & Tourism"},{value:"9",label:"Information Technology & Software"},{value:"10",label:"Legal Services"},{value:"11",label:"Logistics & Transportation"},{value:"12",label:"Manufacturing"},{value:"13",label:"Marketing & Advertising"},{value:"14",label:"Media & Entertainment"},{value:"15",label:"Non-Profit & Charities"},{value:"16",label:"Professional Services (e.g., consulting, accounting)"},{value:"17",label:"Real Estate & Property Management"},{value:"18",label:"Retail & E-Commerce"},{value:"19",label:"Telecommunications"},{value:"20",label:"Wholesale & Distribution"}],value:l.value,onChange:t=>{l.onChange(t.value)},placeholder:"Select industry"})}),i.industry&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:i.industry.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Description"}),e.jsx("div",{className:"relative",children:e.jsx("textarea",{..._("description"),placeholder:"Write your text here...",rows:4,className:"mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea]"})}),i.description&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:i.description.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Estimated Deal Size"}),e.jsx("input",{type:"text",..._("deal_size"),placeholder:"e.g. $500",className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"}),i.deal_size&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:i.deal_size.message})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Commission Percentage (%)"}),e.jsx("input",{type:"text",..._("referral_fee"),placeholder:"Enter percentage (e.g. 10, 15)",className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"}),i.referral_fee&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:i.referral_fee.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Payment Method"}),e.jsx(w,{name:"payment_method",control:N,render:({field:l})=>e.jsx(C,{options:[{value:"bank",label:"Bank Transfer"},{value:"card",label:"Bank card"}],value:l.value,onChange:t=>{l.onChange(t.value)},placeholder:"Select method"})}),i.payment_method&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:i.payment_method.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Referral Type"}),e.jsx(w,{name:"referral_type",control:N,render:({field:l})=>e.jsx(C,{options:[{value:"open",label:"Open Referral"},{value:"community",label:"Community Referral"},{value:"direct",label:"Direct Referral"}],value:l.value,onChange:t=>{l.onChange(t.value),B({target:{value:t.value}})},placeholder:"Select type"})}),i.referral_type&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:i.referral_type.message})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[(y==="community"||y==="direct")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Select Community"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{name:"community",control:N,render:({field:l})=>e.jsx(C,{options:n.map(t=>{var c,o,a;const s=((c=t.id)==null?void 0:c.value)||t.id||t.community_id;let r=null;return(o=t.title)!=null&&o.value?r=t.title.value:t.title?r=t.title:(a=t.name)!=null&&a.value?r=t.name.value:t.name?r=t.name:r=`Community ${s}`,{value:s.toString(),label:r}}),value:l.value,onChange:t=>{l.onChange(t.value),H({target:{value:t.value}})},placeholder:"Select community",loading:n.length===0})}),n.length<=1&&e.jsx("button",{type:"button",onClick:()=>{console.log("Manual fetch communities triggered"),q()},className:"text-xs text-[#b5b5b5] hover:text-[#eaeaea]",children:"Limited communities found. Click to retry."})]}),i.community&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:i.community.message})]}),y==="direct"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Select Direct Person"}),F?e.jsx("div",{className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-[#eaeaea]"})}):e.jsxs("div",{className:"space-y-2",children:[e.jsx(w,{name:"direct_person",control:N,render:({field:l})=>e.jsx(C,{options:A.map(t=>{const s=t.user_id||t.id;let r=t.name;return r||(r=t.first_name&&t.last_name?`${t.first_name} ${t.last_name}`:t.email||t.username||`User ${s}`),t.role&&(r=`${r} (${t.role})`),{value:s.toString(),label:r,community_id:t.community_id}}),value:l.value,onChange:t=>{l.onChange(t.value);const s=A.find(r=>(r.user_id||r.id).toString()===t.value);s&&j("community_id",s.community_id)},placeholder:"Select person",loading:F})}),A.length===0&&e.jsx("button",{type:"button",onClick:()=>{console.log("Manual fetch users triggered"),M(z)},className:"text-xs text-[#b5b5b5] hover:text-[#eaeaea]",children:"No users found. Select a community with members."})]}),i.direct_person&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:i.direct_person.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Additional Notes"}),e.jsx("textarea",{..._("additional_notes"),placeholder:"Write any additional notes here...",rows:4,className:"mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea]"}),i.additional_notes&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:i.additional_notes.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-[#b5b5b5]",children:"Expiration Date"}),e.jsx("input",{type:"date",..._("expiration_date"),className:"mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"}),i.expiration_date&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:i.expiration_date.message})]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{type:"button",onClick:()=>{x&&x(!1)},className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea]",children:"Cancel"}),e.jsx(le,{type:"submit",loading:I,disabled:I,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Submit Opportunity"})]})]}),f&&e.jsx(X,{message:f})]})};export{Ce as default};
