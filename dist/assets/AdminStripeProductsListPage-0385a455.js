import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as s,d as H}from"./vendor-1c28ea83.js";import{M as K,G as L,A as O,o as q,t as J,a6 as u}from"./index-b3edd152.js";import{u as Q}from"./react-hook-form-eec8b32f.js";import{c as U,a as m}from"./yup-1b5612ec.js";import{P as W}from"./index-7ba88dde.js";import{A as X}from"./index-e2604cb4.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let Y=new K;const w=[{header:"Row",accessor:"row"},{header:"Stripe Id",accessor:"stripe_id"},{header:"Name",accessor:"name"},{header:"Status",accessor:"status",mapping:{0:"Inactive",1:"Active"}},{header:"Action",accessor:""}],Se=()=>{var g,f;const{dispatch:y}=s.useContext(L),{dispatch:v}=s.useContext(O);s.useState("");const[j,N]=s.useState([]),[i,p]=s.useState(10),[x,S]=s.useState(0),[Z,P]=s.useState(0),[c,k]=s.useState(0),[C,A]=s.useState(!1),[R,E]=s.useState(!1),_=H(),D=U({stripe_id:m(),name:m(),status:m()}),{register:l,handleSubmit:I,formState:{errors:h}}=Q({resolver:q(D)}),T=[{key:"",value:"All"},{key:"0",value:"Inactive"},{key:"1",value:"Active"}];function z(t){(async function(){p(t),await n(1,t)})()}function B(){(async function(){await n(c-1>1?c-1:1,i)})()}function F(){(async function(){await n(c+1<=x?c+1:1,i)})()}async function n(t,r,o){try{const a=await Y.getStripeProducts({page:t,limit:r},o),{list:M,total:V,limit:$,num_pages:b,page:d}=a;N(M),p(+$),S(+b),k(+d),P(+V),A(+d>1),E(+d+1<=+b)}catch(a){console.log("ERROR",a),J(v,a.message)}}const G=t=>{const r=u(t.name),o=u(t.status),a=u(t.stripe_id);n(1,i,{name:r,status:o,stripe_id:a})};return s.useEffect(()=>{y({type:"SETPATH",payload:{path:"products"}}),async function(){await n(1,i)}()},[]),e.jsxs(e.Fragment,{children:[e.jsxs("form",{className:"mb-10 rounded bg-white p-5 shadow",onSubmit:I(G),children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Search"}),e.jsxs("div",{className:"filter-form-holder mt-10 flex flex-wrap",children:[e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Stripe Id"}),e.jsx("input",{type:"text",placeholder:"Stripe Id",...l("stripe_id"),className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(g=h.stripe_id)==null?void 0:g.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...l("name"),className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=h.name)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4 w-full pl-2 pr-2 md:w-1/2",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("status"),children:T.map(t=>e.jsx("option",{name:"status",value:t.key,defaultValue:"",children:t.value},t.key))}),e.jsx("p",{className:"text-xs italic text-red-500"})]})]}),e.jsxs("div",{className:"search-buttons pl-2",children:[e.jsx("button",{type:"submit",className:"mr-2 inline-block rounded bg-blue-600 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:"Search"}),e.jsx("button",{type:"reset",onClick:()=>n(1,i),className:"inline-block rounded bg-gray-800 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-gray-900 hover:shadow-lg focus:bg-gray-900 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-gray-900 active:shadow-lg",children:"Reset"})]})]}),e.jsxs("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:[e.jsxs("div",{className:"mb-3 flex w-full justify-between text-center  ",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Products "}),e.jsx(X,{link:"/admin/add-product"})]}),e.jsx("div",{className:"overflow-x-auto border-b border-gray-200 shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:w.map((t,r)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},r))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:j.map((t,r)=>e.jsx("tr",{children:w.map((o,a)=>o.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("button",{className:"mx-1 inline-block rounded-full bg-green-500 px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-green-600 hover:shadow-lg focus:bg-green-600 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-green-700 active:shadow-lg",onClick:()=>{_("/admin/edit-product/"+t.id,{state:t})},children:[" ","Edit"]})},a):o.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:o.mapping[t[o.accessor]]},a):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[o.accessor]},a))},r))})]})})]}),e.jsx(W,{currentPage:c,pageCount:x,pageSize:i,canPreviousPage:C,canNextPage:R,updatePageSize:z,previousPage:B,nextPage:F})]})};export{Se as default};
