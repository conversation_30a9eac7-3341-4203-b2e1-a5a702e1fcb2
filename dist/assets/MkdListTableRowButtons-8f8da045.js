import{j as l}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{L as w}from"./index-b3edd152.js";import"./index-dbfe2d0c.js";import"./index-1805b874.js";import{A as R}from"./index-e2604cb4.js";import{a as $}from"./MkdListTableBindOperations-46ca2ffa.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const tr=({row:t,columns:E,actions:i,actionId:u="id",setDeleteId:e=null})=>l.jsx(l.Fragment,{children:l.jsx("div",{className:"z-3 relative flex h-fit w-fit items-center gap-2",children:l.jsx(w,{children:Object.keys(i).filter(r=>{var m,o,h,p,d,n;return((m=i[r])==null?void 0:m.show)&&((o=i[r])==null?void 0:o.locations)&&((p=(h=i[r])==null?void 0:h.locations)==null?void 0:p.length)&&((n=(d=i[r])==null?void 0:d.locations)==null?void 0:n.includes("buttons"))}).map((r,m)=>{var o,h,p,d,n,b,c,j,x,g,C,a,L,N,v,z,A,B;if((o=i[r])!=null&&o.bind)switch((p=(h=i[r])==null?void 0:h.bind)==null?void 0:p.action){case"hide":if(!$(i[r],t))return l.jsx(w,{children:l.jsxs(R,{title:((d=i[r])==null?void 0:d.children)??r,onClick:()=>{var s,f;["delete"].includes(r)?e&&e(t[u]):(s=i[r])!=null&&s.action&&((f=i[r])==null||f.action([t[u]]))},showPlus:!1,className:`!h-[2rem] ${(n=i[r])!=null&&n.showChildren?"!w-fit !text-sub-500":"!w-[2.0713rem]"}  !border-gray-200 !bg-white`,children:[(b=i[r])!=null&&b.icon?(c=i[r])==null?void 0:c.icon:null,(j=i[r])!=null&&j.showChildren?(x=i[r])!=null&&x.children?(g=i[r])==null?void 0:g.children:r:null]},m)},m)}if(!((C=i[r])!=null&&C.bind))return l.jsx(w,{children:l.jsxs(R,{title:((a=i[r])==null?void 0:a.children)??r,onClick:()=>{var s,f,P;["delete"].includes(r)&&!((s=i[r])!=null&&s.action)?e&&e(t[u]):(f=i[r])!=null&&f.action&&((P=i[r])==null||P.action([t[u]]))},showPlus:!1,className:`!h-[2rem] ${(L=i[r])!=null&&L.showChildren?"!w-fit !text-sub-500":"!w-[2.0713rem]"}  !border-gray-200 !bg-white`,children:[(N=i[r])!=null&&N.icon?(v=i[r])==null?void 0:v.icon:null,(z=i[r])!=null&&z.showChildren?(A=i[r])!=null&&A.children?(B=i[r])==null?void 0:B.children:r:null]},m)},m)})})})});export{tr as default};
