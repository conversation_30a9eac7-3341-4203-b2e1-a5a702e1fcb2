import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as s,d as h}from"./vendor-1c28ea83.js";import{u as b}from"./react-hook-form-eec8b32f.js";import{G as _,A as g,o as S,M as v,s as A,t as j}from"./index-b3edd152.js";import{c as E,a as n}from"./yup-1b5612ec.js";import{M as d}from"./MkdInput-67f7082d.js";import{I as w}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const st=({setSidebar:l})=>{const{dispatch:o}=s.useContext(_),p=E({status:n().required(),user_id:n().required(),community_id:n().required()}).required(),{dispatch:c}=s.useContext(g),[u,i]=s.useState(!1),x=h(),{register:r,handleSubmit:f,setError:C,formState:{errors:a}}=b({resolver:S(p)}),y=async m=>{i(!0);try{let e=new v;e.setTable("community_invite"),(await e.callRestAPI({status:m.status,user_id:m.user_id,community_id:m.community_id},"POST")).error||(A(o,"Added"),x("/admin/community_invite"),l(!1),o({type:"REFRESH_DATA",payload:{refreshData:!0}})),i(!1)}catch(e){i(!1),console.log("Error",e),j(c,e.message)}};return s.useEffect(()=>{o({type:"SETPATH",payload:{path:"community_invite"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Add Community_invite"}),t.jsxs("form",{className:"w-full max-w-lg",onSubmit:f(y),children:[t.jsx(d,{type:"text",page:"add",name:"status",errors:a,label:"Status",placeholder:"Status",register:r,className:""}),t.jsx(d,{type:"text",page:"add",name:"user_id",errors:a,label:"User_id",placeholder:"User_id",register:r,className:""}),t.jsx(d,{type:"text",page:"add",name:"community_id",errors:a,label:"Community_id",placeholder:"Community_id",register:r,className:""}),t.jsx(w,{type:"submit",loading:u,disabled:u,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{st as default};
