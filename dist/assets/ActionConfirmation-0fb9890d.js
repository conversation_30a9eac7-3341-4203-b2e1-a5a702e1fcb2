import{j as o}from"./@react-google-maps/api-211df1ae.js";import{R as g}from"./vendor-1c28ea83.js";import{u as Z}from"./react-hook-form-eec8b32f.js";import{M,G as S,A as ee,o as re,s as A,an as w,a9 as k,aH as C,af as j,aI as v}from"./index-b3edd152.js";import{c as te,a as $}from"./yup-1b5612ec.js";import{M as ie}from"./MkdInput-67f7082d.js";import{I as se}from"./index-632d14e3.js";import"./pdf-lib-623decea.js";import{A as ne}from"./index-e2604cb4.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const _e=({data:i={id:null},options:s={endpoint:null,method:"GET"},onSuccess:t,onClose:I,multiple:O=!1,action:p="",mode:d="create",customMessage:q="",table:m="",input:u="input",disableCancel:R=!1,inputConfirmation:x=!0})=>{var D,b,N;new M;const P=te({...["input","input_create"].includes(d)?{[u]:$().required()}:{confirm:$().required().oneOf([p],`Confirmation must be "${p}"`)}}).required(),{state:{createModel:l,updateModel:c,deleteModel:f},dispatch:n}=g.useContext(S),{state:oe,dispatch:a}=g.useContext(ee),{register:T,handleSubmit:_,setValue:E,watch:G,formState:{errors:B}}=Z({resolver:re(P)}),y=G(),z=async()=>{if(!["create","update","delete","custom"].includes(d))return A(n,"Mode must be create, update, delete or custom",5e3,"error");if(!Array.isArray(i))return A(n,"Data must be an list",5e3,"error");const r=i==null?void 0:i.map(h=>F(d,h));(await Promise.all(r)).some(h=>!h.error)&&t&&t()},F=(r,e)=>{var h;if(["create"].includes(r))return w(n,a,m,e,!1);if(["update"].includes(r))return k(n,a,m,e==null?void 0:e.id,e,!1);if(["delete"].includes(r))return C(n,a,m,e.id,null,!1);if(["custom"].includes(r))return j(n,a,{endpoint:s==null?void 0:s.endpoint,method:"POST",payload:e},(h=v)==null?void 0:h.createModel,!1)},H=async r=>{const e=await k(n,a,m,r==null?void 0:r.id,r);!(e!=null&&e.error)&&t&&t()},K=async r=>{const e=await w(n,a,m,{...r,[u]:y[u]});!(e!=null&&e.error)&&t&&t({[u]:y[u],id:e==null?void 0:e.data})},V=async r=>{if(p==="move")return W(r);const e=await w(n,a,m,r);!(e!=null&&e.error)&&t&&t()},J=async r=>{const e=await C(n,a,m,r.id);!(e!=null&&e.error)&&t&&t()},L=async r=>{const e=await j(n,a,{endpoint:s==null?void 0:s.endpoint,method:s==null?void 0:s.method,payload:(s==null?void 0:s.payload)||r},v.createModel);e&&(e!=null&&e.hasOwnProperty("error"))&&!(e!=null&&e.error)&&t&&t(e)},Q=()=>{const r=y[u];t&&t({[u]:r})},U=r=>{t&&t(r)},W=async r=>{const e=await j(n,a,{endpoint:"/v3/api/custom/Dealmaker/inventory/move",method:"POST",payload:r},v.createModel);!(e!=null&&e.error)&&t&&t(e)},X={create:V,input_create:K,update:H,delete:J,custom:L,manual:U,input:Q},Y=async()=>{if(O)z();else{const r=X[d];return r(i)}};return g.useEffect(()=>{x||E("confirm",p)},[x]),o.jsx("div",{className:"mx-auto flex h-fit flex-col items-center justify-start rounded !font-inter leading-snug tracking-wide",children:o.jsx("form",{className:"flex h-fit w-full flex-col text-start",onSubmit:_(Y,r=>{console.log("ERROR >>",r)}),children:o.jsxs("div",{className:"space-y-5",children:[o.jsx("div",{className:"my-2",children:q?o.jsx("div",{children:q}):o.jsxs("div",{children:["Are you sure you want to ",p," ",i!=null&&i.id&&((D=i==null?void 0:i.id)!=null&&D.length)&&((b=i==null?void 0:i.id)==null?void 0:b.length)>1||(i==null?void 0:i.length)>1?"these":"this"," ",(N=m==null?void 0:m.split("_"))==null?void 0:N.join(" "),"?"]})}),o.jsx("div",{className:`!mb-10 ${x?"":"hidden"}`,children:o.jsx(ie,{type:"text",page:"items",name:["input","input_create"].includes(d)?u:"confirm",errors:B,register:T,label:o.jsxs("div",{className:"font-bold text-black",children:["Type"," ",["input","input_create"].includes(d)?"":`'${p}'`," ","below"]}),className:"grow"})}),o.jsxs("div",{className:"mt-5  flex w-full grow gap-5",children:[R?null:o.jsx(ne,{type:"button",onClick:()=>I(),disabled:(l==null?void 0:l.loading)||(c==null?void 0:c.loading)||(f==null?void 0:f.loading),className:"grow self-end !border-gray-200 !bg-transparent font-bold !text-sub-500",children:"Cancel"}),o.jsx(se,{type:"submit",loading:(l==null?void 0:l.loading)||(c==null?void 0:c.loading)||(f==null?void 0:f.loading),disabled:(l==null?void 0:l.loading)||(c==null?void 0:c.loading)||(f==null?void 0:f.loading),className:`self-end rounded px-4 py-2 font-bold capitalize text-white ${R?"!grow":"!w-1/2"}`,children:p})]})]})})})};export{_e as ActionConfirmation,_e as default};
