import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as l,r as m,d as D}from"./vendor-1c28ea83.js";import{u as $}from"./react-hook-form-eec8b32f.js";import{G as M,A as R,o as T,M as q,s as O,t as G}from"./index-b3edd152.js";import{c as W,a as c,f as N,g as H}from"./yup-1b5612ec.js";import{R as U}from"./react-quill-4ec0fa7c.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./@craftjs/core-a5d68af1.js";const ue=({setSidebar:d})=>{const{dispatch:u}=l.useContext(M),C=W({title:c().required("Title is required"),description:c(),industry_id:N().required("Industry is required").typeError("Industry must be a number"),guidelines:c(),privacy:c().required("Privacy setting is required"),user_id:N().required("User ID is required").typeError("User ID must be a number"),subscription_fee:c(),has_paid:H()}).required(),{dispatch:w}=l.useContext(R),[y,p]=l.useState(!1),[a,h]=m.useState("basic"),[x,A]=m.useState(""),[n,E]=m.useState({who_can_invite:"everyone",activity_visibility:"public",who_can_post:"everyone",view_list:"members_only",who_can_find:"private",who_can_join:"invite_only",who_can_see_content:"members_only",content_moderation:"admin_approval",enable_affiliate:!1,subscription_fee:"0"}),[_,v]=m.useState(""),b=D(),{register:r,handleSubmit:S,setValue:g,formState:{errors:s}}=$({resolver:T(C),defaultValues:{privacy:"public",has_paid:!1,subscription_fee:"0.00"}});l.useEffect(()=>{g("guidelines",x)},[x,g]);const F=async i=>{p(!0),v("");try{let t=new q;t.setTable("community");const j=JSON.stringify({...n,subscription_fee:i.subscription_fee||"0"}),I={title:i.title,description:i.description,industry_id:parseInt(i.industry_id),guidelines:i.guidelines,privacy:i.privacy,user_id:parseInt(i.user_id),has_paid:i.has_paid?1:0,subscription_fee:i.subscription_fee||"0.00",privacy_settings:j},f=await t.callRestAPI(I,"POST");f.error?v(f.message||"Failed to add community"):(O(u,"Community added successfully",5e3,"success"),d?d(!1):b("/admin/community"),u({type:"REFRESH_DATA",payload:{refreshData:!0}})),p(!1)}catch(t){p(!1),console.log("Error",t),v(t.message||"Failed to add community"),G(w,t.message)}};l.useEffect(()=>{u({type:"SETPATH",payload:{path:"community"}})},[]);const k={toolbar:{container:[["bold","italic","underline"],[{list:"bullet"},{list:"ordered"}],["link","image","code-block"],[{align:[]}]]}},P=["bold","italic","underline","list","bullet","link","image","code-block","align"],o=(i,t)=>{E(j=>({...j,[i]:t}))};return e.jsx("div",{className:"add-community-page bg-[#161616] text-white",children:e.jsxs("div",{className:"container mx-auto p-6",children:[e.jsxs("div",{className:"header mb-6",children:[e.jsx("h1",{className:"text-2xl font-semibold mb-2",children:"Add Community"}),e.jsx("p",{className:"text-[#9ca3af] text-sm",children:"Create a new community"})]}),e.jsxs("div",{className:"form-container bg-[#161616]",children:[e.jsxs("div",{className:"tabs mb-6",children:[e.jsx("div",{className:`tab ${a==="basic"?"active":""}`,onClick:()=>h("basic"),children:"Basic Information"}),e.jsx("div",{className:`tab ${a==="description"?"active":""}`,onClick:()=>h("description"),children:"Description & Guidelines"}),e.jsx("div",{className:`tab ${a==="privacy"?"active":""}`,onClick:()=>h("privacy"),children:"Privacy Settings"})]}),e.jsxs("form",{onSubmit:S(F),children:[e.jsx("div",{className:`tab-content ${a==="basic"?"active":""}`,children:e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{children:"Community Details"}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"title",children:"Community Name"}),e.jsx("input",{type:"text",id:"title",...r("title"),placeholder:"Enter community name"}),s.title&&e.jsx("p",{className:"error-message",children:s.title.message})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"industry_id",children:"Industry"}),e.jsxs("select",{id:"industry_id",...r("industry_id"),children:[e.jsx("option",{value:"",children:"Select industry"}),e.jsx("option",{value:"1",children:"Technology"}),e.jsx("option",{value:"2",children:"Finance"}),e.jsx("option",{value:"3",children:"Healthcare"}),e.jsx("option",{value:"4",children:"Education"}),e.jsx("option",{value:"5",children:"Manufacturing"}),e.jsx("option",{value:"6",children:"Retail"}),e.jsx("option",{value:"7",children:"Real Estate"}),e.jsx("option",{value:"8",children:"Other"})]}),s.industry_id&&e.jsx("p",{className:"error-message",children:s.industry_id.message})]}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"privacy",children:"Privacy"}),e.jsxs("select",{id:"privacy",...r("privacy"),children:[e.jsx("option",{value:"public",children:"Public"}),e.jsx("option",{value:"private",children:"Private"})]}),s.privacy&&e.jsx("p",{className:"error-message",children:s.privacy.message})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"user_id",children:"Creator User ID"}),e.jsx("input",{type:"number",id:"user_id",...r("user_id"),placeholder:"Enter user ID"}),s.user_id&&e.jsx("p",{className:"error-message",children:s.user_id.message})]}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"subscription_fee",children:"Monthly Subscription Fee ($)"}),e.jsx("input",{type:"text",id:"subscription_fee",...r("subscription_fee"),placeholder:"0.00"}),s.subscription_fee&&e.jsx("p",{className:"error-message",children:s.subscription_fee.message})]})]}),e.jsx("div",{className:"form-input",children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",...r("has_paid"),className:"mr-2"}),"Has Paid"]})})]})}),e.jsxs("div",{className:`tab-content ${a==="description"?"active":""}`,children:[e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{children:"Description"}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"description",children:"Community Description"}),e.jsx("textarea",{id:"description",...r("description"),placeholder:"Describe your community...",rows:4}),s.description&&e.jsx("p",{className:"error-message",children:s.description.message})]})]}),e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{children:"Guidelines"}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"guidelines",children:"Community Guidelines"}),e.jsx("div",{className:"editor-wrapper",children:e.jsx(U,{theme:"snow",value:x,onChange:A,modules:k,formats:P,placeholder:"Enter your community guidelines here..."})}),s.guidelines&&e.jsx("p",{className:"error-message",children:s.guidelines.message})]})]})]}),e.jsx("div",{className:`tab-content ${a==="privacy"?"active":""}`,children:e.jsxs("div",{className:"form-section",children:[e.jsx("h3",{children:"Privacy & Access Settings"}),e.jsx("div",{className:"form-input",children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:n.enable_affiliate,onChange:i=>o("enable_affiliate",i.target.checked),className:"mr-2"}),"Enable Affiliate Program"]})}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"who_can_join",children:"Who Can Join"}),e.jsxs("select",{id:"who_can_join",value:n.who_can_join,onChange:i=>o("who_can_join",i.target.value),children:[e.jsx("option",{value:"anyone",children:"Anyone"}),e.jsx("option",{value:"invite_only",children:"Invite Only"}),e.jsx("option",{value:"approval",children:"With Approval"})]})]}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"who_can_invite",children:"Who Can Invite"}),e.jsxs("select",{id:"who_can_invite",value:n.who_can_invite,onChange:i=>o("who_can_invite",i.target.value),children:[e.jsx("option",{value:"everyone",children:"Everyone"}),e.jsx("option",{value:"admins_only",children:"Admins Only"}),e.jsx("option",{value:"moderators",children:"Moderators & Admins"})]})]}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"who_can_post",children:"Who Can Post"}),e.jsxs("select",{id:"who_can_post",value:n.who_can_post,onChange:i=>o("who_can_post",i.target.value),children:[e.jsx("option",{value:"everyone",children:"Everyone"}),e.jsx("option",{value:"admins_only",children:"Admins Only"}),e.jsx("option",{value:"moderators",children:"Moderators & Admins"})]})]}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"who_can_see_content",children:"Who Can See Content"}),e.jsxs("select",{id:"who_can_see_content",value:n.who_can_see_content,onChange:i=>o("who_can_see_content",i.target.value),children:[e.jsx("option",{value:"everyone",children:"Everyone"}),e.jsx("option",{value:"members_only",children:"Members Only"})]})]}),e.jsxs("div",{className:"form-input",children:[e.jsx("label",{htmlFor:"content_moderation",children:"Content Moderation"}),e.jsxs("select",{id:"content_moderation",value:n.content_moderation,onChange:i=>o("content_moderation",i.target.value),children:[e.jsx("option",{value:"none",children:"None"}),e.jsx("option",{value:"admin_approval",children:"Admin Approval"}),e.jsx("option",{value:"moderator_approval",children:"Moderator Approval"})]})]})]})}),_&&e.jsx("div",{className:"bg-red-500 bg-opacity-10 border border-red-500 text-red-500 px-4 py-3 rounded mb-4",children:_}),e.jsxs("div",{className:"flex justify-end mt-6",children:[e.jsx("button",{type:"button",onClick:()=>{d?d(!1):b("/admin/community")},className:"cancel-button",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:y,className:"submit-button flex items-center gap-2",children:y?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"animate-spin h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Creating..."]}):"Create Community"})]})]})]})]})})};export{ue as default};
