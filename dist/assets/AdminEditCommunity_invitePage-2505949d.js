import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as s,d as E,f as j}from"./vendor-1c28ea83.js";import{u as I}from"./react-hook-form-eec8b32f.js";import{M as N,A as w,G as A,t as h,S as C,o as T,s as R}from"./index-b3edd152.js";import{c as P,a as p}from"./yup-1b5612ec.js";import{M as f}from"./MkdInput-67f7082d.js";import{I as k}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let a=new N;const nt=o=>{const{dispatch:y}=s.useContext(w),_=P({status:p().required(),user_id:p().required(),community_id:p().required()}).required(),{dispatch:r}=s.useContext(A),[x,m]=s.useState(!1),[g,n]=s.useState(!1),b=E(),{register:d,handleSubmit:v,setError:q,setValue:u,formState:{errors:l}}=I({resolver:T(_)}),i=j();s.useEffect(function(){(async function(){try{n(!0),a.setTable("community_invite");const t=await a.callRestAPI({id:o.activeId?o.activeId:Number(i==null?void 0:i.id)},"GET");t.error||(u("status",t.model.status),u("user_id",t.model.user_id),u("community_id",t.model.community_id),n(!1))}catch(t){n(!1),console.log("error",t),h(y,t.message)}})()},[]);const S=async t=>{m(!0);try{a.setTable("community_invite"),(await a.callRestAPI({id:o.activeId?o.activeId:Number(i==null?void 0:i.id),status:t.status,user_id:t.user_id,community_id:t.community_id},"PUT")).error||(R(r,"Updated"),b("/admin/community_invite"),o.setSidebar(!1),r({type:"REFRESH_DATA",payload:{refreshData:!0}})),m(!1)}catch(c){m(!1),console.log("Error",c),h(y,c.message)}};return s.useEffect(()=>{r({type:"SETPATH",payload:{path:"community_invite"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Edit Community_invite"}),g?e.jsx(C,{}):e.jsxs("form",{className:"w-full max-w-lg",onSubmit:v(S),children:[e.jsx(f,{type:"text",page:"edit",name:"status",errors:l,label:"Status",placeholder:"Status",register:d,className:""}),e.jsx(f,{type:"text",page:"edit",name:"user_id",errors:l,label:"User_id",placeholder:"User_id",register:d,className:""}),e.jsx(f,{type:"text",page:"edit",name:"community_id",errors:l,label:"Community_id",placeholder:"Community_id",register:d,className:""}),e.jsx(k,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{nt as default};
