import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as g,r as k}from"./vendor-1c28ea83.js";import{u as U}from"./react-hook-form-eec8b32f.js";import{M as A,G as P,t as S,o as O,s as p}from"./index-b3edd152.js";import{c as C,a as u}from"./yup-1b5612ec.js";import{I as G}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let f=new A;const de=()=>{var j,N,y,v;const R=C({email:u().email().required(),password:u(),first_name:u().required("First name is required"),last_name:u().required("Last name is required")}).required(),{dispatch:l}=g.useContext(P),[F,q]=k.useState(""),[w,b]=k.useState(!1),{dispatch:I}=g.useContext(P),{register:d,handleSubmit:L,setError:x,setValue:h,formState:{errors:c}}=U({resolver:O(R)});g.useEffect(()=>{I({type:"SETPATH",payload:{path:"settings"}}),async function(){var t,i;try{console.log("Fetching profile data...");const a=await f.callRawAPI("/v1/api/dealmaker/super_admin/lambda/profile",{},"GET");console.log("Profile data received:",a),a&&typeof a=="object"?(h("email",a.model.email||""),h("first_name",a.model.first_name||""),h("last_name",a.model.last_name||""),q(a.model.email||""),console.log("Form values set with profile data")):console.error("Invalid profile data format:",a)}catch(a){console.log("Error fetching profile:",a),S(l,(i=(t=a.response)==null?void 0:t.data)!=null&&i.message?a.response.data.message:a.message)}}()},[]);const T=async t=>{var i,a,_,E;try{b(!0);let s=!1;if(t.password&&t.password.length>0){const o=await f.updatePassword(t.password);if(!o.error)p(l,"Password Updated",2e3),s=!0;else if(o.validation){const n=Object.keys(o.validation);for(let r=0;r<n.length;r++){const m=n[r];x(m,{type:"manual",message:o.validation[m]})}}}if(F!==t.email){const o=await f.updateEmail(t.email);if(!o.error)p(l,"Email Updated",2e3),s=!0;else if(o.validation){const n=Object.keys(o.validation);for(let r=0;r<n.length;r++){const m=n[r];x(m,{type:"manual",message:o.validation[m]})}}}(await f.callRawAPI("/v1/api/dealmaker/super_admin/lambda/preference",{first_name:t.first_name,last_name:t.last_name},"POST")).error||(p(l,"Profile Updated",2e3),s=!0),s||p(l,"No changes to update",2e3),b(!1)}catch(s){b(!1),console.log("Error",s),x("email",{type:"manual",message:(a=(i=s.response)==null?void 0:i.data)!=null&&a.message?s.response.data.message:s.message}),S(l,(E=(_=s.response)==null?void 0:_.data)!=null&&E.message?s.response.data.message:s.message)}};return e.jsx(e.Fragment,{children:e.jsx("main",{className:"bg-[#1e1e1e] min-h-screen pt-16",children:e.jsx("div",{className:"p-[3rem]",children:e.jsxs("div",{className:"bg-[#161616] shadow rounded p-[2rem] border border-solid border-[#363636]",children:[e.jsx("h4",{className:"text-2xl font-medium mb-7 text-[#eaeaea]",children:"Edit Profile"}),e.jsxs("form",{onSubmit:L(T),className:"max-w-lg",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-[#b5b5b5] text-sm font-bold mb-2",children:"Email"}),e.jsx("input",{className:"shadow appearance-none border border-[#363636] rounded w-full py-2 px-3 text-[#eaeaea] bg-[#242424] leading-tight focus:outline-none focus:shadow-outline",id:"email",type:"email",placeholder:"Email",name:"email",...d("email")}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(j=c.email)==null?void 0:j.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-[#b5b5b5] text-sm font-bold mb-2",children:"First Name"}),e.jsx("input",{className:"shadow appearance-none border border-[#363636] rounded w-full py-2 px-3 text-[#eaeaea] bg-[#242424] leading-tight focus:outline-none focus:shadow-outline",id:"first_name",type:"text",placeholder:"First name",name:"first_name",...d("first_name")}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(N=c.first_name)==null?void 0:N.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-[#b5b5b5] text-sm font-bold mb-2",children:"Last Name"}),e.jsx("input",{className:"shadow appearance-none border border-[#363636] rounded w-full py-2 px-3 text-[#eaeaea] bg-[#242424] leading-tight focus:outline-none focus:shadow-outline",id:"last_name",type:"text",placeholder:"Last name",name:"last_name",...d("last_name")}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(y=c.last_name)==null?void 0:y.message})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-[#b5b5b5] text-sm font-bold mb-2",children:"Password"}),e.jsx("input",{...d("password"),name:"password",className:"shadow appearance-none border border-[#363636] rounded w-full py-2 px-3 text-[#eaeaea] bg-[#242424] leading-tight focus:outline-none focus:shadow-outline",id:"password",type:"password",placeholder:"******************"}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(v=c.password)==null?void 0:v.message})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(G,{className:"bg-[#2e7d32] hover:bg-[#1b5e20] disabled:bg-[#4c8c4a] disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mt-[2rem]",type:"submit",loading:w,disabled:w,children:"Update Profile"})})]})]})})})})};export{de as default};
