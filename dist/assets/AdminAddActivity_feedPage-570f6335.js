import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as s,d as v}from"./vendor-1c28ea83.js";import{u as _}from"./react-hook-form-eec8b32f.js";import{G as h,A as b,o as g,M as E,s as A,t as S}from"./index-b3edd152.js";import{c as j,a as m}from"./yup-1b5612ec.js";import{M as l}from"./MkdInput-67f7082d.js";import{I as w}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const se=({setSidebar:n})=>{const{dispatch:r}=s.useContext(h),c=j({user_id:m().required(),event_details:m().required(),event_type:m().required()}).required(),{dispatch:u}=s.useContext(b),[p,a]=s.useState(!1),f=v(),{register:o,handleSubmit:x,setError:N,formState:{errors:i}}=_({resolver:g(c)}),y=async d=>{a(!0);try{let t=new E;t.setTable("activity_feed"),(await t.callRestAPI({user_id:d.user_id,event_details:d.event_details,event_type:d.event_type},"POST")).error||(A(r,"Added"),f("/admin/activity_feed"),n(!1),r({type:"REFRESH_DATA",payload:{refreshData:!0}})),a(!1)}catch(t){a(!1),console.log("Error",t),S(u,t.message)}};return s.useEffect(()=>{r({type:"SETPATH",payload:{path:"activity_feed"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Activity_feed"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:x(y),children:[e.jsx(l,{type:"text",page:"add",name:"user_id",errors:i,label:"User_id",placeholder:"User_id",register:o,className:""}),e.jsx(l,{type:"text",page:"add",name:"event_details",errors:i,label:"Event_details",placeholder:"Event_details",register:o,className:""}),e.jsx(l,{type:"text",page:"add",name:"event_type",errors:i,label:"Event_type",placeholder:"Event_type",register:o,className:""}),e.jsx(w,{type:"submit",loading:p,disabled:p,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{se as default};
