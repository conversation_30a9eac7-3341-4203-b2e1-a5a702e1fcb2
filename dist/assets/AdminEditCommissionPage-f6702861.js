import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as s,d as j,f as v}from"./vendor-1c28ea83.js";import{u as w}from"./react-hook-form-eec8b32f.js";import{M as N,A,G as C,t as x,S as T,o as _,s as R}from"./index-b3edd152.js";import{c as D,a as h}from"./yup-1b5612ec.js";import{M as g}from"./MkdInput-67f7082d.js";import{I as P}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let r=new N;const ne=i=>{const{dispatch:l}=s.useContext(A),b=D({commission_rate:h().required(),description:h().required()}).required(),{dispatch:a}=s.useContext(C),[d,m]=s.useState(!1),[y,n]=s.useState(!1),E=j(),{register:p,handleSubmit:S,setError:k,setValue:u,formState:{errors:f}}=w({resolver:_(b)}),o=v();s.useEffect(function(){(async function(){try{n(!0),r.setTable("commission");const e=await r.callRestAPI({id:i.activeId?i.activeId:Number(o==null?void 0:o.id)},"GET");e.error||(u("commission_rate",e.model.commission_rate),u("description",e.model.description),n(!1))}catch(e){n(!1),console.log("error",e),x(l,e.message)}})()},[]);const I=async e=>{m(!0);try{r.setTable("commission"),(await r.callRestAPI({id:i.activeId?i.activeId:Number(o==null?void 0:o.id),commission_rate:e.commission_rate,description:e.description},"PUT")).error||(R(a,"Updated"),E("/admin/commission"),i.setSidebar(!1),a({type:"REFRESH_DATA",payload:{refreshData:!0}})),m(!1)}catch(c){m(!1),console.log("Error",c),x(l,c.message)}};return s.useEffect(()=>{a({type:"SETPATH",payload:{path:"commission"}})},[]),t.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Commission"}),y?t.jsx(T,{}):t.jsxs("form",{className:"w-full max-w-lg",onSubmit:S(I),children:[t.jsx(g,{type:"text",page:"edit",name:"commission_rate",errors:f,label:"Commission_rate",placeholder:"Commission_rate",register:p,className:""}),t.jsx(g,{type:"text",page:"edit",name:"description",errors:f,label:"Description",placeholder:"Description",register:p,className:""}),t.jsx(P,{type:"submit",loading:d,disabled:d,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ne as default};
