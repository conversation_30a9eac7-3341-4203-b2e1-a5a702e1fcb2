import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as s,d as N,f as j}from"./vendor-1c28ea83.js";import{u as v}from"./react-hook-form-eec8b32f.js";import{M as w,A,G as T,t as h,S as R,o as D,s as P}from"./index-b3edd152.js";import{c as k,a as p}from"./yup-1b5612ec.js";import{M as f}from"./MkdInput-67f7082d.js";import{I as C}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */let r=new w;const mt=o=>{const{dispatch:g}=s.useContext(A),b=k({status:p().required(),name:p().required(),description:p()}).required(),{dispatch:i}=s.useContext(T),[x,n]=s.useState(!1),[S,m]=s.useState(!1),y=N(),{register:l,handleSubmit:E,setError:L,setValue:c,formState:{errors:d}}=v({resolver:D(b)}),a=j();s.useEffect(function(){(async function(){try{m(!0),r.setTable("integration");const t=await r.callRestAPI({id:o.activeId?o.activeId:Number(a==null?void 0:a.id)},"GET");t.error||(c("status",t.model.status),c("name",t.model.name),c("description",t.model.description),m(!1))}catch(t){m(!1),console.log("error",t),h(g,t.message)}})()},[]);const I=async t=>{n(!0);try{r.setTable("integration"),(await r.callRestAPI({id:o.activeId?o.activeId:Number(a==null?void 0:a.id),status:t.status,name:t.name,description:t.description},"PUT")).error||(P(i,"Updated"),y("/admin/integration"),o.setSidebar(!1),i({type:"REFRESH_DATA",payload:{refreshData:!0}})),n(!1)}catch(u){n(!1),console.log("Error",u),h(g,u.message)}};return s.useEffect(()=>{i({type:"SETPATH",payload:{path:"integration"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Edit Integration"}),S?e.jsx(R,{}):e.jsxs("form",{className:"w-full max-w-lg",onSubmit:E(I),children:[e.jsx(f,{type:"text",page:"edit",name:"status",errors:d,label:"Status",placeholder:"Status",register:l,className:""}),e.jsx(f,{type:"text",page:"edit",name:"name",errors:d,label:"Name",placeholder:"Name",register:l,className:""}),e.jsx(f,{type:"text",page:"edit",name:"description",errors:d,label:"Description",placeholder:"Description",register:l,className:""}),e.jsx(C,{type:"submit",loading:x,disabled:x,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{mt as default};
