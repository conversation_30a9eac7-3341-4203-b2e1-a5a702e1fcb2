import{j as e}from"./@react-google-maps/api-211df1ae.js";import{d as h,u as v,r}from"./vendor-1c28ea83.js";import{M as g}from"./index-b3edd152.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const W=()=>{const o=h(),m=v(),{formData:l}=m.state||{},[i,u]=r.useState({...l,guidelines:"",whoCanFind:"everyone",whoCanJoin:"anyone",enableAffiliate:!1,subscriptionFee:"0.00"});console.log("formData",i);const[s,n]=r.useState({whoCanFind:"everyone",whoCanJoin:"anyone",enableAffiliate:!1,subscriptionFee:"0.00"});console.log("privacySettings",s);const p=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:"Privacy Settings"}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Who Can Find the Community?"}),e.jsxs("select",{value:s.whoCanFind,onChange:a=>n(t=>({...t,whoCanFind:a.target.value})),className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"everyone",children:"Everyone"}),e.jsx("option",{value:"invite-only",children:"Invite Only"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Who Can Join the Group?"}),e.jsxs("select",{value:s.whoCanJoin,onChange:a=>n(t=>({...t,whoCanJoin:a.target.value})),className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"anyone",children:"Anyone"}),e.jsx("option",{value:"approval-required",children:"Approval Required"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Enable Affiliate Program"}),e.jsxs("select",{value:s.enableAffiliate?"yes":"no",onChange:a=>n(t=>({...t,enableAffiliate:a.target.value==="yes"})),className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]",children:[e.jsx("option",{value:"no",children:"No"}),e.jsx("option",{value:"yes",children:"Yes"})]}),s.enableAffiliate&&e.jsx("p",{className:"mt-2 text-xs text-[#7dd87d]",children:"If user selects yes, people can invite members to this community and will get 10% of their subscription fee."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Monthly Subscription Fee"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-4 top-1/2 -translate-y-1/2 text-[#eaeaea]",children:"$"}),e.jsx("input",{type:"number",min:"0",step:"0.01",value:s.subscriptionFee,onChange:a=>n(t=>({...t,subscriptionFee:a.target.value})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] pl-8 pr-4 text-[#eaeaea]"})]})]})]}),[d,c]=r.useState(""),b=async()=>{try{const t=await new g().CreateCommunity({title:{value:i.title},description:{value:i.description},industry_description:{value:i.industry_description},guidelines:{value:i.guidelines},privacy:{value:i.whoCanJoin==="approval-required"?"private":"public"},whoCanFind:{value:s.whoCanFind},whoCanJoin:{value:s.whoCanJoin},enableAffiliate:{value:s.enableAffiliate},subscriptionFee:{value:s.subscriptionFee},created_at:{value:new Date().toISOString()}});t.error?c(t.message):o(`/member/communities/${t.model.id.value}`)}catch(a){c(a.message)}},x=()=>{o("/member/communities/create/step2",{state:{formData:l}})};return e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-4 md:p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:"Community Guidelines & Privacy"}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Step 3 of 3"})]}),d&&e.jsx("div",{className:"mb-4 rounded-lg bg-red-100 p-4 text-red-700",children:d}),e.jsx("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-[#eaeaea]",children:"Community Guidelines"}),e.jsx("textarea",{value:i.guidelines,onChange:a=>u({...i,guidelines:a.target.value}),className:"mt-1 block w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-2.5 text-[#eaeaea]",rows:6,placeholder:"Enter community guidelines...",required:!0})]}),p(),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{onClick:x,className:"rounded-lg border border-[#363636] px-4 py-2 text-[#eaeaea]",children:"Back"}),e.jsx("button",{onClick:b,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea]",children:"Create Community"})]})]})})]})};export{W as default};
