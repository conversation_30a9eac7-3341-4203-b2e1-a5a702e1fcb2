import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as i,d as y}from"./vendor-1c28ea83.js";import{u as b}from"./react-hook-form-eec8b32f.js";import{G as g,A as k,o as S,M as j,s as N,t as T}from"./index-b3edd152.js";import{c as E,a as o}from"./yup-1b5612ec.js";import{M as r}from"./MkdInput-67f7082d.js";import{I as A}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const ae=({setSidebar:n})=>{const{dispatch:p}=i.useContext(g),c=E({user_id:o().required(),token:o().required(),code:o().required(),type:o().required(),data:o(),status:o().required(),expired_at:o()}).required(),{dispatch:u}=i.useContext(k),[m,l]=i.useState(!1),x=y(),{register:t,handleSubmit:h,setError:w,formState:{errors:a}}=b({resolver:S(c)}),f=async s=>{l(!0);try{let d=new j;d.setTable("tokens"),(await d.callRestAPI({user_id:s.user_id,token:s.token,code:s.code,type:s.type,data:s.data,status:s.status,expired_at:s.expired_at},"POST")).error||(N(p,"Added"),x("/admin/tokens"),n(!1),p({type:"REFRESH_DATA",payload:{refreshData:!0}})),l(!1)}catch(d){l(!1),console.log("Error",d),T(u,d.message)}};return i.useEffect(()=>{p({type:"SETPATH",payload:{path:"tokens"}})},[]),e.jsxs("div",{className:"shadow-md rounded mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Tokens"}),e.jsxs("form",{className:"w-full max-w-lg",onSubmit:h(f),children:[e.jsx(r,{type:"text",page:"add",name:"user_id",errors:a,label:"User_id",placeholder:"User_id",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"token",errors:a,label:"Token",placeholder:"Token",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"code",errors:a,label:"Code",placeholder:"Code",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"type",errors:a,label:"Type",placeholder:"Type",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"data",errors:a,label:"Data",placeholder:"Data",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"status",errors:a,label:"Status",placeholder:"Status",register:t,className:""}),e.jsx(r,{type:"text",page:"add",name:"expired_at",errors:a,label:"Expired_at",placeholder:"Expired_at",register:t,className:""}),e.jsx(A,{type:"submit",loading:m,disabled:m,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ae as default};
